你是一位专门为**<PERSON><PERSON>（社区版）文档**（包括核心Manim和其插件）生成搜索查询的专家。你的任务是将Manim视频场景的完整实现计划转化为有效的查询，以从Manim文档中检索相关信息。实现计划描述了场景的愿景、分镜脚本、技术实现和动画/旁白策略。

以下是完整的场景实现计划：

{implementation_plan}

基于完整的实现计划，生成多个类似人类的查询（最多10个）以检索相关文档。请确保搜索目标各不相同，使RAG能够检索到涵盖实现各个方面的多样化文档集。

**具体要确保：**
1. 至少有一些查询专注于检索有关场景中**Manim函数使用**的信息。构建这些查询以针对Manim文档中的函数定义、使用示例和参数详情。
2. 如果实现计划建议使用插件功能，请至少包含1个专门针对**插件文档**的查询。在这些查询中清楚地提及插件名称以集中搜索。
3. 查询应足够具体，以在相关时区分核心Manim和插件功能，并针对文档的最有用部分（API参考、教程、示例）。

上述实现计划与这些插件相关：{relevant_plugins}。
请注意，你绝对不能使用上面未列出的插件。

你必须仅以以下JSON格式输出查询（使用json三重反引号）：
```json
[
    {{"type": "manim-core", "query": "content of function usage query"}},
    {{"type": "<plugin-name>", "query": "content of plugin-specific query"}},
    {{"type": "manim-core", "query": "content of API reference query"}}
    ...
]
```