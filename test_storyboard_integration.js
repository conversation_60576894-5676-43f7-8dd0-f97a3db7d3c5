const VideoGenerator = require('./generatorVideo/index.js');

// 测试故事板生成的完整集成
async function testStoryboardIntegration() {
  console.log('=== 测试故事板生成集成 ===\n');

  // 创建VideoGenerator实例
  const videoGenerator = new VideoGenerator({
    useRag: false,
  });

  const topic = '勾股定理';
  const description = '通过动画演示勾股定理的概念、证明和应用';
  const sceneNum = 1;
  const sceneContent = `
**场景标题**：勾股定理的发现
**核心内容**：介绍勾股定理的基本概念，展示直角三角形的三边关系，通过具体的数值例子让观众理解a²+b²=c²的含义
**教学目标**：让观众理解勾股定理的基本表述和几何意义，能够识别直角三角形并知道三边的关系
**视觉元素**：直角三角形、边长标注、公式文本、坐标系
**动画流程**：先显示坐标系，然后绘制直角三角形，标注边长，最后显示公式
  `;
  const sceneOutline = `
**场景一：勾股定理的发现**
* 核心内容：介绍勾股定理的基本概念，展示直角三角形的三边关系
* 教学目标：让观众理解勾股定理的基本表述和几何意义
* 视觉方向提示：绘制一个标准的直角三角形，标注三边长度

**场景二：勾股定理的证明**
* 核心内容：通过几何证明方法来证明勾股定理
* 教学目标：让观众理解勾股定理的数学严谨性
* 视觉方向提示：构建大正方形，展示面积分解过程

**场景三：勾股定理的应用**
* 核心内容：展示勾股定理在实际生活中的应用
* 教学目标：让观众掌握如何在实际问题中应用勾股定理
* 视觉方向提示：展示实际场景的数学建模过程
  `;

  try {
    console.log('1. 测试故事板生成...');
    const storyboard = await videoGenerator.generateStoryboard({
      topic,
      description,
      sceneNum,
      sceneContent,
      sceneOutline,
    });

    console.log('\n2. 生成的故事板内容:');
    console.log('---');
    console.log(storyboard.substring(0, 500) + '...');
    console.log('---\n');

    // 检查故事板是否包含关键元素
    const checks = [
      { name: '包含场景编号', test: storyboard.includes('场景 1') },
      { name: '包含输出格式标签', test: storyboard.includes('<SCENE_VISION_STORYBOARD_PLAN>') },
      { name: '包含开场设定', test: storyboard.includes('开场设定') },
      { name: '包含核心元素', test: storyboard.includes('核心元素清单') },
      { name: '包含动画时间轴', test: storyboard.includes('动画时间轴') },
      { name: '包含技术提示', test: storyboard.includes('技术实现提示') },
      { name: '包含坐标范围', test: storyboard.includes('[-7.1, 7.1]') },
      { name: '包含主题信息', test: storyboard.includes(topic) },
    ];

    console.log('3. 故事板内容检查:');
    checks.forEach(check => {
      console.log(`${check.test ? '✅' : '❌'} ${check.name}`);
    });

    const passedChecks = checks.filter(check => check.test).length;
    console.log(`\n通过检查: ${passedChecks}/${checks.length}`);

    if (passedChecks === checks.length) {
      console.log('🎉 故事板生成测试通过！');
    } else {
      console.log('⚠️  故事板生成有问题，请检查。');
    }

  } catch (error) {
    console.error('测试过程中发生错误:', error.message);
  }

  console.log('\n=== 测试完成 ===');
}

// 运行测试
if (require.main === module) {
  testStoryboardIntegration();
}

module.exports = {
  testStoryboardIntegration,
};
