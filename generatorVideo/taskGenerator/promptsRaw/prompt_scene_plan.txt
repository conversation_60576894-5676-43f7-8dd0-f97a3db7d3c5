You are an expert in educational video production, instructional design, and {topic}. Please design a high-quality video to provide in-depth explanation on {topic}.

**Video Overview:**

Topic: {topic}
Description: {description}

**Scene Breakdown:**

Plan individual scenes. For each scene please provide the following:

*   **Scene Title:** Short, descriptive title (2-5 words).
*   **Scene Purpose:** Learning objective of this scene. How does it connect to previous scenes?
*   **Scene Description:** Detailed description of scene content.
*   **Scene Layout:** Detailedly describe the spatial layout concept. Consider safe area margins and minimum spacing between objects.

Please generate the scene plan for the video in the following format:

```xml
<SCENE_OUTLINE>
    <SCENE_1>
    Scene Title: [Title]
    Scene Purpose: [Learning objective, connection to previous scene]
    Scene Description: [Brief content description]
    Scene Layout: [Spatial layout concept, consider safe area and spacing]
    </SCENE_1>

    <SCENE_2>
    ...
    </SCENE_2>
...
</SCENE_OUTLINE>
```

**Spatial Constraints:**
*   **Safe area margins:** 0.5 units on all sides from the scene edges. *All objects must be positioned within these margins.*
*   **Minimum spacing:** 0.3 units between any two Manim objects (measured edge to edge). *Ensure adequate spacing to prevent overlaps and maintain visual clarity.*

Requirements:
1. Scenes must build progressively, starting from foundational concepts and advancing to more complex ideas to ensure a logical flow of understanding for the viewer. Each scene should naturally follow from the previous one, creating a cohesive learning narrative. Start with simpler scene layouts and progressively increase complexity in later scenes.
2. The total number of scenes should be between 3 and 4.
3. Learning objectives should be distributed evenly across the scenes.
4. The total video duration must be under 15 minutes.
5. It is essential to use the exact output format, tags, and headers as specified in the prompt.
6. Maintain consistent formatting throughout the entire scene plan.
7. **No External Assets:** Do not import any external files (images, audio, video). *Use only Manim built-in elements and procedural generation.
8. **Focus on in-depth explanation of the theorem. Do not include any promotional elements (like YouTube channel promotion, subscribe messages, or external resources) or quiz sessions. Detailed example questions are acceptable and encouraged.**

Note: High-level plan. Detailed scene specifications will be generated later, ensuring adherence to safe area margins and minimum spacing. The spatial constraints defined above will be strictly enforced in subsequent planning stages.