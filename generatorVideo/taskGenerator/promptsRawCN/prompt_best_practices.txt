#使用manim生成教育视频的最佳实践

1. 在有意义的时候指定相对于其他对象的位置。
   * 例如，如果你想为一个几何对象放置标签。
2. 对象的颜色应该与黑色背景不同。
3. 保持屏幕上的文字简洁。
   * 屏幕上的元素应该专注于展示概念、示例和视觉效果。标签和说明性文字仍然被鼓励。
   * 对于解释和观察，请选择叙述而不是屏幕上的文本。
   * 你仍然应该在屏幕上完整地显示计算和算法。
   * 对于示例和练习问题，显示更多文本是合理的，特别是关键语句。
   * 较长的文本应该显示较小以适应屏幕。
4. 控制：控制对象出现的时间：
   * `add`具有即时效果，最适合用于场景的初始设置。
   * 动画最适合用于旁白。
   * 确保动画有意义。如果一个对象已经在屏幕上了，那么将它淡入或重新创建它是没有意义的。
5. 想显示数学信息时，可以使用TeX或MathTeX，包括符号和公式。
