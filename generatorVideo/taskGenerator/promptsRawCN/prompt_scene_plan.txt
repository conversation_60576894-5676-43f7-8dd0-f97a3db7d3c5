你是一位教育视频制作、教学设计和{topic}领域的专家。请设计一个高质量的视频，对{topic}提供深入解释。

**视频概述：**

主题：{topic}
描述：{description}

**场景细分：**

规划各个场景。对于每个场景，请提供以下内容：

* **场景标题：**简短、描述性的标题（2-5个词）。
* **场景目的：**此场景的学习目标。它如何与前面的场景相连接？
* **场景描述：**场景内容的详细描述，场景里有什么元素，元素的形象以及动画描述。
* **场景布局：**详细描述空间布局概念。重点各个元素在画布上的位置。

请按照以下格式生成视频的场景计划：

```xml
<SCENE_OUTLINE>
    <SCENE_1>
    场景标题: [简单描述场景主题]
    场景目的: [设计该场景要达到的目的]
    场景描述: [详细描述场景的具体实现脚本]
    场景布局: [详细描述空间布局概念。重点各个元素在画布上的位置]
    </SCENE_1>

    <SCENE_2>
    ...
    </SCENE_2>
...
</SCENE_OUTLINE>
```

要求：

1. 场景必须逐步构建，从基础概念开始，逐渐发展到更复杂的想法，以确保观众理解的逻辑流程。每个场景应自然地从前一个场景延续，创建一个连贯的学习叙述。从简单的场景布局开始，在后续场景中逐渐增加复杂性。
2. 场景总数应在3到4个之间。
3. 学习目标应均匀分布在各个场景中。
4. 视频总时长必须在15分钟以内。
5. 必须使用提示中指定的确切输出格式、标签和标题。
6. 在整个场景计划中保持一致的格式。
7. **无外部资产：**不要导入任何外部文件（图像、音频、视频）。*仅使用Manim内置元素和程序生成。
8. 专注于对定理的深入解释。不要包含任何宣传元素（如YouTube频道推广、订阅信息或外部资源）或测验环节。详细的示例问题是可接受且鼓励的。
9. 如果是证明动画，先确定常见的证明方法。如果3个场景能完成，就不需要4个场景，不用勉强加入。
注意：这是高层次计划。详细的场景规格将在后期生成，确保遵守安全区域边距和最小间距。上面定义的空间约束将在后续规划阶段严格执行。只考虑manim能够实现的方案。