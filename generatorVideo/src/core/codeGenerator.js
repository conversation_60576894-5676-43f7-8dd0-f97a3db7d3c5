const fs = require('fs');
const path = require('path');
const { Image } = require('image-js');
const glob = require('glob');

// 修改导入方式为 require
const { prepareTextInputs } = require('../../mllmTools/utils.js');
const {
  getPromptCodeGeneration,
  getPromptFixError,
  // getPromptVisualFixError,
  getBannedReasonings,
  getPromptRagQueryGenerationFixError,
  getPromptVisualSelfReflection,
  getPromptContextLearningCode,
  getPromptRagQueryGenerationCode,
} = require('../../taskGenerator/index.js');
const { RAGVectorStore } = require('../rag/vectorStore.js');

// 修改导出方式
class CodeGenerator {
  /**
   * 用于生成和管理Manim代码的类
   *
   * @param {Object} options - 配置选项
   * @param {Object} options.sceneModel - 用于场景生成的模型
   * @param {Object} options.helperModel - 用于辅助任务的模型
   * @param {string} options.outputDir - 输出文件目录，默认为"output"
   * @param {boolean} options.printResponse - 是否打印模型响应，默认为false
   * @param {boolean} options.useRag - 是否使用RAG，默认为false
   * @param {boolean} options.useContextLearning - 是否使用上下文学习，默认为false
   * @param {string} options.contextLearningPath - 上下文学习示例路径，默认为"data/context_learning"
   * @param {string} options.chromaDbPath - ChromaDB路径，默认为"rag/chroma_db"
   * @param {string} options.manimDocsPath - Manim文档路径，默认为"rag/manim_docs"
   * @param {string} options.embeddingModel - 嵌入模型名称，默认为"azure/text-embedding-3-large"
   * @param {boolean} options.useVisualFixCode - 是否使用视觉代码修复，默认为false
   * @param {boolean} options.useLangfuse - 是否使用Langfuse日志记录，默认为true
   * @param {string} options.sessionId - 会话标识符，默认为null
   */
  constructor({
    sceneModel,
    helperModel,
    outputDir,
    printResponse = false,
    useRag = false,
    useContextLearning = false,
    contextLearningPath = 'data/context_learning',
    chromaDbPath = 'rag/chroma_db',
    manimDocsPath = 'rag/manim_docs',
    embeddingModel = 'text-embedding-v3',
    useVisualFixCode = false,
    useLangfuse = true,
    sessionId = null,
  } = {}) {
    this.sceneModel = sceneModel;
    this.helperModel = helperModel;
    this.outputDir = outputDir;
    this.printResponse = printResponse;
    this.useRag = useRag;
    this.useContextLearning = useContextLearning;
    this.contextLearningPath = contextLearningPath;
    this.contextExamples = useContextLearning
      ? this._loadContextExamples()
      : null;
    this.manimDocsPath = manimDocsPath;

    this.useVisualFixCode = useVisualFixCode;
    this.bannedReasonings = getBannedReasonings();
    this.sessionId = sessionId; // 使用从VideoGenerator传递的sessionId

    if (useRag) {
      this.vectorStore = new RAGVectorStore({
        chromaDbPath,
        manimDocsPath,
        embeddingModel,
        sessionId: this.sessionId,
        useLangfuse: useLangfuse,
      });
    } else {
      this.vectorStore = null;
    }
  }

  /**
   * 从指定目录加载所有上下文学习示例
   *
   * @returns {string} 格式化的上下文学习示例，如果没有找到示例则返回null
   */
  _loadContextExamples() {
    const examples = [];
    const files = glob.sync(`${this.contextLearningPath}/**/*.py`, {
      recursive: true,
    });

    for (const exampleFile of files) {
      const content = fs.readFileSync(exampleFile, 'utf-8');
      examples.push(
        `# Example from ${path.basename(exampleFile)}\n${content}\n`
      );
    }

    // 使用getPromptContextLearningCode而不是_prompt_context_learning格式化示例
    if (examples.length > 0) {
      const formattedExamples = getPromptContextLearningCode({
        examples: examples.join('\n'),
      });
      return formattedExamples;
    }
    return null;
  }

  /**
   * 从实现计划生成RAG查询
   *
   * @param {Object} options - 参数选项
   * @param {string} options.implementation - 实现计划文本
   * @param {string} options.topic - 场景主题，默认为null
   * @param {number} options.sceneNumber - 场景编号，默认为null
   * @param {string} options.sessionId - 会话标识符，默认为null
   * @param {Array} options.relevantPlugins - 相关插件列表，默认为空数组
   * @returns {Array} 生成的RAG查询列表
   */
  async _generateRagQueriesCode({
    implementation,
    topic = null,
    sceneNumber = null,
    relevantPlugins = [],
  }) {
    // // 为此场景创建缓存键
    // const cacheKey = `${topic}_scene${sceneNumber}`;

    // // 检查是否已有此场景的缓存文件
    // const cacheDir = path.join(
    //   this.outputDir,
    //   topic.toLowerCase().replace(/[^a-z0-9_]+/g, "_"),
    //   `scene${sceneNumber}`,
    //   "rag_cache"
    // );
    // fs.mkdirSync(cacheDir, { recursive: true });
    // const cacheFile = path.join(cacheDir, "rag_queries_code.json");

    // // 如果缓存文件存在，加载并返回缓存的查询
    // if (fs.existsSync(cacheFile)) {
    //   const cachedQueries = JSON.parse(fs.readFileSync(cacheFile, "utf-8"));
    //   console.log(`Using cached RAG queries for ${cacheKey}`);
    //   return cachedQueries;
    // }

    // 如果没有缓存，生成新的查询
    let prompt;
    if (relevantPlugins.length > 0) {
      prompt = getPromptRagQueryGenerationCode(
        implementation,
        relevantPlugins.join(', ')
      );
    } else {
      prompt = getPromptRagQueryGenerationCode(
        implementation,
        'No plugins are relevant.'
      );
    }

    const queries = await this.helperModel(prepareTextInputs(prompt), {
      metadata: {
        generation_name: 'rag_query_generation',
        tags: [topic, `scene${sceneNumber}`],
      },
    });

    console.log(`RAG queries: ${queries}`);

    try {
      // 添加try-except块处理潜在的json解码错误
      const jsonMatch = queries.match(/```json([\s\S]*)```/);
      const jsonContent = jsonMatch ? jsonMatch[1] : queries;
      const parsedQueries = JSON.parse(jsonContent);

      // 缓存查询
      // fs.writeFileSync(cacheFile, JSON.stringify(parsedQueries));

      return parsedQueries;
    } catch (e) {
      console.log(
        `JSONDecodeError when parsing RAG queries for storyboard: ${e}`
      );
      console.log(`Response text was: ${queries}`);
      return []; // 解析错误时返回空列表
    }
  }

  /**
   * 为修复代码错误生成RAG查询
   *
   * @param {Object} options - 参数选项
   * @param {string} options.error - 要修复的错误消息
   * @param {string} options.code - 包含错误的代码
   * @param {string} options.topic - 场景主题，默认为null
   * @param {number} options.sceneNumber - 场景编号，默认为null
   * @param {string} options.sessionId - 会话标识符，默认为null
   * @param {Array} options.relevantPlugins - 相关插件列表，默认为空数组
   * @returns {Array} 用于错误修复的生成RAG查询列表
   */
  async _generateRagQueriesErrorFix({
    error,
    code,
    topic = null,
    sceneNumber = null,
    sessionId = null,
    relevantPlugins = [],
  }) {
    // 为此场景和错误创建缓存键
    const cacheKey = `${topic}_scene${sceneNumber}_error_fix`;

    // 检查是否已有错误修复查询的缓存文件
    // const cacheDir = path.join(
    //   this.outputDir,
    //   topic.toLowerCase().replace(/[^a-z0-9_]+/g, "_"),
    //   `scene${sceneNumber}`,
    //   "rag_cache"
    // );
    // fs.mkdirSync(cacheDir, { recursive: true });
    // const cacheFile = path.join(cacheDir, "rag_queries_error_fix.json");

    // // 如果缓存文件存在，加载并返回缓存的查询
    // if (fs.existsSync(cacheFile)) {
    //   const cachedQueries = JSON.parse(fs.readFileSync(cacheFile, "utf-8"));
    //   console.log(`Using cached RAG queries for error fix in ${cacheKey}`);
    //   return cachedQueries;
    // }

    // 如果没有缓存，为错误修复生成新的查询
    const prompt = getPromptRagQueryGenerationFixError({
      error: error,
      code: code,
      relevantPlugins:
        relevantPlugins.length > 0
          ? relevantPlugins.join(', ')
          : 'No plugins are relevant.',
    });

    const queries = await this.helperModel(prepareTextInputs(prompt), {
      metadata: {
        generation_name: 'rag-query-generation-fix-error',
        tags: [topic, `scene${sceneNumber}`],
      },
    });

    // 移除json三重反引号
    const cleanedQueries = queries.replace(/```json|```/g, '');
    try {
      // 添加try-except块处理潜在的json解码错误
      const parsedQueries = JSON.parse(cleanedQueries);

      // 缓存查询
      // fs.writeFileSync(cacheFile, JSON.stringify(parsedQueries));

      return parsedQueries;
    } catch (e) {
      console.log(
        `JSONDecodeError when parsing RAG queries for error fix: ${e}`
      );
      console.log(`Response text was: ${queries}`);
      return []; // 解析错误时返回空列表
    }
  }

  /**
   * 使用重试逻辑从响应文本中提取代码
   *
   * @param {Object} options - 参数选项
   * @param {string} options.responseText - 包含要提取代码的文本
   * @param {string} options.pattern - 用于提取代码的正则表达式模式
   * @param {string} options.generationName - 生成步骤的名称，默认为null
   * @param {string} options.traceId - 跟踪标识符，默认为null
   * @param {string} options.sessionId - 会话标识符，默认为null
   * @param {number} options.maxRetries - 最大重试次数，默认为10
   * @returns {string} 提取的代码
   * @throws {Error} 如果在最大重试次数后代码提取失败
   */
  async _extractCodeWithRetries({
    responseText,
    pattern,
    generationName = null,
    traceId = null,
    sessionId = null,
    maxRetries = 10,
  }) {
    const retryPrompt = `
    Please extract the Python code in the correct format using the pattern: ${pattern}.
    You MUST NOT include any other text or comments.
    You MUST return the exact same code as in the previous response, NO CONTENT EDITING is allowed.
    Previous response:
    ${responseText}
    `;

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      const codeMatch = responseText.match(new RegExp(pattern, 's'));
      if (codeMatch) {
        console.log(`xxr:成功提取代码模式`);
        return codeMatch[1];
      }

      if (attempt < maxRetries - 1) {
        console.log(`xxr:重试 ${attempt + 1}: 提取代码失败， Retrying...`);
        // 使用更明确的提示重新生成响应
        console.log(`xxr: 将rsp传给sceneModel，让大模型提取代码`);
        responseText = await this.sceneModel(prepareTextInputs(retryPrompt), {
          metadata: {
            generation_name: `${generationName}_format_retry_${attempt + 1}`,
            trace_id: traceId,
            session_id: sessionId,
          },
        });
        console.log(`xxr: 重试${attempt + 1}本次提取代码结束`);
      }
    }

    throw new Error(
      `Failed to extract code pattern after ${maxRetries} attempts. Pattern: ${pattern}`
    );
  }

  /**
   * 从视频计划生成Manim代码
   *
   * @param {Object} options - 参数选项
   * @param {string} options.topic - 场景主题
   * @param {string} options.description - 场景描述
   * @param {string} options.sceneOutline - 场景大纲
   * @param {string} options.sceneImplementation - 实现细节
   * @param {number} options.sceneNumber - 场景编号
   * @param {string|Array} options.additionalContext - 附加上下文，默认为null
   * @param {string} options.sessionId - 会话标识符，默认为null
   * @param {Object} options.ragQueriesCache - RAG查询缓存，默认为null
   * @returns {Array} 生成的代码和响应文本
   */
  async generateManimCode({
    topic,
    description,
    sceneContent,
    sceneOutline,
    sceneImplementation,
    sceneNumber,
    additionalContext = null,
    sessionId = null,
    // ragQueriesCache = null
  }) {
    if (this.useContextLearning) {
      // 将上下文示例添加到additionalContext
      if (additionalContext === null) {
        additionalContext = [];
      } else if (typeof additionalContext === 'string') {
        additionalContext = [additionalContext];
      }

      // 现在使用正确格式化的代码示例
      if (this.contextExamples) {
        additionalContext.push(this.contextExamples);
      }
    }

    if (this.useRag) {
      // 生成RAG查询（如果可用将使用缓存）
      const ragQueries = await this._generateRagQueriesCode({
        implementation: sceneImplementation,
        topic: topic,
        sceneNumber: sceneNumber,
      });

      const retrievedDocs = await this.vectorStore.findRelevantDocs({
        queries: ragQueries,
        k: 2, // 要检索的文档数量
        topic: topic,
        sceneNumber: sceneNumber,
      });

      // 将检索到的文档格式化为字符串
      if (additionalContext === null) {
        additionalContext = [];
      }
      additionalContext.push(retrievedDocs);
    }
    // 使用计划和检索的上下文格式化代码生成提示
    const prompt = getPromptCodeGeneration({
      sceneContent: sceneContent,
      sceneOutline: sceneOutline,
      sceneImplementation: sceneImplementation,
      topic: topic,
      description: description,
      sceneNumber: sceneNumber,
      additionalContext: additionalContext,
    });
    console.log(
      `xxr: 获取生成代码的提示: ${prompt}，附加条件可以上下文和RAG查询`
    );

    // 使用模型生成代码
    const responseText = await this.sceneModel(prepareTextInputs(prompt), {
      metadata: {
        generation_name: 'code_generation',
        tags: [topic, `scene${sceneNumber}`],
      },
    });
    console.log(`xxr: 使用sceneModel生成代码完成`);

    // 使用重试提取代码
    const code = await this._extractCodeWithRetries({
      responseText: responseText,
      pattern: '```python([\\s\\S]*)```',
      generationName: 'code_generation',
    });

    return code;
  }

  /**
   * 修复生成的Manim代码中的错误
   *
   * @param {Object} options - 参数选项
   * @param {string} options.implementationPlan - 原始实现计划
   * @param {string} options.code - 包含错误的代码
   * @param {string} options.error - 要修复的错误消息
   * @param {string} options.topic - 场景主题
   * @param {number} options.sceneNumber - 场景编号
   * @param {string} options.sessionId - 会话标识符
   * @param {Object} options.ragQueriesCache - RAG查询缓存，默认为null
   * @returns {Array} 修复的代码和响应文本
   */
  async fixCodeErrors({
    implementationPlan,
    code,
    error,
    topic,
    sceneNumber,
    // ragQueriesCache = null
  }) {
    // 格式化错误修复提示
    let prompt = getPromptFixError({
      implementationPlan: implementationPlan,
      manimCode: code,
      error: error,
    });

    if (this.useRag) {
      // 为错误修复生成RAG查询
      const ragQueries = await this._generateRagQueriesErrorFix({
        error: error,
        code: code,
        topic: topic,
        sceneNumber: sceneNumber,
      });

      const retrievedDocs = await this.vectorStore.findRelevantDocs({
        queries: ragQueries,
        k: 2, // 为错误修复检索的文档数量
        topic: topic,
        sceneNumber: sceneNumber,
      });
      console.log(11111222, code);
      // 将检索到的文档格式化为字符串
      prompt = getPromptFixError({
        implementationPlan: implementationPlan,
        manimCode: code,
        error: error,
        additionalContext: retrievedDocs,
      });
    }
    console.log(`xxr: 使用sceneModel修复code，可使用rag，提示词`);
    // 从模型获取修复的代码
    const responseText = await this.sceneModel(prepareTextInputs(prompt), {
      metadata: {
        generation_name: 'code_fix_error',
        tags: [topic, `scene${sceneNumber}`],
      },
    });
    console.log(`xxr: 使用sceneModel修复code完成,提取code`);
    // 使用重试提取修复的代码
    const fixedCode = await this._extractCodeWithRetries({
      responseText: responseText,
      pattern: '```python([\\s\\S]*)```',
      generationName: 'code_fix_error',
    });

    return [fixedCode, responseText];
  }

  /**
   * 使用快照图像或mp4视频修复代码
   *
   * @param {Object} options - 参数选项
   * @param {string} options.code - 要修复的代码
   * @param {string|Object} options.mediaPath - 媒体文件路径或PIL图像
   * @param {string} options.topic - 场景主题
   * @param {number} options.sceneNumber - 场景编号
   * @param {string} options.sessionId - 会话标识符
   * @returns {Array} 修复的代码和响应文本
   */
  async visualSelfReflection({ code, mediaPath, topic, sceneNumber }) {
    // 确定我们处理的是视频还是图像
    // const isVideo = typeof mediaPath === 'string' && mediaPath.endsWith('.mp4')

    // 格式化提示
    const prompt = getPromptVisualSelfReflection(code);

    // 根据媒体类型准备输入
    let messages;
    // if (isVideo && (this.sceneModel instanceof GeminiWrapper || this.sceneModel instanceof VertexAIWrapper)) {
    //   // 对于Gemini模型的视频
    //   messages = [
    //     { type: "text", content: prompt },
    //     { type: "video", content: mediaPath }
    //   ];
    // } else {
    // 对于图像或非Gemini模型
    let media;
    if (typeof mediaPath === 'string') {
      media = await Image.load(mediaPath);
    } else {
      media = mediaPath;
    }
    messages = [
      { type: 'text', content: prompt },
      { type: 'image', content: media },
    ];
    // }

    // 获取模型响应
    const responseText = await this.sceneModel(messages, {
      metadata: {
        generation_name: 'visual_self_reflection',
        tags: [topic, `scene${sceneNumber}`],
      },
    });

    // 使用重试提取代码
    const fixedCode = await this._extractCodeWithRetries({
      responseText: responseText,
      pattern: '```python([\\s\\S]*)```',
      generationName: 'visual_self_reflection',
    });

    return [fixedCode, responseText];
  }
}

// 添加 module.exports
module.exports = { CodeGenerator };
