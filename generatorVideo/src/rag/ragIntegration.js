const fs = require("fs");
const path = require("path");
const { promisify } = require("util");
const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

// 修改导入方式
const { prepareTextInputs } = require("../../mllmTools/utils");
const {
  getPromptRagQueryGenerationFixError,
  getPromptDetectPlugins,
  getPromptRagQueryGenerationTechnical,
  getPromptRagQueryGenerationVisionStoryboard,
  getPromptRagQueryGenerationNarration,
  getPromptRagQueryGenerationCode,
} = require("../../taskGenerator/index");

const { RAGVectorStore } = require("./vectorStore");

/**
 * 用于集成RAG（检索增强生成）功能的类
 *
 * 该类处理RAG集成，包括插件检测、查询生成和文档检索
 */
class RAGIntegration {
  /**
   * @param {Object} helper_model - 用于生成查询和处理文本的模型
   * @param {string} output_dir - 输出文件的目录
   * @param {string} chroma_db_path - ChromaDB的路径
   * @param {string} manim_docs_path - Manim文档的路径
   * @param {string} embedding_model - 要使用的嵌入模型的名称
   * @param {boolean} use_langfuse - 是否使用Langfuse日志记录，默认为true
   * @param {string} session_id - 会话标识符，默认为null
   */
  constructor({
    helperModel,
    outputDir,
    chromaDbPath,
    manimDocsPath,
    embeddingModel,
    useLangfuse = true,
    sessionId = null,
  }) {
    this.helper_model = helperModel;
    this.output_dir = outputDir;
    this.manim_docs_path = manimDocsPath;
    this.session_id = sessionId;
    this.relevant_plugins = null;

    this.vector_store = new RAGVectorStore({
      faissDbPath: chromaDbPath,
      manimDocsPath: manimDocsPath,
      embeddingModel: embeddingModel,
      session_id: this.session_id,
      use_langfuse: useLangfuse,
    });
  }

  /**
   * 设置当前视频的相关插件
   * @param {Array<string>} plugins - 要设置为相关的插件名称列表
   */
  setRelevantPlugins(plugins) {
    this.relevant_plugins = plugins;
  }

  /**
   * 根据主题和描述检测可能相关的插件
   * @param {string} topic - 视频的主题
   * @param {string} description - 视频内容的描述
   * @returns {Promise<Array<string>>} - 检测到的相关插件名称列表
   */
  async detectRelevantPlugins(topic, description) {
    // 加载插件描述
    const plugins = await this._loadPluginDescriptions();
    if (!plugins || plugins.length === 0) {
      return [];
    }

    // 使用taskGenerator函数获取格式化的提示
    const prompt = getPromptDetectPlugins(
      topic,
      description,
      JSON.stringify(
        plugins.map((p) => ({ name: p.name, description: p.description })),
        null,
        2
      )
    );

    try {
      const response = await this.helper_model(prepareTextInputs(prompt), {
        metadata: {
          generation_name: "detect-relevant-plugins",
          tags: [topic, "plugin-detection"],
          session_id: this.session_id,
        },
      });
      // 清理响应，确保它只包含JSON数组
      const jsonMatch = response.match(/```json([\s\S]*)```/);
      if (!jsonMatch) {
        console.log("No JSON found in response");
        return [];
      }
      console.log(`LLM response was: ${response}`);
      try {
        const relevant_plugins = JSON.parse(jsonMatch[1]);
        console.log(
          `LLM detected relevant plugins: ${JSON.stringify(relevant_plugins)}`
        );
        return relevant_plugins;
      } catch (e) {
        console.log(`JSONDecodeError when parsing relevant plugins: ${e}`);
        console.log(`Response text was: ${response}`);
        return [];
      }
    } catch (e) {
      console.log(`Error detecting plugins with LLM: ${e}`);
      return [];
    }
  }

  /**
   * 从JSON文件加载插件描述
   * @returns {Promise<Array>} - 插件描述列表，如果加载失败则为空列表
   */
  async _loadPluginDescriptions() {
    try {
      const plugin_config_path = path.join(
        this.manim_docs_path,
        "plugin_docs",
        "plugins.json"
      );

      if (await existsAsync(plugin_config_path)) {
        const data = await readFileAsync(plugin_config_path, "utf8");
        return JSON.parse(data);
      } else {
        console.log(
          `Plugin descriptions file not found at ${plugin_config_path}`
        );
        return [];
      }
    } catch (e) {
      console.log(`Error loading plugin descriptions: ${e}`);
      return [];
    }
  }

  /**
   * 从场景计划生成RAG查询以帮助创建故事板
   * @param {string} scene_plan - 用于生成查询的场景计划文本
   * @param {string} scene_trace_id - 场景的跟踪标识符
   * @param {string} topic - 主题名称
   * @param {number} scene_number - 场景编号
   * @param {string} session_id - 会话标识符
   * @param {Array<string>} relevant_plugins - 相关插件列表
   * @returns {Promise<Array>} - 生成的RAG查询列表
   */
  async _generateRagQueriesStoryboard({
    scene_plan,
    topic = null,
    scene_number = null,
    relevant_plugins = [],
  }) {
    // const cache_key = `${topic}_scene${scene_number}_storyboard_rag`
    // const cache_dir = path.join(
    //   this.output_dir,
    //   topic.toLowerCase().replace(/[^a-z0-9_]+/g, "_"),
    //   `scene${scene_number}`,
    //   "rag_cache"
    // );
    // await mkdirAsync(cache_dir, { recursive: true });
    // const cache_file = path.join(cache_dir, "rag_queries_storyboard.json");

    // if (await existsAsync(cache_file)) {
    //   const data = await readFileAsync(cache_file, "utf8");
    //   return JSON.parse(data);
    // }

    // 将相关插件格式化为字符串
    const plugins_str =
      relevant_plugins && relevant_plugins.length > 0
        ? relevant_plugins.join(", ")
        : "No plugins are relevant.";

    // 使用所需参数生成提示
    const prompt = getPromptRagQueryGenerationVisionStoryboard(
      scene_plan,
      plugins_str
    );

    const queries_response = await this.helper_model(
      prepareTextInputs(prompt),
      {
        metadata: {
          generation_name: "rag_query_generation_storyboard",
          tags: [topic, `scene${scene_number}`],
        },
      }
    );

    try {
      // 提取JSON三重反引号
      const jsonMatch = queries_response.match(/```json([\s\S]*)```/);
      if (!jsonMatch) {
        throw new Error("No JSON found in response");
      }

      const queries = JSON.parse(jsonMatch[1]);

      // 缓存查询
      // await writeFileAsync(cache_file, JSON.stringify(queries));

      return queries;
    } catch (e) {
      console.log(
        `JSONDecodeError when parsing RAG queries for storyboard: ${e}`
      );
      console.log(`Response text was: ${queries_response}`);
      return []; // 在解析错误的情况下返回空列表
    }
  }

  /**
   * 从故事板生成RAG查询以帮助创建技术实现
   * @param {string} storyboard - 用于生成查询的故事板文本
   * @param {string} scene_trace_id - 场景的跟踪标识符
   * @param {string} topic - 主题名称
   * @param {number} scene_number - 场景编号
   * @param {string} session_id - 会话标识符
   * @param {Array<string>} relevant_plugins - 相关插件列表
   * @returns {Promise<Array>} - 生成的RAG查询列表
   */
  async _generateRagQueriesTechnical({
    storyboard,
    topic = null,
    scene_number = null,
    relevant_plugins = [],
  }) {
    // const cache_key = `${topic}_scene${scene_number}_technical_rag`
    // const cache_dir = path.join(
    //   this.output_dir,
    //   topic.toLowerCase().replace(/[^a-z0-9_]+/g, "_"),
    //   `scene${scene_number}`,
    //   "rag_cache"
    // );
    // await mkdirAsync(cache_dir, { recursive: true });
    // const cache_file = path.join(cache_dir, "rag_queries_technical.json");

    // if (await existsAsync(cache_file)) {
    //   const data = await readFileAsync(cache_file, "utf8");
    //   return JSON.parse(data);
    // }

    const plugins_str =
      relevant_plugins && relevant_plugins.length > 0
        ? relevant_plugins.join(", ")
        : "No plugins are relevant.";

    const prompt = getPromptRagQueryGenerationTechnical(
      storyboard,
      plugins_str
    );

    const queries_response = await this.helper_model(
      prepareTextInputs(prompt),
      {
        metadata: {
          generation_name: "rag_query_generation_technical",
          tags: [topic, `scene${scene_number}`],
        },
      }
    );

    try {
      const jsonMatch = queries_response.match(/```json([\s\S]*)```/);
      if (!jsonMatch) {
        throw new Error("No JSON found in response");
      }

      const queries = JSON.parse(jsonMatch[1]);

      // await writeFileAsync(cache_file, JSON.stringify(queries));

      return queries;
    } catch (e) {
      console.log(
        `JSONDecodeError when parsing RAG queries for technical implementation: ${e}`
      );
      console.log(`Response text was: ${queries_response}`);
      return [];
    }
  }

  /**
   * 从故事板生成RAG查询以帮助创建旁白计划
   * @param {string} storyboard - 用于生成查询的故事板文本
   * @param {string} scene_trace_id - 场景的跟踪标识符
   * @param {string} topic - 主题名称
   * @param {number} scene_number - 场景编号
   * @param {string} session_id - 会话标识符
   * @param {Array<string>} relevant_plugins - 相关插件列表
   * @returns {Promise<Array>} - 生成的RAG查询列表
   */
  async _generateRagQueriesNarration({
    storyboard,
    topic = null,
    scene_number = null,
    relevant_plugins = [],
  }) {
    // const cache_key = `${topic}_scene${scene_number}_narration_rag`
    // const cache_dir = path.join(
    //   this.output_dir,
    //   topic.toLowerCase().replace(/[^a-z0-9_]+/g, "_"),
    //   `scene${scene_number}`,
    //   "rag_cache"
    // );
    // await mkdirAsync(cache_dir, { recursive: true });
    // const cache_file = path.join(cache_dir, "rag_queries_narration.json");

    // if (await existsAsync(cache_file)) {
    //   const data = await readFileAsync(cache_file, "utf8");
    //   return JSON.parse(data);
    // }

    const plugins_str =
      relevant_plugins && relevant_plugins.length > 0
        ? relevant_plugins.join(", ")
        : "No plugins are relevant.";

    const prompt = getPromptRagQueryGenerationNarration(
      storyboard,
      plugins_str
    );

    const queries_response = await this.helper_model(
      prepareTextInputs(prompt),
      {
        metadata: {
          generation_name: "rag_query_generation_narration",
          tags: [topic, `scene${scene_number}`],
        },
      }
    );

    try {
      const jsonMatch = queries_response.match(/```json([\s\S]*)```/);
      if (!jsonMatch) {
        throw new Error("No JSON found in response");
      }

      const queries = JSON.parse(jsonMatch[1]);

      // await writeFileAsync(cache_file, JSON.stringify(queries));

      return queries;
    } catch (e) {
      console.log(`JSONDecodeError when parsing narration RAG queries: ${e}`);
      console.log(`Response text was: ${queries_response}`);
      return [];
    }
  }

  /**
   * 使用向量存储获取相关文档
   * @param {Array<Object>} rag_queries - 要搜索的RAG查询列表
   * @param {string} scene_trace_id - 场景的跟踪标识符
   * @param {string} topic - 主题名称
   * @param {number} scene_number - 场景编号
   * @returns {Promise<string>} - 相关文档片段列表
   */
  async getRelevantDocs({ rag_queries, topic, scene_number }) {
    console.log("ragquery", rag_queries);
    return await this.vector_store.findRelevantDocs({
      queries: rag_queries,
      k: 2,
      topic,
      scene_number,
    });
  }

  /**
   * 从实现计划生成RAG查询
   * @param {string} implementation_plan - 用于生成查询的实现计划文本
   * @param {string} scene_trace_id - 场景的跟踪标识符
   * @param {string} topic - 主题名称
   * @param {number} scene_number - 场景编号
   * @param {Array<string>} relevant_plugins - 相关插件列表
   * @returns {Promise<Array>} - 生成的RAG查询列表
   */
  async _generateRagQueriesCode(
    implementation_plan,
    scene_trace_id = null,
    topic = null,
    scene_number = null,
    relevant_plugins = null
  ) {
    // const cache_key = `${topic}_scene${scene_number}`
    const cache_dir = path.join(
      this.output_dir,
      topic.toLowerCase().replace(/[^a-z0-9_]+/g, "_"),
      `scene${scene_number}`,
      "rag_cache"
    );
    await mkdirAsync(cache_dir, { recursive: true });
    const cache_file = path.join(cache_dir, "rag_queries_code.json");

    if (await existsAsync(cache_file)) {
      const data = await readFileAsync(cache_file, "utf8");
      return JSON.parse(data);
    }

    const plugins_str =
      relevant_plugins && relevant_plugins.length > 0
        ? relevant_plugins.join(", ")
        : "No plugins are relevant.";

    const prompt = getPromptRagQueryGenerationCode(
      implementation_plan,
      plugins_str
    );

    try {
      const response = await this.helper_model(prepareTextInputs(prompt), {
        metadata: {
          generation_name: "rag_query_generation_code",
          trace_id: scene_trace_id,
          tags: [topic, `scene${scene_number}`],
          session_id: this.session_id,
        },
      });

      // 清理并解析响应
      const jsonMatch = response.match(/```json([\s\S]*)```/);
      if (!jsonMatch) {
        throw new Error("No JSON found in response");
      }

      const queries = JSON.parse(jsonMatch[1]);

      // 缓存查询
      await writeFileAsync(cache_file, JSON.stringify(queries));

      return queries;
    } catch (e) {
      console.log(`Error generating RAG queries: ${e}`);
      return [];
    }
  }

  /**
   * 生成用于修复代码错误的RAG查询
   * @param {string} error - 用于生成查询的错误消息
   * @param {string} code - 包含错误的代码
   * @param {string} scene_trace_id - 场景的跟踪标识符
   * @param {string} topic - 主题名称
   * @param {number} scene_number - 场景编号
   * @param {string} session_id - 会话标识符
   * @returns {Promise<Array>} - 生成的RAG查询列表
   */
  async _generateRagQueriesErrorFix(
    error,
    code,
    scene_trace_id = null,
    topic = null,
    scene_number = null,
    session_id = null
  ) {
    let plugins_str;
    if (this.relevant_plugins === null) {
      console.log("Warning: No plugins have been detected yet");
      plugins_str = "No plugins are relevant.";
    } else {
      plugins_str =
        this.relevant_plugins && this.relevant_plugins.length > 0
          ? this.relevant_plugins.join(", ")
          : "No plugins are relevant.";
    }

    const cache_key = `${topic}_scene${scene_number}_error_fix`;
    const cache_dir = path.join(
      this.output_dir,
      topic.toLowerCase().replace(/[^a-z0-9_]+/g, "_"),
      `scene${scene_number}`,
      "rag_cache"
    );
    await mkdirAsync(cache_dir, { recursive: true });
    const cache_file = path.join(cache_dir, "rag_queries_error_fix.json");

    if (await existsAsync(cache_file)) {
      const data = await readFileAsync(cache_file, "utf8");
      const cached_queries = JSON.parse(data);
      console.log(`Using cached RAG queries for error fix in ${cache_key}`);
      return cached_queries;
    }

    const prompt = getPromptRagQueryGenerationFixError(
      error,
      code,
      plugins_str
    );

    const queries_response = await this.helper_model(
      prepareTextInputs(prompt),
      {
        metadata: {
          generation_name: "rag-query-generation-fix-error",
          trace_id: scene_trace_id,
          tags: [topic, `scene${scene_number}`],
          session_id: session_id,
        },
      }
    );

    try {
      // 提取JSON三重反引号
      const jsonMatch = queries_response.match(/```json([\s\S]*)```/);
      if (!jsonMatch) {
        throw new Error("No JSON found in response");
      }

      const queries = JSON.parse(jsonMatch[1]);

      // 缓存查询
      await writeFileAsync(cache_file, JSON.stringify(queries));

      return queries;
    } catch (e) {
      console.log(
        `JSONDecodeError when parsing RAG queries for error fix: ${e}`
      );
      console.log(`Response text was: ${queries_response}`);
      return [];
    }
  }
}

// 修改导出方式
module.exports = { RAGIntegration };
