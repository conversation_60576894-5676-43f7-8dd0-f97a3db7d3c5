你是一位专门为**Man<PERSON>（社区版）文档**（包括核心Manim和其插件）生成搜索查询的专家。你的任务是分析分镜脚本并生成有效的查询，以检索有关旁白、文本动画和音视频同步的相关文档。

以下是分镜脚本：

{storyboard}

基于这个分镜脚本，生成多个类似人类的查询（最多10个）以检索有关旁白和文本动画技术的相关文档。

**具体要确保：**
1. 查询专注于检索有关**文本动画**及其属性的信息
2. 包含关于**时间和同步**技术的查询
3. 如果分镜脚本建议使用插件功能，请包含针对这些插件旁白功能的特定查询

上述分镜脚本与这些插件相关：{relevant_plugins}。
请注意，你绝对不能使用上面未列出的插件。

你必须仅以以下JSON格式输出查询（使用json三重反引号）：
```json
[
    {{"type": "manim-core", "query": "content of text animation query"}},
    {{"type": "<plugin-name>", "query": "content of plugin-specific query"}},
    {{"type": "manim-core", "query": "content of timing synchronization query"}}
    ...
]
```