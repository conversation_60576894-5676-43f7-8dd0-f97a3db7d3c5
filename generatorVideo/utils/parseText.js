const { logger } = require('../../src/utils/logger');

// 解析场景大纲字符串为对象数组
function parseSceneOutline(sceneString) {
  // 确保有内容可解析
  if (!sceneString) {
    logger.error('parseSceneOutline: 输入为空');
    return [];
  }

  logger.info('parseSceneOutline: 开始解析场景大纲');
  logger.info('输入内容长度:', sceneString.length);

  const scenes = [];

  // 使用正则表达式匹配每个场景块
  const sceneRegex = /<SCENE_(\d+)>([\s\S]*?)<\/SCENE_\1>/g;
  let sceneMatch;
  let matchCount = 0;

  while ((sceneMatch = sceneRegex.exec(sceneString)) !== null) {
    matchCount++;
    const sceneNumber = sceneMatch[1];
    const sceneContent = sceneMatch[2].trim();
    logger.info(`找到场景 ${sceneNumber}, 内容长度: ${sceneContent.length}`);
    scenes.push(sceneContent);
  }

  logger.info(`parseSceneOutline: 共找到 ${matchCount} 个场景`);

  // 如果没有找到标准格式，尝试其他可能的格式
  if (scenes.length === 0) {
    logger.warn('未找到标准<SCENE_X>格式，尝试其他格式...');

    // 尝试匹配 **场景一：** 格式
    const altRegex =
      /\*\*场景[一二三]：.*?\*\*([\s\S]*?)(?=\*\*场景[一二三]：|\*\*整体要求\*\*|$)/g;
    let altMatch;

    while ((altMatch = altRegex.exec(sceneString)) !== null) {
      const sceneContent = altMatch[1].trim();
      if (sceneContent) {
        scenes.push(sceneContent);
        logger.info(`找到备用格式场景, 内容长度: ${sceneContent.length}`);
      }
    }
  }

  // 确保至少返回空数组而不是null
  return scenes.length > 0 ? scenes : [];
}
function parseSceneOutlineSingle(sceneContent) {
  // 提取场景标题
  const titleMatch = /Scene Title: (.*?)(?:\r?\n|\r|$)/.exec(sceneContent);
  const title = titleMatch ? titleMatch[1].trim() : '未命名场景';

  // 提取场景描述
  const descriptionMatch =
    /Scene Description: ([\s\S]*?)(?:Scene Layout:|$)/.exec(sceneContent);
  const description = descriptionMatch ? descriptionMatch[1].trim() : '无描述';

  // 提取场景目的（可选）
  const purposeMatch = /Scene Purpose: (.*?)(?:\r?\n|\r|$)/.exec(sceneContent);
  const purpose = purposeMatch ? purposeMatch[1].trim() : '';

  // 提取场景布局（可选）
  const layoutMatch = /Scene Layout: ([\s\S]*?)(?:\r?\n\r?\n|\r\r|$)/.exec(
    sceneContent
  );
  const layout = layoutMatch ? layoutMatch[1].trim() : '';
  return {
    title,
    description,
    purpose,
    layout,
  };
}

function parseStoryboard(storyboardString) {
  if (!storyboardString) {
    logger.warn('parseStoryboard: Input is null or empty');
    return null;
  }

  // 记录输入数据到日志
  logger.debug('parseStoryboard: Function called', {
    function: 'parseStoryboard',
    inputType: typeof storyboardString,
    inputLength: storyboardString.length,
    inputPreview:
      storyboardString.substring(0, 200) +
      (storyboardString.length > 200 ? '...' : ''),
  });

  const content = storyboardString;

  // 分离 SCENE_VISION 和 STORYBOARD 部分
  const sceneVisionMatch = content.match(
    /\[SCENE_VISION\]([\s\S]*?)\[STORYBOARD\]/
  );
  const storyboardContentMatch = content.match(/\[STORYBOARD\]([\s\S]*)/);

  logger.debug('parseStoryboard: Regex match results', {
    function: 'parseStoryboard',
    sceneVisionMatch: sceneVisionMatch ? 'Found match' : 'No match',
    storyboardContentMatch: storyboardContentMatch ? 'Found match' : 'No match',
    regexPatterns: {
      sceneVision: '/\\[SCENE_VISION\\]([\\s\\S]*?)\\[STORYBOARD\\]/',
      storyboard: '/\\[STORYBOARD\\]([\\s\\S]*)/',
    },
  });

  // 如果匹配失败，返回默认值而不是报错
  if (!sceneVisionMatch || !storyboardContentMatch) {
    logger.warn(
      'parseStoryboard: No matches found, returning default structure',
      {
        function: 'parseStoryboard',
        fallbackStrategy: 'returning original content for both fields',
      }
    );
    return {
      sceneVision: content,
      content: content,
    };
  }

  const sceneVision = sceneVisionMatch[1].trim();
  const storyboardContent = storyboardContentMatch[1].trim();

  logger.info('parseStoryboard: Successfully parsed content', {
    function: 'parseStoryboard',
    sceneVisionLength: sceneVision.length,
    storyboardContentLength: storyboardContent.length,
  });

  // 返回简化的结果
  return {
    sceneVision,
    content: storyboardContent,
  };
}
function parseTLContent(content) {
  return content;
}
function parseAnimationContent(content) {
  return content;
}
module.exports = {
  parseSceneOutline,
  parseSceneOutlineSingle,
  parseStoryboard,
  parseTLContent,
  parseAnimationContent,
};
