{"name": "webhooks", "version": "1.0.0", "description": "", "main": "webhook-server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@langchain/community": "^0.3.40", "@langchain/core": "^0.3.44", "@langchain/deepseek": "^0.0.1", "@langchain/openai": "^0.5.5", "aws-sdk": "^2.1692.0", "axios": "^1.7.9", "body-parser": "^1.20.3", "cors": "^2.8.5", "crypto": "^1.0.1", "express": "^4.21.2", "faiss-node": "^0.5.1", "fluent-ffmpeg": "^2.1.3", "image-js": "^0.36.1", "mysql": "^2.18.1", "pm2": "^5.4.3", "tqdm": "^2.0.3"}}