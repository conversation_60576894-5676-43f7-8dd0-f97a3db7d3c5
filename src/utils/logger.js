const fs = require('fs');
const path = require('path');
const util = require('util');

class Logger {
  constructor(options = {}) {
    this.logDir = options.logDir || path.join(process.cwd(), 'logs');
    this.maxFileSize = options.maxFileSize || 10 * 1024 * 1024; // 10MB
    this.maxFiles = options.maxFiles || 5;
    this.enableConsole = options.enableConsole !== false;
    this.enableFile = options.enableFile !== false;
    this.logLevel = options.logLevel || 'info';

    // 日志级别定义
    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3,
      trace: 4,
    };

    // 颜色配置
    this.colors = {
      error: '\x1b[31m', // 红色
      warn: '\x1b[33m', // 黄色
      info: '\x1b[36m', // 青色
      debug: '\x1b[35m', // 紫色
      trace: '\x1b[37m', // 白色
      reset: '\x1b[0m',
    };

    this.initLogDirectory();
  }

  initLogDirectory() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  shouldLog(level) {
    return this.levels[level] <= this.levels[this.logLevel];
  }

  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const pid = process.pid;
    const memUsage = process.memoryUsage();

    const logEntry = {
      timestamp,
      level: level.toUpperCase(),
      pid,
      message,
      meta,
      memory: {
        rss: Math.round(memUsage.rss / 1024 / 1024) + 'MB',
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + 'MB',
        external: Math.round(memUsage.external / 1024 / 1024) + 'MB',
      },
    };

    return logEntry;
  }

  formatConsoleMessage(logEntry) {
    const color =
      this.colors[logEntry.level.toLowerCase()] || this.colors.reset;
    const resetColor = this.colors.reset;

    let output = `${color}[${logEntry.timestamp}] [${logEntry.level}] [PID:${logEntry.pid}]${resetColor} ${logEntry.message}`;

    if (Object.keys(logEntry.meta).length > 0) {
      output += `\n${color}Meta:${resetColor} ${util.inspect(logEntry.meta, {
        colors: true,
        depth: 3,
      })}`;
    }

    if (logEntry.level === 'ERROR' || logEntry.level === 'WARN') {
      output += `\n${color}Memory:${resetColor} ${JSON.stringify(
        logEntry.memory
      )}`;
    }

    return output;
  }

  formatFileMessage(logEntry) {
    return JSON.stringify(logEntry) + '\n';
  }

  getLogFileName(level) {
    const date = new Date().toISOString().split('T')[0];
    return path.join(this.logDir, `${level}-${date}.log`);
  }

  rotateLogFile(filePath) {
    if (!fs.existsSync(filePath)) return;

    const stats = fs.statSync(filePath);
    if (stats.size < this.maxFileSize) return;

    // 轮转日志文件
    for (let i = this.maxFiles - 1; i > 0; i--) {
      const oldPath = `${filePath}.${i}`;
      const newPath = `${filePath}.${i + 1}`;

      if (fs.existsSync(oldPath)) {
        if (i === this.maxFiles - 1) {
          fs.unlinkSync(oldPath); // 删除最老的文件
        } else {
          fs.renameSync(oldPath, newPath);
        }
      }
    }

    fs.renameSync(filePath, `${filePath}.1`);
  }

  writeToFile(level, logEntry) {
    if (!this.enableFile) return;

    const filePath = this.getLogFileName(level);
    this.rotateLogFile(filePath);

    try {
      const logMessage = this.formatFileMessage(logEntry);
      fs.appendFileSync(filePath, logMessage);
    } catch (error) {
      console.error('写入日志文件失败:', error);
    }
  }

  writeToConsole(logEntry) {
    if (!this.enableConsole) return;

    const message = this.formatConsoleMessage(logEntry);

    if (logEntry.level === 'ERROR') {
      console.error(message);
    } else if (logEntry.level === 'WARN') {
      console.warn(message);
    } else {
      console.log(message);
    }
  }

  log(level, message, meta = {}) {
    if (!this.shouldLog(level)) return;

    // 如果message是Error对象，提取stack信息
    if (message instanceof Error) {
      meta.stack = message.stack;
      message = message.message;
    }

    const logEntry = this.formatMessage(level, message, meta);

    this.writeToConsole(logEntry);
    this.writeToFile(level, logEntry);
    this.writeToFile('all', logEntry); // 同时写入综合日志
  }

  error(message, meta = {}) {
    this.log('error', message, meta);
  }

  warn(message, meta = {}) {
    this.log('warn', message, meta);
  }

  info(message, meta = {}) {
    this.log('info', message, meta);
  }

  debug(message, meta = {}) {
    this.log('debug', message, meta);
  }

  trace(message, meta = {}) {
    this.log('trace', message, meta);
  }

  // HTTP请求日志专用方法
  httpRequest(req, res, duration) {
    const meta = {
      method: req.method,
      url: req.originalUrl || req.url,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      contentLength: res.get('Content-Length') || 0,
    };

    const level = res.statusCode >= 400 ? 'warn' : 'info';
    this.log(level, `HTTP ${req.method} ${req.originalUrl || req.url}`, meta);
  }

  // 数据库操作日志
  dbQuery(query, params, duration, error = null) {
    const meta = {
      query: query.substring(0, 200) + (query.length > 200 ? '...' : ''),
      params,
      duration: `${duration}ms`,
    };

    if (error) {
      meta.error = error.message;
      this.error('数据库查询失败', meta);
    } else {
      this.debug('数据库查询执行完成', meta);
    }
  }

  // 视频生成日志
  videoGeneration(sessionId, sceneNum, operation, meta = {}) {
    const logMeta = {
      sessionId,
      sceneNum,
      operation,
      ...meta,
    };

    this.info(`视频生成: ${operation}`, logMeta);
  }

  // 性能监控日志
  performance(operation, duration, meta = {}) {
    const logMeta = {
      operation,
      duration: `${duration}ms`,
      ...meta,
    };

    const level = duration > 5000 ? 'warn' : 'info'; // 超过5秒警告
    this.log(level, `性能监控: ${operation}`, logMeta);
  }

  // 清理旧日志文件
  cleanOldLogs(daysToKeep = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    fs.readdir(this.logDir, (err, files) => {
      if (err) return;

      files.forEach((file) => {
        const filePath = path.join(this.logDir, file);
        fs.stat(filePath, (err, stats) => {
          if (err) return;

          if (stats.mtime < cutoffDate) {
            fs.unlink(filePath, (err) => {
              if (!err) {
                this.info(`已清理旧日志文件: ${file}`);
              }
            });
          }
        });
      });
    });
  }

  // 获取日志统计信息
  getLogStats() {
    const stats = {
      totalSize: 0,
      fileCount: 0,
      files: [],
    };

    if (!fs.existsSync(this.logDir)) return stats;

    const files = fs.readdirSync(this.logDir);

    files.forEach((file) => {
      const filePath = path.join(this.logDir, file);
      const fileStat = fs.statSync(filePath);

      stats.totalSize += fileStat.size;
      stats.fileCount++;
      stats.files.push({
        name: file,
        size: fileStat.size,
        modified: fileStat.mtime,
      });
    });

    return stats;
  }
}

// 创建默认logger实例
const defaultLogger = new Logger({
  logLevel: process.env.LOG_LEVEL || 'info',
  enableConsole: process.env.NODE_ENV !== 'production',
  logDir: path.join(process.cwd(), 'logs'),
});

// 导出logger类和默认实例
module.exports = {
  Logger,
  logger: defaultLogger,
};
