const axios = require('axios');

// 测试缓存机制
async function testCacheMechanism() {
  console.log('=== 缓存机制测试 ===\n');

  const baseURL = 'http://localhost:3000/frontend-chat';
  const testData = {
    topic: '勾股定理',
    description: '通过动画演示勾股定理的概念、证明和应用'
  };

  console.log('测试数据:', testData);
  console.log('\n--- 第一次请求（应该生成新内容）---');

  try {
    // 第一次请求
    const start1 = Date.now();
    const response1 = await axios.post(`${baseURL}/generateSceneOutline`, testData);
    const time1 = Date.now() - start1;
    
    console.log('第一次请求结果:');
    console.log('- 状态码:', response1.status);
    console.log('- 响应时间:', time1 + 'ms');
    console.log('- sessionId:', response1.data.data?.sessionId);
    console.log('- 场景数量:', response1.data.data?.sceneList?.length);

    // 等待1秒
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('\n--- 第二次请求（应该使用缓存）---');
    
    // 第二次请求（相同数据）
    const start2 = Date.now();
    const response2 = await axios.post(`${baseURL}/generateSceneOutline`, testData);
    const time2 = Date.now() - start2;
    
    console.log('第二次请求结果:');
    console.log('- 状态码:', response2.status);
    console.log('- 响应时间:', time2 + 'ms');
    console.log('- sessionId:', response2.data.data?.sessionId);
    console.log('- 场景数量:', response2.data.data?.sceneList?.length);

    // 分析结果
    console.log('\n--- 缓存效果分析 ---');
    const isSameSessionId = response1.data.data?.sessionId === response2.data.data?.sessionId;
    const speedImprovement = time1 - time2;
    
    console.log('SessionId是否相同:', isSameSessionId ? '✅ 是' : '❌ 否');
    console.log('响应时间对比:');
    console.log(`- 第一次: ${time1}ms`);
    console.log(`- 第二次: ${time2}ms`);
    console.log(`- 提升: ${speedImprovement}ms (${speedImprovement > 0 ? '缓存生效' : '缓存未生效'})`);

    if (isSameSessionId && speedImprovement > 0) {
      console.log('\n🎉 缓存机制工作正常！');
    } else {
      console.log('\n⚠️  缓存机制可能有问题：');
      if (!isSameSessionId) {
        console.log('- SessionId不同，说明没有找到缓存');
      }
      if (speedImprovement <= 0) {
        console.log('- 响应时间没有明显改善');
      }
    }

    // 测试细微差异的输入
    console.log('\n--- 测试输入差异敏感性 ---');
    const testDataWithSpace = {
      topic: '勾股定理 ', // 末尾多一个空格
      description: '通过动画演示勾股定理的概念、证明和应用'
    };

    const response3 = await axios.post(`${baseURL}/generateSceneOutline`, testDataWithSpace);
    const isSameWithSpace = response1.data.data?.sessionId === response3.data.data?.sessionId;
    
    console.log('末尾空格测试:');
    console.log('- 原始topic: "' + testData.topic + '"');
    console.log('- 空格topic: "' + testDataWithSpace.topic + '"');
    console.log('- SessionId相同:', isSameWithSpace ? '✅ 是（忽略空格）' : '❌ 否（严格匹配）');

  } catch (error) {
    console.error('测试过程中发生错误:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }

  console.log('\n=== 测试完成 ===');
}

// 运行测试
if (require.main === module) {
  testCacheMechanism();
}

module.exports = {
  testCacheMechanism,
};
