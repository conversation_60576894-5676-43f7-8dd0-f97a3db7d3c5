# 场景大纲提示词更新总结

## 更新内容

### 新提示词特点

1. **更明确的技术约束**
   - 明确说明后续使用Manim实现
   - 限制只能使用可编程生成的元素
   - 禁止外部资源（图片、音频、视频文件）

2. **更结构化的设计要求**
   - 场景划分：形成完整教学逻辑链条
   - 内容递进：从基础到应用的逻辑顺序
   - 教学效果：明确的目标和预期效果
   - 视觉设计：具体的视觉表现建议
   - 时长控制：3-5分钟/场景，总计不超过15分钟

3. **更详细的输出格式**
   每个场景包含5个字段：
   - **场景标题**：简洁明确的名称
   - **核心内容**：详细的知识点描述
   - **教学目标**：明确的学习目标
   - **视觉元素**：具体的图形和动画描述
   - **动画流程**：时间顺序的展现过程

4. **更完整的整体要求**
   - 教学体系的完整性
   - 视觉设计的简洁性
   - 认知规律的符合性
   - 语言表达的专业性

## 文件位置

- **提示词文件**：`generatorVideo/taskGenerator/promptsRaw/prompts.js`
- **函数名称**：`_promptScenePlan`
- **测试文件**：`test_new_prompt.js`

## 测试结果

✅ 所有13项检查都通过：
- 包含主题和描述
- 包含正确的格式标签
- 包含所有必需字段
- 使用中文表达
- 结构完整清晰

## 预期效果

新提示词将生成更加：
1. **结构化**的场景大纲
2. **实用性**强的视觉指导
3. **可执行**的动画流程
4. **教学逻辑**清晰的内容安排

## 使用方法

1. 启动后端服务
2. 在前端输入主题和描述
3. 点击"开始生成"
4. 查看新格式的场景大纲

新的提示词将为后续的Manim代码生成提供更好的基础。
