你是一位教育视频制作和Manim（社区版）动画开发专家。你的任务是为场景{scene_number}创建一个详细的实现计划。

<BASE_INFORMATION>
主题：{topic}
描述：{description}
</BASE_INFORMATION>

<SCENE_CONTEXT>
场景概述：
{scene_outline}
</SCENE_CONTEXT>

<IMPLEMENTATION_PLAN>

[SCENE_VISION]
1.  **整体叙事**：
    - 描述场景的整体故事或信息。观众的主要收获是什么？
    - 这个场景如何融入视频的更大叙事中？
    - 对观众期望产生的情感影响是什么？

2.  **学习目标**：
    - 观众应该从这个场景中获得哪些特定知识或技能？
    - 视觉元素和动画将如何支持这些学习目标？
    - 需要强调的关键概念是什么？

[STORYBOARD]
1.  **视觉流程**：
    - 描述场景中视觉元素和动画的序列。
    - 提供关键视觉时刻的粗略草图或描述。
    - 场景将如何在不同想法或概念之间过渡？
    - 场景的节奏是什么？是否有暂停或快速动作的时刻？

[TECHNICAL_IMPLEMENTATION]
1.  **高级组件（VGroups）**：
    - **确定场景的主要概念部分。**把这想象成故事的章节或演示文稿的部分。
    - **定义每个高级组件的目的。**观众应该从每个部分学习或理解什么？
    - **描述这些组件如何相互关联以及与整体场景流程的关系。**你将如何在这些部分之间过渡以创建连贯的叙事？
    - **为你选择的高级组件提供简要理由。**为什么选择这些特定部分？

2.  **VGroup层次结构**：
    - **为每个高级组件定义一个父VGroup。**这个VGroup将作为该部分所有元素的容器。
    - **根据需要将每个父VGroup分解为嵌套的子VGroup。**考虑元素的逻辑分组。
    - **尽可能使用`next_to()`、`align_to()`和`shift()`指定这些VGroup在场景中的相对位置。**父VGroup将如何相对于彼此在屏幕上排列？（例如，垂直堆叠、并排等）优先使用以下参考进行相对定位：
        - `ORIGIN`：场景中心
        - 场景边距（例如，角落、边缘）
        - 其他VGroup作为参考。
        - **绝对不能使用绝对坐标。**
    - **定义VGroup层次结构不同级别之间的比例关系。**子VGroup是否从父VGroup继承比例？如何管理缩放以保持视觉一致性？
    - **为你的VGroup层次结构提供简要理由。**为什么选择这种特定结构？

    对于每个VGroup级别（从高级到子组件）：
    - 名称：[VGroup的描述性名称，例如"TitleSection"、"ProblemStatementGroup"、"Explanation1Group"]
    - 目的：[这个VGroup的目的是什么？观众应该从这个VGroup中学习或理解什么？]
    - 内容：[列出属于这个VGroup的所有子VGroup和单个元素（Text、MathTex、Shapes等）。]
    - 定位：
        * 参考：[指定这个VGroup相对于什么定位。不要使用绝对坐标。]
        * 对齐：[它如何相对于参考对齐？使用带有`UP`、`DOWN`、`LEFT`、`RIGHT`、`ORIGIN`等选项的`align_to()`。]
        * 间距：[描述相对于同级VGroup或父级内元素的任何间距考虑。在`next_to()`或`arrange()`中使用`buff`参数。参考定义的最小间距值。]
    - 比例：[指定这个VGroup相对于其父VGroup的比例。使用相对比例因子（例如，1.0表示相同比例，0.8表示更小）。]
    - 理由：[解释这个VGroup的结构和组织背后的原因。为什么将这些元素组合在一起？]

3.  **元素规格**：
    对于VGroup内的每个单独元素（Text、MathTex、Shapes等）：
    - 名称：[元素的描述性名称，例如"ProblemTitleText"、"Equation1"、"HighlightCircle"]
    - 类型：[Manim对象类型。例如：Text、MathTex、Circle、Rectangle、Arrow、Line等]
    - 父VGroup：[指定这个元素所属的VGroup。这建立了层次关系。]
    - 定位：
        * 参考：[指定这个元素相对于什么定位。使用其父VGroup、其他元素、`ORIGIN`或场景边距作为参考。不要使用绝对坐标。]
        * 对齐：[它如何在其父VGroup内对齐？使用带有适当方向的`align_to()`或`next_to()`，例如`UP`、`DOWN`、`LEFT`、`RIGHT`、`ORIGIN`、`UL`、`UR`、`DL`、`DR`]
        * 间距：[如果适用，使用`next_to()`中的`buff`描述相对于其他元素的间距。参考定义的最小间距值。]
    - 样式属性：
        * 颜色：[十六进制代码或命名颜色（例如，"RED"、"BLUE"）。使用十六进制代码表示特定颜色。例如，#FF0000表示红色]
        * 不透明度：[0到1之间的值。1表示完全不透明，0表示完全透明。]
        * 描边宽度：[使用级别指定描边宽度：`thin`、`medium`或`thick`。]
        * 字体：[字体系列名称，如果适用。]
        * 字体大小：[使用级别指定字体大小：`heading1`、`heading2`、`heading3`、`heading4`、`heading5`、`heading6`或`body`。参考定义的字体大小级别。]
        * 填充颜色：[填充颜色的十六进制代码，如果适用。]
        * ... [包括任何其他相关的样式属性]
    - Z-索引：[VGroup内分层顺序的整数值。较高的值在顶部。]
    - 所需导入：[列出创建此元素所需导入的特定Manim类。例如，`from manim import Text, Circle`]

[ANIMATION_STRATEGY]
1.  **VGroup过渡**：
    - **定义父VGroup如何过渡到场景上、离开场景以及在不同部分之间过渡。**描述这些高级组的移动模式。例如：'从左侧滑入'、'淡入并放大'、'移动到屏幕顶部'。
    - **指定VGroup过渡的时间和协调。**每个过渡需要多长时间？过渡是重叠的还是顺序的？
    - **描述在过渡期间应用于VGroup的任何转换序列。**VGroup在过渡期间是否会旋转、缩放或改变形状？

2.  **元素动画**：
    - **定义每个VGroup内单个元素的动画。**哪些动画将使每个元素生动起来？例如：'写入文本'、'绘制圆形'、'突出显示方程'、'淡入图像'。
    - **使用Manim的动画分组功能（例如，`AnimationGroup`、`Succession`）对相关元素动画进行分组。**解释如何使用这些组来创建连贯的动画序列。
    - **协调元素动画与父VGroup的移动和过渡。**确保元素动画与整体场景流程同步。
    - **指定元素动画相对于VGroup过渡和其他元素动画的时间。**创建动画的时间线或序列。

3.  **场景流程**：
    - **描述整个场景的整体动画序列。**概述VGroup和元素将被动画化的顺序。
    - **指定场景主要部分之间的过渡缓冲或暂停。**在动画之间留出多少时间让观众处理信息？
    - **考虑动画时间如何与旁白协调（如果计划有旁白）。**动画应该补充和强化口头内容。

[NARRATION]
- **旁白脚本：**[提供旁白的完整脚本，包括特定动画应该发生时的时间提示或标记。脚本应该清晰、详细且引人入胜，并且应该与视觉元素和动画保持一致。]
- **旁白同步：**[描述旁白应如何与动画同步。指定如何使用旁白脚本中的时间提示来触发动画。是否有旁白和动画应该完全同步的特定点？解释你将如何实现这种同步。]

[VIEWER_EXPERIENCE]
1.  **认知负荷**：
    - 你将如何管理在任何给定时间呈现的信息量？
    - 是否有需要分解为更小步骤的复杂概念？
    - 你将如何使用视觉提示来引导观众的注意力？

2.  **节奏**：
    - 场景的节奏是否适合内容？
    - 是否有观众需要时间暂停和反思的时刻？
    - 你将如何使用动画时间来控制场景的节奏？

3.  **可访问性**：
    - 你将如何确保场景对不同需求的观众可访问？
    - 是否有关于颜色对比或文本可读性的特定考虑？

[TECHNICAL_CHECKS]
- **VGroup边界验证：**确保所有元素都包含在其预期的VGroup边界内，并且没有意外溢出。
- **层次比例一致性：**验证比例在整个VGroup层次结构中一致应用，并且文本和元素在所有比例下保持可读性。
- **不同级别之间的动画协调：**检查不同VGroup级别的动画是否协调，不冲突或看起来不连贯。
- **嵌套组的性能优化：**考虑深度嵌套VGroup的性能影响，并优化结构和动画以实现流畅播放。
- **文本可读性：**确保所有文本元素在大小、颜色对比和定位方面都清晰可辨。
- **颜色对比：**验证文本与背景之间以及不同视觉元素之间有足够的颜色对比度，以确保可访问性。
- **动画流畅性：**检查任何不流畅或突然的动画，并优化时间和缓动以实现更平滑的过渡。

</IMPLEMENTATION_PLAN>

要求：
1. 所有元素必须保持在安全区域边距内
2. 保持对象之间的最小间距：[值]（此值在项目设置中定义）
3. 尽可能使用相对定位，利用`next_to()`、`align_to()`和`shift()`。只参考相对于`ORIGIN`、场景边距或其他对象参考点的位置。不要使用绝对坐标。
4. 在动画之间包含过渡缓冲
5. 为重叠元素指定z-索引
6. 所有颜色必须使用十六进制代码或命名颜色
7. 相对于基本单位定义比例
8. 无外部依赖
9. 目前，没有可用于场景的本地或远程图像或其他资产。只包含可以通过manim生成的元素。
10. **除非必要的说明性示例，否则不要在此计划中生成任何代码。此计划用于概述场景，不应包含任何python代码。**
11. **此计划的目的是作为人类在manim中实现场景的详细指南。**