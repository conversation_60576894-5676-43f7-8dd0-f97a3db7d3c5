const fs = require('fs');
const path = require('path');

/**
 * 查找 prompts_raw 目录并生成包含提示文本的 prompts.js 文件
 *
 * 在当前和父目录中搜索 prompts_raw 目录。一旦找到，
 * 调用 createJsFileWithTexts() 生成 prompts.js 文件。
 */
function callParsePrompt() {
  const currentFilePath = __filename;
  const currentFolderPath = path.dirname(currentFilePath);
  let folderPath = path.join(currentFolderPath, 'promptsRaw');

  // 如果在当前目录中找不到 prompts_raw，则搜索父目录
  if (!fs.existsSync(folderPath)) {
    let parentDir = currentFolderPath;
    while (parentDir !== path.dirname(parentDir)) {
      // 在根目录停止
      parentDir = path.dirname(parentDir);
      const testPath = path.join(parentDir, 'promptsRaw');
      if (fs.existsSync(testPath)) {
        folderPath = testPath;
        break;
      }
    }
  }

  const outputFile = path.join(folderPath, 'prompts.js');
  createJsFileWithTexts(folderPath, outputFile);
}

/**
 * 生成包含来自 .txt 文件的提示文本的 JavaScript 文件
 *
 * @param {string} folderPath - 包含提示 .txt 文件的目录路径
 * @param {string} outputFile - 生成的 JavaScript 文件将保存的路径
 *
 * 该函数读取给定文件夹中的所有 .txt 文件，将其内容转换为 JavaScript 变量，
 * 并将它们写入输出文件。变量名从文件路径派生，特殊字符被替换。
 */
function createJsFileWithTexts(folderPath, outputFile) {
  // 开始写入输出文件
  let outputContent = '// 此文件通过 parse_prompt.js 自动生成\n\n';

  // 递归获取所有 txt 文件
  const txtFiles = getAllTxtFiles(folderPath);

  console.log(`处理 ${txtFiles.length} 个文件...`);

  // 处理每个文件
  txtFiles.forEach((file, index) => {
    const filePath = path.join(folderPath, file);
    // 创建变量名：替换路径分隔符和扩展名
    const relativePath = filePath.replace(folderPath, '');

    // 将文件名转换为驼峰命名法
    let varName =
      '_' +
      relativePath
        .replace(new RegExp('\\' + path.sep, 'g'), '_')
        .replace('.txt', '')
        .replace(/^_/, '')
        .replace(/_([a-z])/g, (match, letter) => letter.toUpperCase()); // 将下划线后的字母转为大写

    // 读取文件内容
    const content = fs
      .readFileSync(filePath, 'utf-8')
      .replace(/`/g, '\\`')
      .replace(/\\"/g, '"'); // 转义反引号 // 转义反引号 // 转义反引号

    // 使用模板字符串添加到输出内容
    outputContent += `const ${varName} = \`${content}\`;\n\n`;

    // 进度显示
    process.stdout.write(`处理文件: ${index + 1}/${txtFiles.length}\r`);
  });

  // 添加导出语句
  outputContent += 'module.exports = {\n';
  txtFiles.forEach((file) => {
    const filePath = path.join(folderPath, file);
    const relativePath = filePath.replace(folderPath, '');

    // 将文件名转换为驼峰命名法
    let varName =
      '_' +
      relativePath
        .replace(new RegExp('\\' + path.sep, 'g'), '_')
        .replace('.txt', '')
        .replace(/^_/, '')
        .replace(/_([a-z])/g, (match, letter) => letter.toUpperCase()); // 将下划线后的字母转为大写

    outputContent += `  ${varName},\n`;
  });
  outputContent += '};\n';

  // 写入文件
  fs.writeFileSync(outputFile, outputContent, 'utf-8');
  console.log(`\n成功生成文件: ${outputFile}`);
}

/**
 * 递归获取目录中的所有 .txt 文件
 *
 * @param {string} dir - 要搜索的目录
 * @param {string} [subDir=''] - 子目录路径（用于递归）
 * @returns {string[]} - 相对于基础目录的 txt 文件路径数组
 */
function getAllTxtFiles(dir, subDir = '') {
  let results = [];
  const list = fs.readdirSync(path.join(dir, subDir));

  list.forEach((file) => {
    const relativePath = path.join(subDir, file);
    const fullPath = path.join(dir, relativePath);
    const stat = fs.statSync(fullPath);

    if (stat && stat.isDirectory()) {
      // 递归处理子目录
      results = results.concat(getAllTxtFiles(dir, relativePath));
    } else if (file.endsWith('.txt')) {
      // 添加 txt 文件
      results.push(relativePath);
    }
  });

  return results;
}

// 如果直接运行此脚本，则执行 callParsePrompt
if (require.main === module) {
  callParsePrompt();
}

module.exports = { callParsePrompt };
