const {
  getPromptScenePlan,
} = require('./generatorVideo/taskGenerator/index.js');

// 测试新的提示词
function testNewPrompt() {
  console.log('=== 测试新的场景大纲提示词 ===\n');

  const topic = '勾股定理';
  const description = '通过动画演示勾股定理的概念、证明和应用';

  const prompt = getPromptScenePlan(topic, description);

  console.log('生成的提示词:');
  console.log('---');
  console.log(prompt);
  console.log('---\n');

  // 检查提示词是否包含关键元素
  const checks = [
    { name: '包含主题', test: prompt.includes(topic) },
    { name: '包含描述', test: prompt.includes(description) },
    { name: '包含三个场景标签', test: /<SCENE_[123]>/.test(prompt) },
    { name: '包含动画描述格式', test: prompt.includes('**动画描述**') },
    { name: '包含脚本大纲格式', test: prompt.includes('**脚本大纲**') },
    { name: '包含场景标题字段', test: prompt.includes('**场景标题**') },
    { name: '包含核心内容字段', test: prompt.includes('**核心内容**') },
    { name: '包含教学目标字段', test: prompt.includes('**教学目标**') },
    { name: '包含视觉元素字段', test: prompt.includes('**视觉元素**') },
    { name: '包含动画流程字段', test: prompt.includes('**动画流程**') },
    { name: '包含设计要求', test: prompt.includes('设计要求') },
    { name: '包含整体要求', test: prompt.includes('**整体要求**') },
    { name: '使用中文', test: prompt.includes('场景标题') },
  ];

  console.log('提示词检查结果:');
  checks.forEach((check) => {
    console.log(`${check.test ? '✅' : '❌'} ${check.name}`);
  });

  const passedChecks = checks.filter((check) => check.test).length;
  console.log(`\n通过检查: ${passedChecks}/${checks.length}`);

  if (passedChecks === checks.length) {
    console.log('🎉 所有检查都通过了！');
  } else {
    console.log('⚠️  有些检查未通过，请检查提示词。');
  }

  console.log('\n=== 测试完成 ===');
}

// 运行测试
if (require.main === module) {
  testNewPrompt();
}

module.exports = {
  testNewPrompt,
};
