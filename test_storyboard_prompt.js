const {
  getPromptSceneVisionStoryboard,
} = require('./generatorVideo/taskGenerator/index.js');

// 测试新的故事板提示词
function testStoryboardPrompt() {
  console.log('=== 测试新的故事板提示词 ===\n');

  const topic = '勾股定理';
  const description = '通过动画演示勾股定理的概念、证明和应用';
  const sceneOutline = `
**场景一：勾股定理的发现**
* 核心内容：介绍勾股定理的基本概念，展示直角三角形的三边关系
* 教学目标：让观众理解勾股定理的基本表述和几何意义
* 视觉方向提示：绘制一个标准的直角三角形，标注三边长度

**场景二：勾股定理的证明**
* 核心内容：通过几何证明方法来证明勾股定理
* 教学目标：让观众理解勾股定理的数学严谨性
* 视觉方向提示：构建大正方形，展示面积分解过程

**场景三：勾股定理的应用**
* 核心内容：展示勾股定理在实际生活中的应用
* 教学目标：让观众掌握如何在实际问题中应用勾股定理
* 视觉方向提示：展示实际场景的数学建模过程
  `;
  const sceneContent = `
**场景标题**：勾股定理的发现
**核心内容**：介绍勾股定理的基本概念，展示直角三角形的三边关系，通过具体的数值例子让观众理解a²+b²=c²的含义
**教学目标**：让观众理解勾股定理的基本表述和几何意义，能够识别直角三角形并知道三边的关系
**视觉元素**：直角三角形、边长标注、公式文本、坐标系
**动画流程**：先显示坐标系，然后绘制直角三角形，标注边长，最后显示公式
  `;
  const sceneNumber = 1;

  const prompt = getPromptSceneVisionStoryboard(
    sceneNumber,
    topic,
    description,
    sceneOutline,
    sceneContent,
    ['manim-physics', 'manim-slides'] // relevantPlugins 参数示例
  );

  console.log('生成的故事板提示词:');
  console.log('---');
  console.log(prompt);
  console.log('---\n');

  // 检查提示词是否包含关键元素
  const checks = [
    { name: '包含主题', test: prompt.includes(topic) },
    { name: '包含描述', test: prompt.includes(description) },
    { name: '包含场景内容', test: prompt.includes('勾股定理的发现') },
    { name: '包含场景编号', test: prompt.includes('场景 1') },
    { name: '包含技术规格', test: prompt.includes('16:9') },
    { name: '包含坐标范围', test: prompt.includes('[-7.1, 7.1]') },
    { name: '包含安全区域', test: prompt.includes('安全区域') },
    {
      name: '包含输出格式标签',
      test: prompt.includes('<SCENE_VISION_STORYBOARD_PLAN>'),
    },
    { name: '包含开场设定', test: prompt.includes('**1. 开场设定**') },
    { name: '包含核心元素清单', test: prompt.includes('**2. 核心元素清单**') },
    { name: '包含动画时间轴', test: prompt.includes('**3. 动画时间轴**') },
    { name: '包含文本与公式', test: prompt.includes('**4. 文本与公式展示**') },
    { name: '包含重点强调', test: prompt.includes('**5. 重点强调策略**') },
    { name: '包含场景转换', test: prompt.includes('**6. 场景转换**') },
    { name: '包含技术实现提示', test: prompt.includes('**技术实现提示**') },
    { name: '包含质量检查', test: prompt.includes('**质量检查要求**') },
    { name: '包含LaTeX示例', test: prompt.includes('$a^2 + b^2 = c^2$') },
    { name: '包含坐标示例', test: prompt.includes('A(0,0), B(2,0), C(1,1.5)') },
  ];

  console.log('提示词检查结果:');
  checks.forEach((check) => {
    console.log(`${check.test ? '✅' : '❌'} ${check.name}`);
  });

  const passedChecks = checks.filter((check) => check.test).length;
  console.log(`\n通过检查: ${passedChecks}/${checks.length}`);

  if (passedChecks === checks.length) {
    console.log('🎉 所有检查都通过了！');
  } else {
    console.log('⚠️  有些检查未通过，请检查提示词。');
  }

  console.log('\n=== 测试完成 ===');
}

// 运行测试
if (require.main === module) {
  testStoryboardPrompt();
}

module.exports = {
  testStoryboardPrompt,
};
