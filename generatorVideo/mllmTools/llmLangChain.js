const { ChatOpenAI } = require('@langchain/openai');
const { ChatDeepSeek } = require('@langchain/deepseek');
const {
  BaseChatModel,
} = require('@langchain/core/language_models/chat_models');
const { AIMessage } = require('@langchain/core/messages');
const fs = require('fs');
const mime = require('mime-types');
const axios = require('axios');
// 移除 sharp 依赖
// const sharp = require('sharp')

/**
 * 根据文件扩展名获取文件的 MIME 类型
 *
 * @param {string} filePath - 文件路径
 * @returns {string} MIME 类型字符串（例如 "image/jpeg", "audio/mp3"）
 * @private
 */
const _getMimeType = (filePath) => {
  const mimeType = mime.lookup(filePath);
  if (!mimeType) {
    throw new Error(`不支持的文件类型: ${filePath}`);
  }
  return mimeType;
};

/**
 * 将本地文件或图像对象编码为 base64 字符串
 *
 * @param {string|Object} filePath - 本地文件路径或图像对象
 * @returns {Promise<string>} Base64 编码的文件字符串
 * @private
 */
const _encodeFile = async (filePath) => {
  if (typeof filePath === 'object' && filePath.constructor.name === 'Image') {
    // 处理图像对象 - 不再使用sharp
    // 假设图像对象有buffer或data属性
    if (filePath.buffer) {
      return Buffer.from(filePath.buffer).toString('base64');
    } else if (filePath.data) {
      return Buffer.from(filePath.data).toString('base64');
    } else {
      throw new Error('图像对象缺少buffer或data属性');
    }
  } else {
    // 处理文件路径
    return fs.readFileSync(filePath).toString('base64');
  }
};
const DeepSeekHandler = ({ temperature, verbose }) => {
  const model = new ChatDeepSeek({
    model: 'deepseek-reasoner',
    apiKey: '***********************************',
    temperature,
    verbose,
    // other params...
  });
  return model;
};
const OpenAIHandler = ({ temperature, verbose }) => {
  const model = new ChatOpenAI({ model: 'gpt-4o-mini', temperature, verbose });
  return model;
};

// TAL内部API Handler - 使用自定义实现
class ChatTAL extends BaseChatModel {
  constructor(fields) {
    super(fields || {});
    this.apiKey = '1000081419:12c15911e39307c7191b55598baa6154';
    this.model = 'gpt-4o';
    this.baseURL = 'http://ai-service.tal.com/openai-compatible/v1';
    this.temperature = fields?.temperature || 0.7;
    this.verbose = fields?.verbose || false;
  }

  _llmType() {
    return 'tal-openai';
  }

  async _generate(messages, options = {}) {
    if (this.verbose) {
      console.log('Invoking TAL model:', this.model);
      console.log('Messages:', JSON.stringify(messages, null, 2));
    }

    // 将 LangChain 消息格式转换为 OpenAI API 格式
    const formattedMessages = messages.map((message) => {
      if (message._getType() === 'human') {
        return { role: 'user', content: message.content };
      } else if (message._getType() === 'ai') {
        return { role: 'assistant', content: message.content };
      } else if (message._getType() === 'system') {
        return { role: 'system', content: message.content };
      } else {
        return { role: 'user', content: message.content };
      }
    });

    // 构建请求体
    const requestBody = {
      model: this.model,
      messages: formattedMessages,
      temperature: this.temperature,
    };

    try {
      const response = await axios.post(
        `${this.baseURL}/chat/completions`,
        requestBody,
        {
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (this.verbose) {
        console.log('TAL response:', JSON.stringify(response.data, null, 2));
      }

      const message = new AIMessage(response.data.choices[0].message.content);
      return {
        generations: [
          {
            message,
            text: message.content,
          },
        ],
      };
    } catch (error) {
      console.error('TAL API error:', error.response?.data || error.message);
      throw error;
    }
  }
}

const TALHandler = ({ temperature, verbose }) => {
  return new ChatTAL({
    temperature,
    verbose,
  });
};

function getHandler(model, mapping) {
  const patterns = Object.keys(mapping);
  const handlerKey = patterns.find((pattern) => {
    const regex = new RegExp(`${pattern}`, 'g');
    return model.match(regex);
  });
  if (!handlerKey) {
    return null;
  }
  return mapping[handlerKey];
}
// 添加 DashScope 处理器，继承 BaseChatModel
class ChatDashScope extends BaseChatModel {
  constructor(fields) {
    super(fields || {});
    this.apiKey = fields?.apiKey || process.env.DASHSCOPE_API_KEY;
    this.model = fields?.model || 'qwen-max-latest';
    this.temperature = fields?.temperature || 0.7;
    this.verbose = fields?.verbose || false;
    this.stream = fields?.stream || false; // 添加流式处理选项

    if (!this.apiKey) {
      throw new Error(
        "DashScope API key not found. Please set the DASHSCOPE_API_KEY environment variable or pass the key into 'apiKey' field."
      );
    }
  }

  _llmType() {
    return 'dashscope';
  }

  async _generate(messages, options = {}) {
    if (this.verbose) {
      console.log('Invoking DashScope model:', this.model);
      console.log('Messages:', JSON.stringify(messages, null, 2));
    }

    // 将 LangChain 消息格式转换为 DashScope API 格式
    const formattedMessages = messages.map((message) => {
      if (message._getType() === 'human') {
        return { role: 'user', content: message.content };
      } else if (message._getType() === 'ai') {
        return { role: 'assistant', content: message.content };
      } else if (message._getType() === 'system') {
        return { role: 'system', content: message.content };
      } else {
        return { role: 'user', content: message.content };
      }
    });

    // 构建请求体
    const requestBody = {
      model: this.model,
      messages: formattedMessages,
      temperature: this.temperature,
    };

    // 如果启用流式模式，添加相关参数
    if (this.stream) {
      requestBody.stream = true;
      requestBody.stream_options = {
        include_usage: true,
      };
    }

    try {
      if (this.stream) {
        // 流式处理逻辑
        const response = await axios.post(
          'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions',
          requestBody,
          {
            headers: {
              Authorization: `Bearer ${this.apiKey}`,
              'Content-Type': 'application/json',
            },
            responseType: 'stream',
          }
        );

        // 收集完整响应
        let fullContent = '';

        // 处理流式响应
        return new Promise((resolve, reject) => {
          response.data.on('data', (chunk) => {
            const lines = chunk
              .toString()
              .split('\n')
              .filter((line) => line.trim() !== '');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const jsonStr = line.slice(6);
                if (jsonStr === '[DONE]') continue;

                try {
                  const json = JSON.parse(jsonStr);
                  if (
                    json.choices &&
                    json.choices[0] &&
                    json.choices[0].delta &&
                    json.choices[0].delta.content
                  ) {
                    const content = json.choices[0].delta.content;
                    fullContent += content;

                    if (this.verbose) {
                      process.stdout.write(content);
                    }
                  }
                } catch (e) {
                  console.error('解析流式响应时出错:', e);
                }
              }
            }
          });

          response.data.on('end', () => {
            if (this.verbose) {
              console.log('\n流式响应结束');
            }

            const message = new AIMessage(fullContent);
            resolve({
              generations: [
                {
                  message,
                  text: message.content,
                },
              ],
            });
          });

          response.data.on('error', (err) => {
            reject(err);
          });
        });
      } else {
        // 非流式处理逻辑（原有代码）
        const response = await axios.post(
          'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions',
          requestBody,
          {
            headers: {
              Authorization: `Bearer ${this.apiKey}`,
              'Content-Type': 'application/json',
            },
          }
        );

        if (this.verbose) {
          console.log(
            'DashScope response:',
            JSON.stringify(response.data, null, 2)
          );
        }

        // 返回符合 LangChain 格式的响应
        const message = new AIMessage(response.data.choices[0].message.content);
        return {
          generations: [
            {
              message,
              text: message.content,
            },
          ],
        };
      }
    } catch (error) {
      console.error(
        'DashScope API error:',
        error.response?.data || error.message
      );
      throw error;
    }
  }
}

// 更新 DashScopeHandler 以支持流式模式
const DashScopeHandler = ({ temperature, verbose, stream }) => {
  return new ChatDashScope({
    model: 'qwen-plus',
    temperature,
    verbose,
    stream,
    apiKey: 'sk-b70be8c61fdb497895f038fb3662c9bb',
  });
};

// 添加 QwenQwqHandler 专门用于 qwq-32b 模型
const QwenQwqHandler = ({ temperature, verbose, stream }) => {
  return new ChatDashScope({
    model: 'qwen-plus-latest',
    temperature,
    verbose,
    stream: true, // 默认启用流式模式
    apiKey: 'sk-b70be8c61fdb497895f038fb3662c9bb',
  });
};

// 添加 Claude 处理器，继承 BaseChatModel
class ChatClaude extends BaseChatModel {
  constructor(fields) {
    super(fields || {});
    this.apiKey = fields?.apiKey || process.env.ANTHROPIC_API_KEY;
    this.model = fields?.model || 'claude-3-7-sonnet-20250219';
    this.temperature = fields?.temperature || 0.7;
    this.verbose = fields?.verbose || false;
    this.stream = fields?.stream || false;

    if (!this.apiKey) {
      throw new Error(
        "Anthropic API key not found. Please set the ANTHROPIC_API_KEY environment variable or pass the key into 'apiKey' field."
      );
    }
  }

  _llmType() {
    return 'claude';
  }

  async _generate(messages, options = {}) {
    if (this.verbose) {
      console.log('Invoking Claude model:', this.model);
      console.log('Messages:', JSON.stringify(messages, null, 2));
    }

    // 将 LangChain 消息格式转换为 Anthropic API 格式
    const formattedMessages = messages.map((message) => {
      if (message._getType() === 'human') {
        return { role: 'user', content: message.content };
      } else if (message._getType() === 'ai') {
        return { role: 'assistant', content: message.content };
      } else if (message._getType() === 'system') {
        return { role: 'system', content: message.content };
      } else {
        return { role: 'user', content: message.content };
      }
    });

    // 构建请求体
    const requestBody = {
      model: this.model,
      messages: formattedMessages,
      temperature: this.temperature,
      max_tokens: 4096,
    };

    try {
      if (this.stream) {
        // 流式处理逻辑
        const response = await axios.post(
          'https://sg.uiuiapi.com/v1/chat/completions',
          requestBody,
          {
            headers: {
              Authorization: 'Bearer ' + this.apiKey,
              'content-type': 'application/json',
            },
            responseType: 'stream',
          }
        );

        // 收集完整响应
        let fullContent = '';

        // 处理流式响应
        return new Promise((resolve, reject) => {
          response.data.on('data', (chunk) => {
            const lines = chunk
              .toString()
              .split('\n')
              .filter((line) => line.trim() !== '');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const jsonStr = line.slice(6);
                if (jsonStr === '[DONE]') continue;

                try {
                  const json = JSON.parse(jsonStr);
                  if (
                    json.type === 'content_block_delta' &&
                    json.delta &&
                    json.delta.text
                  ) {
                    const content = json.delta.text;
                    fullContent += content;

                    if (this.verbose) {
                      process.stdout.write(content);
                    }
                  }
                } catch (e) {
                  console.error('解析流式响应时出错:', e);
                }
              }
            }
          });

          response.data.on('end', () => {
            if (this.verbose) {
              console.log('\n流式响应结束');
            }

            const message = new AIMessage(fullContent);
            resolve({
              generations: [
                {
                  message,
                  text: message.content,
                },
              ],
            });
          });

          response.data.on('error', (err) => {
            reject(err);
          });
        });
      } else {
        // 非流式处理逻辑
        const response = await axios.post(
          'https://sg.uiuiapi.com/v1/chat/completions',
          requestBody,
          {
            headers: {
              Authorization: 'Bearer ' + this.apiKey,
              'content-type': 'application/json',
            },
          }
        );

        if (this.verbose) {
          console.log(
            'Claude response:',
            JSON.stringify(response.data, null, 2)
          );
        }
        const text = response.data.choices[0].message.content;

        // 检查响应结构是否符合预期
        // 返回符合 LangChain 格式的响应
        const message = new AIMessage(text);
        return {
          generations: [
            {
              message,
              text: message.content,
            },
          ],
        };
      }
    } catch (error) {
      console.error('Claude API error:', error.response?.data || error.message);
      throw error;
    }
  }
}

// 添加 Claude 处理器
const ClaudeHandler = ({ temperature, verbose, stream }) => {
  return new ChatClaude({
    model: 'claude-3-7-sonnet-20250219',
    // model: "claude-3-5-sonnet-20241022",
    temperature,
    verbose,
    stream,
    apiKey: 'sk-Adh0zjvnNdJTomP7eJSFUEERslJzTOk4BifspmjC3AnmvhYw',
  });
};
const miniHandler = ({ temperature, verbose, stream }) => {
  return new ChatClaude({
    // model: "claude-3-7-sonnet-20250219",
    model: 'gpt-4o-mini',
    temperature,
    verbose,
    stream,
    apiKey: 'sk-Adh0zjvnNdJTomP7eJSFUEERslJzTOk4BifspmjC3AnmvhYw',
  });
};

// 更新模型处理器映射
const MODEL_HANDLER_MAPPINGS = {
  'gpt-4o': TALHandler, // 直接支持gpt-4o模型名 - 必须放在 "gpt-" 之前
  'tal-gpt': TALHandler, // 添加TAL内部API支持
  'gpt-': OpenAIHandler,
  'openai/': OpenAIHandler,
  deepseek: DeepSeekHandler,
  'qwen-': DashScopeHandler,
  'qwq-': QwenQwqHandler,
  'claude-': ClaudeHandler, // 添加 Claude 模型的处理器
  'mini-': miniHandler, // 添加 Claude 模型的处理器
};

const getModel = (modelName, temperature, verbose, stream) => {
  const handler = getHandler(modelName, MODEL_HANDLER_MAPPINGS);
  if (!handler) {
    throw new Error(`No handler found for model ${modelName}`);
  }
  const model = handler({
    temperature,
    verbose,
    stream,
  });
  return model;
};

const llmLangChainWrapper = ({
  modelName,
  temperature,
  // print_cost,
  verbose,
  stream,
  // use_langfuse
}) => {
  return async (messages, metadata = {}) => {
    const model = getModel(modelName, temperature, verbose, stream);
    if (!metadata) {
      console.log('未提供元数据，使用空元数据');
      metadata = {};
    }
    metadata.trace_name = `litellm-completion-${modelName}`;

    const formattedMessages = [];
    for (const msg of messages) {
      if (msg.type === 'text') {
        formattedMessages.push({
          role: msg.role || 'user', // 添加role检查，如果没有提供则默认为user
          content: msg.content, // 对于文本消息，直接使用content字符串
        });
      } else if (['image', 'audio', 'video'].includes(msg.type)) {
        // 检查内容是否为本地文件路径或图像对象
        let dataUrl;

        if (typeof msg.content === 'object' || fs.existsSync(msg.content)) {
          try {
            let mimeType;
            if (typeof msg.content === 'object') {
              mimeType = 'image/png';
            } else {
              mimeType = _getMimeType(msg.content);
            }

            const base64Data = await _encodeFile(msg.content);
            dataUrl = `data:${mimeType};base64,${base64Data}`;
          } catch (e) {
            console.log(`处理文件 ${msg.content} 时出错: ${e}`);
            continue;
          }
        } else {
          dataUrl = msg.content;
        }

        // 根据模型附加格式化的消息
        if (modelName.includes('gemini')) {
          formattedMessages.push({
            role: 'user',
            content: [
              {
                type: 'image_url',
                image_url: dataUrl,
              },
            ],
          });
        } else if (modelName.includes('gpt')) {
          // GPT 和其他模型需要不同的格式
          if (msg.type === 'image') {
            // GPT 中图像和视频的默认格式
            formattedMessages.push({
              role: 'user',
              content: [
                {
                  type: 'image_url', // 修正类型名称
                  image_url: {
                    // 修正字段名称
                    url: dataUrl,
                    detail: 'high',
                  },
                },
              ],
            });
          } else {
            throw new Error('对于 GPT，仅支持文本和图像推理');
          }
        } else {
          throw new Error('目前仅支持 Gemini 和 GPT 的多模态能力');
        }
      }
    }
    const response = await model.invoke(formattedMessages, metadata);
    return response.content;
  };
};

// 修改导出方式
module.exports = {
  getHandler,
  MODEL_HANDLER_MAPPINGS,
  llmLangChainWrapper,
};
