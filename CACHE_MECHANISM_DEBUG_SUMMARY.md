# 缓存机制调试总结

## 🚨 问题描述

用户发现每次输入相同的主题和描述都会调用AI接口，而不是使用数据库缓存，这导致：
- 重复的API调用和费用
- 响应时间变慢
- 用户体验不佳

## 🔍 缓存机制分析

### **现有缓存逻辑**

1. **查找缓存**: `findSessionId(topic, description)`
   - 在`topic`表中查找完全匹配的记录
   - 返回对应的`session_id`

2. **使用缓存**: 如果找到sessionId
   - 查询`topic_scene`表获取场景大纲
   - 直接返回缓存结果，不调用AI

3. **生成新内容**: 如果没找到缓存
   - 调用AI生成新的场景大纲
   - 保存到数据库供下次使用

### **可能的问题原因**

1. **字符串匹配严格**: 数据库使用精确匹配，任何细微差异都会导致缓存失效
   - 前后空格
   - 标点符号差异
   - 大小写差异

2. **数据库查询失败**: 可能存在连接或查询错误

3. **日志不足**: 无法确定缓存是否真的被查询和命中

## ✅ 调试改进

### **文件**: `generatorVideo/app.js`

### **1. 增强缓存查询日志**
**位置**: 第57-61行
```javascript
console.log('🔍 缓存查询开始 - topic:', topic, 'description:', description);
const sessionId = await findSessionId(topic, description);
console.log('🔍 缓存查询结果 - sessionId:', sessionId);
```

### **2. 增强数据库查询日志**
**位置**: 第18-30行
```javascript
const findSessionId = async (topic, description) => {
  console.log('🔍 数据库查询 - 查找缓存:', { topic, description });
  const result = await db.query(
    'SELECT session_id FROM topic WHERE topic =? AND description =?',
    [topic, description]
  );
  console.log('🔍 数据库查询结果:', 
    result.length > 0 ? `找到sessionId: ${result[0].session_id}` : '未找到缓存'
  );
  return result.length > 0 ? result[0].session_id : null;
};
```

### **3. 添加缓存未命中日志**
**位置**: 第89行
```javascript
console.log('❌ 缓存未命中，开始生成新的场景大纲');
```

## 🧪 测试工具

创建了 `test_cache_mechanism.js` 测试脚本：

### **测试内容**:
1. **第一次请求**: 生成新内容，记录响应时间
2. **第二次请求**: 相同输入，检查是否使用缓存
3. **差异测试**: 测试输入细微差异的影响

### **使用方法**:
```bash
node test_cache_mechanism.js
```

### **预期结果**:
- 第一次请求：较长响应时间，生成新sessionId
- 第二次请求：快速响应，相同sessionId
- 差异测试：显示缓存匹配的严格程度

## 🔧 可能的解决方案

### **方案1: 输入标准化**
```javascript
const normalizeInput = (str) => {
  return str.trim().toLowerCase().replace(/\s+/g, ' ');
};

const findSessionId = async (topic, description) => {
  const normalizedTopic = normalizeInput(topic);
  const normalizedDescription = normalizeInput(description);
  // 查询数据库...
};
```

### **方案2: 模糊匹配**
```javascript
// 使用LIKE查询或相似度算法
const result = await db.query(
  'SELECT session_id FROM topic WHERE TRIM(topic) = TRIM(?) AND TRIM(description) = TRIM(?)',
  [topic, description]
);
```

### **方案3: 哈希缓存键**
```javascript
const crypto = require('crypto');
const getCacheKey = (topic, description) => {
  const normalized = `${topic.trim()}|${description.trim()}`;
  return crypto.createHash('md5').update(normalized).digest('hex');
};
```

## 🚀 调试步骤

1. **重启后端服务**: 让新的日志生效
2. **运行测试脚本**: `node test_cache_mechanism.js`
3. **查看控制台日志**: 观察缓存查询过程
4. **检查数据库**: 确认数据是否正确保存

### **关键日志标识**:
- 🔍 缓存查询相关
- 🎯 缓存命中
- ❌ 缓存未命中

## 📊 预期改进效果

**调试后应该看到**:
- 详细的缓存查询日志
- 明确的缓存命中/未命中信息
- 数据库查询结果的详细信息

**如果缓存正常工作**:
- 第一次请求：生成新内容（较慢）
- 第二次请求：使用缓存（快速）
- 响应时间有明显差异

**如果缓存有问题**:
- 每次都显示"缓存未命中"
- 响应时间没有改善
- 需要进一步排查原因

现在可以通过详细的日志来确定缓存机制是否正常工作了！
