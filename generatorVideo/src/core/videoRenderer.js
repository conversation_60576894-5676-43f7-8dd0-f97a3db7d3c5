const fs = require("fs");
const path = require("path");
const { exec, execSync } = require("child_process");
const util = require("util");
const ffmpeg = require("fluent-ffmpeg");
// const { createReadStream, createWriteStream } = require('fs')
// const readline = require('readline')
const tqdm = require("tqdm");

// const { getImagesFromVideo, imageWithMostNonBlackSpace } = require('./parseVideo')

// 将 exec 转换为 Promise 版本
const execPromise = util.promisify(exec);

// 将 export class 改为 class
class VideoRenderer {
  /**
   * 用于渲染和组合 Manim 动画视频的类
   */
  constructor({ outputDir, printResponse = false, useVisualFixCode = false }) {
    /**
     * 初始化 VideoRenderer
     *
     * @param {string} outputDir - 输出文件目录，默认为 "output"
     * @param {boolean} printResponse - 是否打印响应，默认为 false
     * @param {boolean} useVisualFixCode - 是否使用视觉修复代码，默认为 false
     */
    this.outputDir = outputDir;
    this.printResponse = printResponse;
    this.useVisualFixCode = useVisualFixCode;
  }

  async renderScene({
    code,
    sceneNum,
    currVersion,
    codeDir,
    mediaDir,
    maxRetries = 3,
    // useVisualFixCode = false,
    // visualSelfReflectionFunc = null,
    // bannedReasonings = null,
    // sceneTraceId = null,
    // topic = null,
    // sessionId = null
  }) {
    /**
     * 渲染单个场景并处理错误重试和视觉修复
     *
     * @param {string} code - 要渲染的 Manim 代码
     * @param {string} filePrefix - 输出文件前缀
     * @param {number} sceneNum - 当前场景编号
     * @param {number} currVersion - 当前版本编号
     * @param {string} codeDir - 代码文件目录
     * @param {string} mediaDir - 媒体输出目录
     * @param {number} maxRetries - 最大重试次数，默认为 3
     * @param {boolean} useVisualFixCode - 是否使用视觉修复代码，默认为 false
     * @param {Function} visualSelfReflectionFunc - 视觉自我反思函数，默认为 null
     * @param {Array} bannedReasonings - 禁止推理字符串列表，默认为 null
     * @param {string} sceneTraceId - 场景跟踪标识符，默认为 null
     * @param {string} topic - 主题名称，默认为 null
     * @param {string} sessionId - 会话标识符，默认为 null
     *
     * @returns {Array} [code, errorMessage] 其中 errorMessage 在成功时为 null
     */
    let retries = 0;

    while (retries < maxRetries) {
      try {
        // 在单独的进程中执行 manim 以防止阻塞
        const filePath = path.join(
          codeDir,
          `scene${sceneNum}_v${currVersion}.py`
        );
        console.log(`xxr： 执行manim生成动画视频,${mediaDir}`);
        // 使用 Promise 执行命令
        // 修改执行命令，添加 PYTHONPATH 环境变量
        const projectRoot = path.resolve(__dirname, "../../../");
        const { stderr } = await execPromise(
          `PYTHONPATH=${projectRoot} manim -qh "${filePath}" --media_dir "${mediaDir}" --progress_bar none`,
          { env: { ...process.env } }
        );

        // 如果命令执行失败，抛出异常
        if (stderr && stderr.includes("Error")) {
          throw new Error(stderr);
        }
        // todo
        // if (useVisualFixCode && visualSelfReflectionFunc && bannedReasonings) {
        //   // 获取渲染的视频路径
        //   const videoPath = path.join(
        //     mediaDir,
        //     'videos',
        //     `${filePrefix}_scene${currScene}_v${currVersion}.mp4`
        //   )

        //   // 对于 Gemini/Vertex AI 模型，直接传递视频
        //   let mediaInput
        //   if (
        //     this.sceneModel &&
        //     (this.sceneModel.modelName.startsWith('gemini/') ||
        //       this.sceneModel.modelName.startsWith('vertex_ai/'))
        //   ) {
        //     mediaInput = videoPath
        //   } else {
        //     // 对于其他模型，使用图像快照
        //     mediaInput = this.createSnapshotScene(topic, currScene, currVersion, 'path')
        //   }

        //   const [newCode, log] = await visualSelfReflectionFunc(code, mediaInput, {
        //     sceneTraceId: sceneTraceId,
        //     topic: topic,
        //     sceneNumber: currScene,
        //     sessionId: sessionId
        //   })

        //   fs.writeFileSync(
        //     path.join(codeDir, `${filePrefix}_scene${currScene}_v${currVersion}_vfix_log.txt`),
        //     log
        //   )

        //   // 检查终止标记
        //   if (
        //     newCode.includes('<LGTM>') ||
        //     bannedReasonings.some((word) => newCode.includes(word))
        //   ) {
        //     break
        //   }

        //   code = newCode
        //   currVersion += 1
        //   fs.writeFileSync(
        //     path.join(codeDir, `${filePrefix}_scene${currScene}_v${currVersion}.py`),
        //     code
        //   )
        //   console.log(
        //     `Code saved to scene${currScene}/code/${filePrefix}_scene${currScene}_v${currVersion}.py`
        //   )
        //   retries = 0
        //   continue
        // }
        console.log(`xxr: 执行manim生成动画成功`);

        break; // 成功时退出重试循环
      } catch (e) {
        console.log(`Error: ${e.message}`);
        console.log(`Retrying ${retries + 1} of ${maxRetries}...`);

        fs.appendFileSync(
          path.join(codeDir, `scene${sceneNum}_v${currVersion}_error.log`),
          `\nError in attempt ${retries}:\n${e.message}\n`
        );
        retries += 1;
        console.log(`xxr: 执行manim生成动画失败`);
        return [code, e.message]; // 表示失败并返回错误消息
      }
    }

    console.log(
      `Successfully rendered ${path.join(
        codeDir,
        `scene${sceneNum}_v${currVersion}.py`
      )}`
    );
    // fs.writeFileSync(
    //   path.join(this.outputDir, `scene${sceneNum}`, "succ_rendered.txt"),
    //   ""
    // );

    return [code, null]; // 表示成功
  }

  runManimProcess(topic) {
    /**
     * 对特定主题的所有生成的 manim 代码运行 manim
     *
     * @param {string} topic - 要处理的主题名称
     * @returns {Object} 最终 manim 进程的结果
     */
    let filePrefix = topic.toLowerCase();
    filePrefix = filePrefix.replace(/[^a-z0-9_]+/g, "_");
    const searchPath = path.join(this.outputDir, filePrefix);

    // 遍历场景文件夹
    const sceneFolders = fs
      .readdirSync(searchPath)
      .filter((f) => fs.statSync(path.join(searchPath, f)).isDirectory())
      .sort(); // 按顺序处理场景

    let result;

    for (const folder of sceneFolders) {
      const folderPath = path.join(searchPath, folder);

      // 按版本顺序获取所有 Python 文件
      const pyFiles = fs
        .readdirSync(folderPath)
        .filter((f) => f.endsWith(".py"))
        .sort((a, b) => {
          const versionA = parseInt(a.split("_v").pop().split(".")[0]);
          const versionB = parseInt(b.split("_v").pop().split(".")[0]);
          return versionA - versionB;
        });

      for (const file of pyFiles) {
        const filePath = path.join(folderPath, file);
        try {
          const mediaDir = path.join(this.outputDir, filePrefix, "media");
          result = execSync(
            `manim -qh "${filePath}" --media_dir "${mediaDir}"`,
            {
              encoding: "utf8",
            }
          );
          console.log(`Successfully rendered ${file}`);
          break; // 成功时移至下一个场景文件夹
        } catch (e) {
          console.log(`Error rendering ${file}: ${e.message}`);
          const errorLogPath = path.join(
            folderPath,
            `${file.split(".")[0]}_error.log`
          );
          fs.writeFileSync(errorLogPath, `Error:\n${e.message}\n`);
          console.log(`Error log saved to ${errorLogPath}`);
        }
      }
    }

    return result;
  }

  // createSnapshotScene(topic, sceneNumber, versionNumber, returnType = 'image') {
  //   /**
  //    * 为特定主题和场景创建视频快照
  //    *
  //    * @param {string} topic - 主题名称
  //    * @param {number} sceneNumber - 场景编号
  //    * @param {number} versionNumber - 版本编号
  //    * @param {string} returnType - 返回值类型 - "path" 或 "image"，默认为 "image"
  //    *
  //    * @returns {string|Image} 保存的图像路径或 Image 对象
  //    * @throws {Error} 如果在视频文件夹中找不到 mp4 文件
  //    */
  //   let filePrefix = topic.toLowerCase()
  //   filePrefix = filePrefix.replace(/[^a-z0-9_]+/g, '_')
  //   const searchPath = path.join(this.outputDir, filePrefix)
  //   const videoFolderPath = path.join(
  //     searchPath,
  //     'media',
  //     'videos',
  //     `${filePrefix}_scene${sceneNumber}_v${versionNumber}`,
  //     '1080p60'
  //   )

  //   fs.mkdirSync(videoFolderPath, { recursive: true })
  //   const snapshotPath = path.join(videoFolderPath, 'snapshot.png')

  //   // 从视频文件夹路径获取 mp4 视频文件
  //   const videoFiles = fs.readdirSync(videoFolderPath).filter((f) => f.endsWith('.mp4'))

  //   if (videoFiles.length === 0) {
  //     throw new Error(`No mp4 files found in ${videoFolderPath}`)
  //   }

  //   const videoPath = path.join(videoFolderPath, videoFiles[0])
  //   const savedImage = imageWithMostNonBlackSpace(
  //     getImagesFromVideo(videoPath),
  //     snapshotPath,
  //     returnType
  //   )

  //   return savedImage
  // }

  async combineVideos({ sessionId, sceneCount }) {
    /**
     * 使用 ffmpeg 组合特定主题的所有视频和字幕文件
     *
     * @param {string} topic - 要组合视频的主题名称
     *
     * 此函数将：
     * - 查找所有场景视频和字幕
     * - 组合有或没有音频的视频
     * - 合并具有正确时间的字幕文件
     * - 将组合的视频和字幕保存到输出目录
     */

    const searchPath = path.join(this.outputDir, sessionId, `media`, `videos`);

    // 如果输出目录不存在，则创建
    const videoOutputDir = path.join(this.outputDir, sessionId);
    fs.mkdirSync(videoOutputDir, { recursive: true });

    const outputVideoPath = path.join(
      videoOutputDir,
      `${sessionId}_combined.mp4`
    );
    const outputSrtPath = path.join(
      videoOutputDir,
      `${sessionId}_combined.srt`
    );

    if (fs.existsSync(outputVideoPath) && fs.existsSync(outputSrtPath)) {
      console.log(
        `Combined video and subtitles already exist at ${outputVideoPath}, not combining again.`
      );
      return true;
    }

    // 查找所有场景文件夹和视频
    const sceneFolders = [];
    const walkSync = (dir, filelist = []) => {
      fs.readdirSync(dir).forEach((file) => {
        const dirPath = path.join(dir, file);
        if (fs.statSync(dirPath).isDirectory()) {
          if (file.startsWith("scene")) {
            filelist.push(dirPath);
          } else {
            walkSync(dirPath, filelist);
          }
        }
      });
      return filelist;
    };

    walkSync(searchPath, sceneFolders);

    const sceneVideos = [];
    const sceneSubtitles = [];

    for (let sceneNum = 1; sceneNum <= sceneCount; sceneNum++) {
      const folders = sceneFolders.filter((f) => {
        const sceneMatch = f.match(/scene(\d+)/);
        return sceneMatch && parseInt(sceneMatch[1]) === sceneNum;
      });

      if (folders.length === 0) {
        console.log(`Warning: Missing scene ${sceneNum}`);
        continue;
      }

      // 按版本排序并获取最新版本
      folders.sort((a, b) => {
        const versionA = parseInt(a.split("_v").pop());
        const versionB = parseInt(b.split("_v").pop());
        return versionB - versionA;
      });

      const folder = folders[0];
      const p1080Path = path.join(folder, "1080p60");

      let videoFound = false;
      let subtitlesFound = false;

      if (fs.existsSync(p1080Path)) {
        fs.readdirSync(p1080Path).forEach((filename) => {
          if (filename.endsWith(".mp4")) {
            sceneVideos.push(path.join(p1080Path, filename));
            videoFound = true;
          } else if (filename.endsWith(".srt")) {
            sceneSubtitles.push(path.join(p1080Path, filename));
            subtitlesFound = true;
          }
        });
      }

      if (!videoFound) {
        console.log(`Warning: Missing video for scene ${sceneNum}`);
      }

      if (!subtitlesFound) {
        sceneSubtitles.push(null);
      }
    }

    if (sceneVideos.length !== sceneCount) {
      console.log(
        "Not all videos/subtitles are found, aborting video combination."
      );
      return;
    }

    try {
      console.log("Analyzing video streams...");

      // 检查视频是否有音频流
      const hasAudio = [];

      for (const video of tqdm(sceneVideos, {
        desc: "Checking audio streams",
      })) {
        const audioStreams = await new Promise((resolve) => {
          ffmpeg.ffprobe(video, (err, metadata) => {
            if (err) {
              console.error(`Error probing ${video}: ${err.message}`);
              resolve([]);
              return;
            }

            const audioStreams = metadata.streams.filter(
              (stream) => stream.codec_type === "audio"
            );
            resolve(audioStreams);
          });
        });

        hasAudio.push(audioStreams.length > 0);
      }

      console.log("Preparing video combination...");

      // 创建临时文件列表
      const tempListFile = path.join(videoOutputDir, "video_list.txt");
      // 使用绝对路径，确保 ffmpeg 能找到文件
      const tempListContent = sceneVideos
        .map((video) => `file '${path.resolve(video)}'`)
        .join("\n");
      fs.writeFileSync(tempListFile, tempListContent);

      // 打印调试信息
      console.log(`视频列表文件路径: ${tempListFile}`);
      console.log(`视频列表内容:\n${tempListContent}`);

      // 使用 ffmpeg 的 concat demuxer 组合视频
      console.log("Combining videos...");

      // 使用 execSync 直接运行 ffmpeg 命令，而不是 fluent-ffmpeg
      try {
        execSync(
          `ffmpeg -f concat -safe 0 -i "${tempListFile}" -c:v libx264 -preset veryfast -crf 28 -threads 0 -tune fastdecode -profile:v baseline -level 4.0 -movflags +faststart "${outputVideoPath}"`,
          {
            stdio: "inherit", // 显示输出到控制台
          }
        );
        console.log(`Successfully combined videos into ${outputVideoPath}`);
      } catch (error) {
        console.error(`Error combining videos: ${error.message}`);
        throw error; // 重新抛出错误以便后续处理
      }

      // 清理临时文件
      fs.unlinkSync(tempListFile);

      console.log(`Successfully combined videos into ${outputVideoPath}`);

      // 处理字幕组合
      if (sceneSubtitles.some(Boolean)) {
        console.log("Combining subtitles...");

        const outfile = fs.createWriteStream(outputSrtPath, {
          encoding: "utf-8",
        });
        let currentTimeOffset = 0;
        let subtitleIndex = 1;

        for (let i = 0; i < sceneSubtitles.length; i++) {
          const srtFile = sceneSubtitles[i];
          const videoFile = sceneVideos[i];

          if (!srtFile) {
            // 获取视频时长并更新偏移量
            const duration = await new Promise((resolve) => {
              ffmpeg.ffprobe(videoFile, (err, metadata) => {
                if (err) {
                  console.error(`Error probing ${videoFile}: ${err.message}`);
                  resolve(0);
                  return;
                }

                resolve(parseFloat(metadata.streams[0].duration || 0));
              });
            });

            currentTimeOffset += duration;
            continue;
          }

          const lines = fs.readFileSync(srtFile, "utf-8").split("\n");
          // 修改这里：使用不同的变量名 j 替代内层循环的 i
          let j = 0;

          while (j < lines.length) {
            const line = lines[j].trim();

            if (/^\d+$/.test(line)) {
              // 字幕索引
              outfile.write(`${subtitleIndex}\n`);
              subtitleIndex++;
              j++;

              // 时间码行
              const timeLine = lines[j].trim();
              const [startTime, endTime] = timeLine.split(" --> ");

              // 转换时间码并添加偏移量
              const adjustTime = (timeStr, offset) => {
                const [h, m, s] = timeStr.replace(",", ".").split(":");
                const totalSeconds =
                  parseFloat(h) * 3600 +
                  parseFloat(m) * 60 +
                  parseFloat(s) +
                  offset;
                const hours = Math.floor(totalSeconds / 3600);
                const minutes = Math.floor((totalSeconds % 3600) / 60);
                const seconds = totalSeconds % 60;
                return `${hours.toString().padStart(2, "0")}:${minutes
                  .toString()
                  .padStart(2, "0")}:${seconds
                  .toFixed(3)
                  .padStart(6, "0")}`.replace(".", ",");
              };

              const newStart = adjustTime(startTime, currentTimeOffset);
              const newEnd = adjustTime(endTime, currentTimeOffset);
              outfile.write(`${newStart} --> ${newEnd}\n`);
              j++;

              // 字幕文本（可能有多行）
              while (j < lines.length && lines[j].trim()) {
                outfile.write(lines[j] + "\n");
                j++;
              }
              outfile.write("\n");
            } else {
              j++;
            }
          }

          // 更新时间偏移量
          const duration = await new Promise((resolve) => {
            ffmpeg.ffprobe(videoFile, (err, metadata) => {
              if (err) {
                console.error(`Error probing ${videoFile}: ${err.message}`);
                resolve(0);
                return;
              }

              resolve(parseFloat(metadata.streams[0].duration || 0));
            });
          });

          currentTimeOffset += duration;
        }

        outfile.end();
        console.log(`Successfully combined subtitles into ${outputSrtPath}`);
      }
      return true;
    } catch (e) {
      console.log(`Error combining videos and subtitles: ${e.message}`);
      console.error(e.stack);
      return false;
    }
  }
}

// 添加 module.exports
module.exports = { VideoRenderer };
