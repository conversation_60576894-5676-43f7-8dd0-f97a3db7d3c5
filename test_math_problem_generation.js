const { getPromptCodeGeneration } = require('./generatorVideo/taskGenerator/index.js');

// 测试数学试题讲解的代码生成提示词
function testMathProblemGeneration() {
  console.log('=== 测试数学试题讲解代码生成 ===\n');

  const topic = '试题讲解';
  const description = '两支粗细、长短都不同的蜡烛，长的一支可以点4小时, 短的可以点6小时, 将它们同时点燃, 两小时后, 两支蜡烛所余下的长度正好相等．原来短蜡烛的长度是长蜡烛的（   ）．';
  const sceneOutline = `
**场景一：问题理解**
* 核心内容：展示题目，理解蜡烛燃烧问题的关键信息
* 教学目标：让学生理解题目中的数量关系
* 视觉方向提示：绘制两支不同长度的蜡烛

**场景二：建立方程**
* 核心内容：根据题目条件建立数学方程
* 教学目标：培养学生的数学建模能力
* 视觉方向提示：展示方程建立过程

**场景三：求解过程**
* 核心内容：解方程得出答案
* 教学目标：掌握解题方法和步骤
* 视觉方向提示：逐步展示计算过程
  `;
  const sceneContent = `
**场景标题**：问题理解
**核心内容**：展示蜡烛燃烧问题，分析已知条件和求解目标
**教学目标**：让学生理解题目中的时间、长度、燃烧速度等关键概念
**视觉元素**：两支蜡烛图示、时间轴、长度标注、燃烧过程动画
**动画流程**：先显示题目文本，然后绘制蜡烛示意图，标注关键数据，最后展示燃烧过程
  `;
  const sceneImplementation = `
**技术实现计划**：
1. 创建题目文本显示区域（上半部分）
2. 绘制两支不同长度的蜡烛（长蜡烛用蓝色，短蜡烛用红色）
3. 添加时间轴和长度标注
4. 动画展示燃烧过程（2小时后长度相等）
5. 高亮关键信息（4小时、6小时、2小时后相等）
  `;

  const prompt = getPromptCodeGeneration(
    topic,
    description,
    sceneOutline,
    sceneImplementation,
    1,
    ['manim-physics']
  );

  console.log('生成的代码生成提示词（前1000字符）:');
  console.log('---');
  console.log(prompt.substring(0, 1000) + '...');
  console.log('---\n');

  // 检查关键修复是否生效
  const checks = [
    { name: '背景设置为白色', test: prompt.includes('默认背景（白色）') || prompt.includes('Default background (WHITE)') },
    { name: '禁止白色文字', test: prompt.includes('不要对任何文本使用白色') || prompt.includes('PLEASE DO NOT use WHITE color for any text') },
    { name: '包含试题讲解专用要求', test: prompt.includes('试题讲解专用要求') },
    { name: '包含安全区域定义', test: prompt.includes('X轴[-6.5, 6.5]，Y轴[-3.5, 3.5]') },
    { name: '包含边界检查要求', test: prompt.includes('边界检查') },
    { name: '包含防重叠要求', test: prompt.includes('防重叠') },
    { name: '包含试题布局要求', test: prompt.includes('试题布局') },
    { name: '包含深色文字要求', test: prompt.includes('深色预定义颜色') },
    { name: '包含字体大小建议', test: prompt.includes('font_size=0.8') },
    { name: '包含颜色高亮建议', test: prompt.includes('不同颜色高亮') },
  ];

  console.log('修复效果检查:');
  checks.forEach(check => {
    console.log(`${check.test ? '✅' : '❌'} ${check.name}`);
  });

  const passedChecks = checks.filter(check => check.test).length;
  console.log(`\n通过检查: ${passedChecks}/${checks.length}`);

  if (passedChecks >= 8) {
    console.log('🎉 修复效果良好！');
  } else {
    console.log('⚠️  还需要进一步优化。');
  }

  console.log('\n=== 测试完成 ===');
}

// 运行测试
if (require.main === module) {
  testMathProblemGeneration();
}

module.exports = {
  testMathProblemGeneration,
};
