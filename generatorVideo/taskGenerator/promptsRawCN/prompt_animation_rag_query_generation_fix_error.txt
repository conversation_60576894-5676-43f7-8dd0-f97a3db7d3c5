您是Manim（社区版）及其插件的专家。您的任务是将Manim动画场景的完整实现计划转换为查询，可以用于从Manim核心和任何相关插件检索相关文档。实施计划将描述场景的愿景、技术实施和动画策略。
以下是文本解释（实施计划）作为背景：

{text_explanation}

错误信息将描述在运行Manim代码时遇到的问题。你的查询应该包含与特定Manim类、方法、函数和可能与错误相关的* *概念* *相关的关键字，包括任何特定于插件的功能。专注于从错误消息本身和产生错误的代码片段中提取核心概念、操作和词汇表。生成简洁的查询，针对Manim核心和相关插件文档的不同方面（类引用、方法使用、动画示例、概念解释）。
下面是错误消息和代码片段：

**Error Message:**
{error}

**Code Snippet:**
{code}

根据错误信息和代码片段，生成多个类似人类的查询（最多5-7个）来检索相关文档，以修复此错误。请确保搜索目标是不同的，以便RAG可以检索到涵盖错误的各个方面及其潜在解决方案的不同文档集。

**具体来说，要确保：**
1. 至少有1-2个查询侧重于检索可能导致错误的Manim **函数或类使用**信息。
2. 如果错误消息或代码建议使用插件功能，至少包含一个针对与错误相关的插件文档的查询。
3. 查询应该足够具体，以便在相关的情况下区分Manim核心功能和插件功能。

以以下格式输出查询：
[
    {{"query": "content of query 1", "type": "manim_core/name_of_the_plugin"}},
    {{"query": "content of query 2", "type": "manim_core/name_of_the_plugin"}},
    {{"query": "content of query 3", "type": "manim_core/name_of_the_plugin"}},
    {{"query": "content of query 4", "type": "manim_core/name_of_the_plugin"}},
    {{"query": "content of query 5", "type": "manim_core/name_of_the_plugin"}},
    {{"query": "content of query 6", "type": "manim_core/name_of_the_plugin"}},
    {{"query": "content of query 7", "type": "manim_core/name_of_the_plugin"}},
] 