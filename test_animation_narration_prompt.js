const { getPromptSceneAnimationNarration } = require('./generatorVideo/taskGenerator/index.js');

// 测试动画旁白提示词
function testAnimationNarrationPrompt() {
  console.log('=== 测试动画旁白提示词 ===\n');

  const topic = '勾股定理';
  const description = '通过动画演示勾股定理的概念、证明和应用';
  const sceneNum = 1;
  const sceneContent = `
**场景标题**：勾股定理的发现
**核心内容**：介绍勾股定理的基本概念，展示直角三角形的三边关系
**教学目标**：让观众理解勾股定理的基本表述和几何意义
**视觉元素**：直角三角形、边长标注、公式文本、坐标系
**动画流程**：先显示坐标系，然后绘制直角三角形，标注边长，最后显示公式
  `;
  const storyContent = `
**1. 开场设定**
- 场景开始时显示空白的坐标系
- 坐标系位于画布中心，X轴范围[-6, 6]，Y轴范围[-3, 3]

**2. 核心元素清单**
- 直角三角形：顶点A(0,0), B(3,0), C(0,4)
- 边长标注：a=4, b=3, c=5
- 公式文本：a²+b²=c²

**3. 动画时间轴**
- [0-5秒] 坐标系淡入显示
- [5-10秒] 直角三角形逐步绘制
- [10-15秒] 边长标注出现
- [15-20秒] 公式显示并高亮
  `;
  const sceneOutline = `
**场景一：勾股定理的发现**
* 核心内容：介绍勾股定理的基本概念
* 教学目标：理解勾股定理的基本表述
* 视觉方向提示：绘制直角三角形并标注

**场景二：勾股定理的证明**
* 核心内容：通过几何证明方法证明勾股定理
* 教学目标：理解定理的数学严谨性
* 视觉方向提示：构建正方形展示面积分解

**场景三：勾股定理的应用**
* 核心内容：展示实际应用场景
* 教学目标：掌握实际问题中的应用
* 视觉方向提示：实际场景的数学建模
  `;
  const relevantPlugins = ['manim-physics', 'manim-slides'];

  const prompt = getPromptSceneAnimationNarration({
    sceneNum,
    topic,
    description,
    sceneContent,
    storyContent,
    sceneOutline,
    relevantPlugins,
  });
  
  console.log('生成的动画旁白提示词:');
  console.log('---');
  console.log(prompt.substring(0, 800) + '...');
  console.log('---\n');

  // 检查提示词是否包含关键元素
  const checks = [
    { name: '包含主题', test: prompt.includes(topic) },
    { name: '包含描述', test: prompt.includes(description) },
    { name: '包含场景编号', test: prompt.includes('场景 1') || prompt.includes('场景1') },
    { name: '包含场景内容', test: prompt.includes('勾股定理的发现') },
    { name: '包含故事板内容', test: prompt.includes('开场设定') },
    { name: '包含场景大纲', test: prompt.includes('场景一') },
    { name: '包含插件信息', test: prompt.includes('manim-physics') },
    { name: '包含坐标系信息', test: prompt.includes('[-7.1, 7.1]') },
    { name: '包含输出格式标签', test: prompt.includes('<SCENE_ANIMATION_NARRATION_PLAN>') },
    { name: '包含动画策略', test: prompt.includes('[ANIMATION_STRATEGY]') },
    { name: '包含旁白部分', test: prompt.includes('[NARRATION]') },
    { name: '包含教学动画计划', test: prompt.includes('教学动画计划') },
    { name: '包含解说脚本', test: prompt.includes('教学解说脚本') },
  ];

  console.log('提示词检查结果:');
  checks.forEach(check => {
    console.log(`${check.test ? '✅' : '❌'} ${check.name}`);
  });

  const passedChecks = checks.filter(check => check.test).length;
  console.log(`\n通过检查: ${passedChecks}/${checks.length}`);

  if (passedChecks === checks.length) {
    console.log('🎉 所有检查都通过了！');
  } else {
    console.log('⚠️  有些检查未通过，请检查提示词。');
  }

  console.log('\n=== 测试完成 ===');
}

// 运行测试
if (require.main === module) {
  testAnimationNarrationPrompt();
}

module.exports = {
  testAnimationNarrationPrompt,
};
