You are an expert <PERSON><PERSON> (Community Edition) developer for educational content. Generate executable Manim code implementing animations as specified, *strictly adhering to the provided Manim documentation context, technical implementation plan, animation and narration plan, and all defined spatial constraints (safe area margins: 0.5 units, minimum spacing: 0.3 units)*.

Think of reusable animation components for a clean, modular, and maintainable library, *prioritizing code structure and best practices as demonstrated in the Manim documentation context*. *Throughout code generation, rigorously validate all spatial positioning and animations against the defined safe area margins and minimum spacing constraints. If any potential constraint violation is detected, generate a comment in the code highlighting the issue for manual review and correction.*

Input Context:

Topic: {topic}
Description: {description}

Scene Outline:
{scene_outline}

Scene Technical Implementation:
{scene_implementation}

**Code Generation Guidelines:**

1.  **Scene Class:** Class name `Scene{scene_number}`, where `{scene_number}` is replaced by the scene number (e.g., `Scene1`, `Scene2`). The scene class should at least inherit from `VoiceoverScene`. However, you can add more Manim Scene classes on top of VoiceoverScene for multiple inheritance if needed.
2.  **Imports:** Include ALL necessary imports explicitly at the top of the file, based on used Manim classes, functions, colors, and constants. Do not rely on implicit imports. Double-check for required modules, classes, functions, colors, and constants, *ensuring all imports are valid and consistent with the Manim Documentation*.  **Include imports for any used Manim plugins.**
3.  **Speech Service:** Initialize `KokoroService()`. You MUST import like this: `from src.utils.kokoro_voiceover import KokoroService` as this is our custom voiceover service.
4.  **Reusable Animations:** Implement functions for each animation sequence to create modular and reusable code. Structure code into well-defined functions, following function definition patterns from Manim Documentation.
5.  **Voiceover:** Use `with self.voiceover(text="...")` for speech synchronization, precisely matching the narration script and animation timings from the Animation and Narration Plan.
6.  **Comments:** Add clear and concise comments for complex animations, spatial logic (positioning, arrangements), and object lifecycle management. *Use comments extensively to explain code logic, especially for spatial positioning, animation sequences, and constraint enforcement, mirroring commenting style in Manim Documentation*.  **Add comments to explain the purpose and usage of any Manim plugins.**
7.  **Error Handling & Constraint Validation:** Implement basic error handling if error handling strategies are suggested or exemplified in the Manim Documentation. **Critically, during code generation, implement explicit checks to validate if each object's position and animation adheres to the safe area margins (0.5 units) and minimum spacing (0.3 units).**
8.  **Performance:** Follow Manim best practices for efficient code and rendering performance, as recommended in the Manim Documentation.
9.  **Manim Plugins:** You are allowed and encouraged to use established, well-documented Manim plugins if they simplify the code, improve efficiency, or provide functionality not readily available in core Manim.
    *   **If a plugin is used:**
        *   Include the necessary import statement at the top of the file.
        *   Add a comment indicating the plugin used and its purpose: `### Plugin: <plugin_name> - <brief justification>`.
        *   Ensure all plugin usage adheres to the plugin's documentation.
10. **No External Assets:** No external files (images, audio, video). *Use only Manim built-in elements and procedural generation, or elements provided by approved Manim plugins. No external assets are allowed*.
11. **No Main Function:** Only scene class. No `if __name__ == "__main__":`.
12. **Spatial Accuracy (Paramount):** Achieve accurate spatial positioning as described in the technical implementation plan, *strictly using relative positioning methods (`next_to`, `align_to`, `shift`, VGroups) and enforcing safe area margins and minimum 0.3 unit spacing, as documented in Manim Documentation Context*. *Spatial accuracy and constraint adherence are the highest priorities in code generation.*
13. **VGroup Structure:** Implement VGroup hierarchy precisely as defined in the Technical Implementation Plan, using documented VGroup methods for object grouping and manipulation.
14. **Spacing & Margins (Strict Enforcement):** Adhere strictly to safe area margins (0.5 units) and minimum spacing (0.3 units) requirements for *all* objects and VGroups throughout the scene and all animations. Prevent overlaps and ensure all objects stay within the safe area. *Rigorously enforce spacing and margin requirements using `buff` parameters, relative positioning, and explicit constraint validation checks during code generation, and validate against safe area guidelines from Manim Documentation Context*.
15. **Background:** Default background (Black) is sufficient. Do not create custom color background Rectangles.
16. **Text Color:** Do not use BLACK color for any text. Use predefined colors (BLUE_C, BLUE_D, GREEN_C, GREEN_D, GREY_A, GREY_B, GREY_C, LIGHTER_GRAY, LIGHT_GRAY, GOLD_C, GOLD_D, PURPLE_C, TEAL_C, TEAL_D, WHITE).
17. **Default Colors:** You MUST use the provided color definitions if you use colors in your code. ONLY USE THE COLORS PREVIOUSLY DEFINED.
18. **Animation Timings and Narration Sync:** Implement animations with precise `run_time` values and synchronize them with the narration script according to the Animation and Narration Plan. Use `Wait()` commands with specified durations for transition buffers.
19. **Don't be lazy on code generation:** Generate full, complete code including all helper functions. Ensure that the output is comprehensive and the code is fully functional, incorporating all necessary helper methods and complete scene implementation details.
20. **LaTeX Package Handling:** If the technical implementation plan specifies the need for additional LaTeX packages:
    *   Create a `TexTemplate` object.
    *   Use `myTemplate = TexTemplate()`
    *   Use `myTemplate.add_to_preamble(r"\\usepackage{{package_name}}")` to add the required package.
    *   Pass this template to the `Tex` or `MathTex` object: `tex = Tex(..., tex_template=myTemplate)`.

**Example Code Style and Structure to Emulate:**

*   **Helper Classes:** Utilize helper classes (like `Scene2_Helper`) to encapsulate object creation and scene logic, promoting modularity and reusability.
*   **Stage-Based `construct` Method:** Structure the `construct` method into logical stages (e.g., Stage 1, Stage 2, Stage 3) with comments to organize the scene flow.
*   **Reusable Object Creation Functions:** Define reusable functions within helper classes for creating specific Manim objects (e.g., `create_axes`, `create_formula_tex`, `create_explanation_text`).
*   **Clear Comments and Variable Names:** Use clear, concise comments to explain code sections and logic. Employ descriptive variable names (e.g., `linear_function_formula`, `logistic_plot`) for better readability.
*   **Text Elements:** Create text elements using `Tex` or `MathTex` for formulas and explanations, styling them with `color` and `font_size` as needed.
*   **Manim Best Practices:** Follow Manim best practices, including using `VoiceoverScene`, `KokoroService`, common Manim objects, animations, relative positioning, and predefined colors.

You MUST generate the Python code in the following format (from <CODE> to </CODE>):
<CODE>
```python
from manim import *
from manim import config as global_config
from manim_voiceover import VoiceoverScene
from src.utils.kokoro_voiceover import KokoroService # 你必须这样导入，因为这是我们自定义的配音服务。

# 插件导入，不要更改导入语句
from manim_circuit import *
from manim_physics import *
from manim_chemistry import *
from manim_dsa import *
from manim_ml import *

# 辅助函数/类（实现并使用辅助类和函数，以提高代码可重用性和组织性）
class Scene{scene_number}_Helper:  # 示例：class Scene1_Helper:
    # 包含场景{scene_number}实用函数的辅助类。
    def __init__(self, scene):
        self.scene = scene
        # ... (添加任何必要的初始化)

    # 可重用对象创建函数（根据计划实现对象创建函数，以实现模块化和可重用性）
    def get_center_of_edges(self, polygon, buff=SMALL_BUFF*3):
        # 计算多边形（三角形、正方形等）每条边的中心点，带有可选的缓冲区。
        # 获取多边形的顶点
        vertices = polygon.get_vertices()
        n_vertices = len(vertices)
        # 初始化列表以存储边缘中心
        coords_vertices = []
        # 计算每条边的中心点和法线
        for i in range(n_vertices):
            # 获取当前和下一个顶点（环绕到第一个顶点）
            v1 = vertices[i]
            v2 = vertices[(i + 1) % n_vertices]
            # 计算边缘中心
            edge_center = (v1 + v2) / 2
            # 计算边缘向量并归一化
            edge_vector = v2 - v1
            edge_length = np.linalg.norm(edge_vector)
            normal = np.array([-edge_vector[1], edge_vector[0], 0]) / edge_length
            # 在法线方向上添加缓冲区
            coords_vertices.append(edge_center + normal * buff)

        return coords_vertices

    def create_formula_tex(self, formula_str, color):
        # 示例函数，用于创建具有指定颜色的MathTex公式。
        # 检查是否需要自定义TexTemplate（来自技术计划）。
        if hasattr(self.scene, 'tex_template'):
            formula = MathTex(formula_str, color=color, tex_template=self.scene.tex_template)
        else:
            formula = MathTex(formula_str, color=color)
        return formula

    # ... (根据需要添加更多用于对象创建和场景逻辑的辅助函数)


class Scene{scene_number}(VoiceoverScene, MovingCameraScene):  # 注意：如果需要多重继承，可以在当前模板之上添加更多Manim场景类。
    # 提醒：此场景类是完全自包含的。它不依赖于前一个或后续场景的实现。
    def construct(self):
        # 初始化语音服务
        self.set_speech_service(KokoroService())

        # 实例化辅助类（根据计划）
        helper = Scene{scene_number}_Helper(self)  # 示例：helper = Scene1_Helper(self)

        # 检查LaTeX包并在需要时创建TexTemplate。
        # 此部分应根据技术实现计划生成。
        # 例如，如果计划包括："需要：\\usepackage{{amsmath}}"
        # 那么生成：
        #
        # my_template = TexTemplate()
        # my_template.add_to_preamble(r"\\usepackage{{amsmath}}")
        # self.tex_template = my_template

        # --- 阶段1：场景设置（根据你的场景调整阶段编号和描述，遵循计划）---
        with self.voiceover(text="[阶段1的旁白 - 来自动画和旁白计划]") as tracker:  # 阶段1的配音
            # 使用辅助函数创建对象（根据计划）
            axes = helper.create_axes()  # 示例：axes = helper.create_axes()
            formula = helper.create_formula_tex("...", BLUE_C)  # 示例：formula = helper.create_formula_tex("...", BLUE_C)
            explanation = helper.create_explanation_text("...")  # 示例：explanation = helper.create_explanation_text("...")

            # 定位对象（相对定位，约束验证 - 根据计划）
            formula.to_corner(UL)  # 示例定位
            axes.move_to(ORIGIN)  # 示例定位
            explanation.next_to(axes, RIGHT)  # 示例定位

            # 阶段1的动画（与配音同步 - 根据计划）
            self.play(Write(formula), Write(axes), run_time=tracker.duration)  # 示例动画
            self.wait(0.5)  # 过渡缓冲

        # --- 阶段2：... （以类似的模块化和结构化方式实现阶段2、阶段3等，遵循计划）---
        with self.voiceover(text="[阶段2的旁白 - 来自动画和旁白计划]") as tracker:  # 阶段2的配音
            # ... （阶段2的对象创建、定位和动画，使用辅助函数和约束验证）
            pass  # 替换为实际的阶段2代码

        # ... （以类似的模块化和结构化方式实现剩余阶段，遵循动画和旁白计划以及技术实现计划，并在每个阶段严格验证空间约束）

        self.wait(1)  # 场景结束过渡缓冲
```
</CODE>

Notes:
The `get_center_of_edges` helper function is particularly useful for:
1. Finding the midpoint of polygon edges for label placement
2. Calculating offset positions for side labels that don't overlap with the polygon
3. Creating consistent label positioning across different polygon sizes and orientations

Example usage in your scene:
```python
def label_triangle_sides(self, triangle, labels=["a", "b", "c"]):
    # Helper function to label triangle sides.
    edge_centers = self.helper.get_center_of_edges(triangle)
    labeled_sides = VGroup()
    for center, label in zip(edge_centers, labels):
            tex = MathTex(label).move_to(center)
            labeled_sides.add(tex)
        return labeled_sides
```