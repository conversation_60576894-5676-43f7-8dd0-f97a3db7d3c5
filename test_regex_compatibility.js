// 测试正则表达式兼容性
function testRegexCompatibility() {
  console.log('=== 测试正则表达式兼容性 ===\n');

  const testInput = `
<SCENE_1>
**场景标题**：问题理解
**核心内容**：展示蜡烛燃烧问题
</SCENE_1>

<SCENE_2>
**场景标题**：建立方程
**核心内容**：根据条件建立方程
</SCENE_2>

<SCENE_3>
**场景标题**：求解过程
**核心内容**：解方程得出答案
</SCENE_3>
  `;

  // 测试当前使用的正则（带反向引用）
  console.log('1. 测试当前正则（带反向引用）:');
  const currentRegex = /<SCENE_(\d+)>([\s\S]*?)<\/SCENE_\1>/g;
  let matches = [];
  let match;
  
  try {
    while ((match = currentRegex.exec(testInput)) !== null) {
      matches.push({
        sceneNumber: match[1],
        content: match[2].trim(),
        fullMatch: match[0]
      });
    }
    console.log(`✅ 找到 ${matches.length} 个匹配`);
    matches.forEach((m, i) => {
      console.log(`   场景 ${m.sceneNumber}: ${m.content.substring(0, 30)}...`);
    });
  } catch (error) {
    console.log(`❌ 正则执行出错: ${error.message}`);
  }

  // 测试备用正则（不使用反向引用）
  console.log('\n2. 测试备用正则（不使用反向引用）:');
  const backupRegex = /<SCENE_\d+>([\s\S]*?)<\/SCENE_\d+>/g;
  matches = [];
  
  try {
    while ((match = backupRegex.exec(testInput)) !== null) {
      // 从完整匹配中提取场景号
      const sceneNumberMatch = match[0].match(/<SCENE_(\d+)>/);
      const sceneNumber = sceneNumberMatch ? sceneNumberMatch[1] : 'unknown';
      
      matches.push({
        sceneNumber: sceneNumber,
        content: match[1].trim(),
        fullMatch: match[0]
      });
    }
    console.log(`✅ 找到 ${matches.length} 个匹配`);
    matches.forEach((m, i) => {
      console.log(`   场景 ${m.sceneNumber}: ${m.content.substring(0, 30)}...`);
    });
  } catch (error) {
    console.log(`❌ 备用正则执行出错: ${error.message}`);
  }

  // 测试边界情况
  console.log('\n3. 测试边界情况:');
  const edgeCases = [
    '<SCENE_1></SCENE_1>',
    '<SCENE_1>\n\n</SCENE_1>',
    '<SCENE_1>内容</SCENE_2>', // 错误匹配
    '<SCENE_10>十个场景</SCENE_10>', // 两位数
  ];

  edgeCases.forEach((testCase, i) => {
    console.log(`\n   边界情况 ${i + 1}: ${testCase}`);
    
    // 测试当前正则
    const currentMatches = testCase.match(/<SCENE_(\d+)>([\s\S]*?)<\/SCENE_\1>/g);
    console.log(`   当前正则匹配: ${currentMatches ? currentMatches.length : 0} 个`);
    
    // 测试备用正则
    const backupMatches = testCase.match(/<SCENE_\d+>([\s\S]*?)<\/SCENE_\d+>/g);
    console.log(`   备用正则匹配: ${backupMatches ? backupMatches.length : 0} 个`);
  });

  console.log('\n=== 测试完成 ===');
}

// 运行测试
if (require.main === module) {
  testRegexCompatibility();
}

module.exports = {
  testRegexCompatibility,
};
