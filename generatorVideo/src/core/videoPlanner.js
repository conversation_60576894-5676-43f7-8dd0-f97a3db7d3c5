const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const glob = require('glob');
// 修改导入方式
const { prepareTextInputs } = require('../../mllmTools/utils');
const { extractXml } = require('../utils/utils');
const {
  getPromptScenePlan,
  getPromptSceneVisionStoryboard,
  getPromptSceneTechnicalImplementation,
  getPromptSceneAnimationNarration,
  getPromptContextLearningScenePlan,
  getPromptContextLearningVisionStoryboard,
  getPromptContextLearningTechnicalImplementation,
  getPromptContextLearningAnimationNarration,
  getPromptContextLearningCode,
} = require('../../taskGenerator/index');
const { RAGIntegration } = require('../rag/ragIntegration');

/**
 * VideoPlanner 类用于规划和生成视频内容
 *
 * 该类处理视频内容的规划和生成，包括场景大纲、视觉故事板、技术实现和动画旁白。
 */
class VideoPlanner {
  /**
   * @param {Object} plannerModel - 用于规划任务的模型
   * @param {Object} [helperModel=null] - 可选的辅助模型，默认为 plannerModel
   * @param {string} [outputDir="output"] - 输出文件的目录
   * @param {boolean} [printResponse=false] - 是否打印模型响应
   * @param {boolean} [useContextLearning=false] - 是否使用上下文学习
   * @param {string} [contextLearningPath="data/context_learning"] - 上下文学习示例的路径
   * @param {boolean} [useRag=false] - 是否使用 RAG
   * @param {string} [sessionId=null] - 会话标识符
   * @param {string} [chromaDbPath="data/rag/chroma_db"] - ChromaDB 的路径
   * @param {string} [manimDocsPath="data/rag/manim_docs"] - Manim 文档的路径
   * @param {string} [embeddingModel="text-embedding-v3"] - 嵌入模型的名称
   * @param {boolean} [useLangfuse=true] - 是否使用 Langfuse 日志记录
   */
  constructor({
    plannerModel,
    helperModel = null,
    outputDir = 'output',
    printResponse = false,
    useContextLearning = false,
    contextLearningPath = 'data/context_learning',
    useRag = false,
    sessionId = null,
    chromaDbPath = 'data/rag/chroma_db',
    manimDocsPath = 'data/rag/manim_docs',
    embeddingModel = 'text-embedding-v3',
    useLangfuse = true,
  }) {
    this.plannerModel = plannerModel;
    this.helperModel = helperModel;
    this.outputDir = outputDir;
    this.printResponse = printResponse;
    this.useContextLearning = useContextLearning;
    this.contextLearningPath = contextLearningPath;

    // 初始化不同类型的上下文示例
    this.scenePlanExamples = useContextLearning
      ? this._loadContextExamples('scene_plan')
      : null;
    this.visionStoryboardExamples = useContextLearning
      ? this._loadContextExamples('scene_vision_storyboard')
      : null;
    this.technicalImplementationExamples = useContextLearning
      ? this._loadContextExamples('technical_implementation')
      : null;
    this.animationNarrationExamples = useContextLearning
      ? this._loadContextExamples('scene_animation_narration')
      : null;
    this.codeExamples = useContextLearning
      ? this._loadContextExamples('code')
      : null;

    this.useRag = useRag;
    this.ragIntegration = null;
    if (useRag) {
      this.ragIntegration = new RAGIntegration({
        helperModel,
        outputDir,
        chromaDbPath,
        manimDocsPath,
        embeddingModel,
        useLangfuse,
        sessionId,
      });
    }
    this.relevantPlugins = []; // 初始化为空数组
  }

  /**
   * 从文件加载特定类型的上下文学习示例
   *
   * @param {string} exampleType - 要加载的示例类型 ('scene_plan', 'scene_vision_storyboard' 等)
   * @returns {string|null} - 包含加载示例的格式化字符串，如果没有找到示例则返回 null
   */
  async _loadContextExamples(exampleType) {
    const examples = [];

    // 为不同类型定义文件模式
    const filePatterns = {
      scene_plan: '*_scene_plan.txt',
      scene_vision_storyboard: '*_scene_vision_storyboard.txt',
      technical_implementation: '*_technical_implementation.txt',
      scene_animation_narration: '*_scene_animation_narration.txt',
      code: '*.js',
    };

    const pattern = filePatterns[exampleType];
    if (!pattern) {
      return null;
    }

    // 在 contextLearningPath 的子目录中搜索
    try {
      const files = await glob(
        path.join(this.contextLearningPath, '**', pattern)
      );

      for (const exampleFile of files) {
        const content = await fs.readFile(exampleFile, 'utf-8');
        if (exampleType === 'code') {
          examples.push(
            `// Example from ${path.basename(exampleFile)}\n${content}\n`
          );
        } else {
          examples.push(
            `# Example from ${path.basename(exampleFile)}\n${content}\n`
          );
        }
      }

      // 使用适当的模板格式化示例
      if (examples.length > 0) {
        const formattedExamples = this._formatExamples(exampleType, examples);
        return formattedExamples;
      }
    } catch (error) {
      console.error(
        `Error loading context examples for ${exampleType}:`,
        error
      );
    }

    return null;
  }

  /**
   * 使用基于类型的适当模板格式化示例
   *
   * @param {string} exampleType - 要格式化的示例类型
   * @param {Array<string>} examples - 要格式化的示例字符串列表
   * @returns {string|null} - 格式化的示例字符串，如果没有找到模板则返回 null
   */
  _formatExamples(exampleType, examples) {
    const templates = {
      scene_plan: getPromptContextLearningScenePlan,
      scene_vision_storyboard: getPromptContextLearningVisionStoryboard,
      technical_implementation: getPromptContextLearningTechnicalImplementation,
      scene_animation_narration: getPromptContextLearningAnimationNarration,
      code: getPromptContextLearningCode,
    };

    const template = templates[exampleType];
    if (template) {
      return template({ examples: examples.join('\n') });
    }
    return null;
  }
  async isUseRagSetPlugins(topic, description) {
    if (this.useRag) {
      console.log('xxr:useRagSetPlugins');
      this.relevantPlugins =
        (await this.ragIntegration.detectRelevantPlugins(topic, description)) ||
        [];
      this.ragIntegration.setRelevantPlugins(this.relevantPlugins);
      console.log(`Detected relevant plugins: ${this.relevantPlugins}`);
    }
  }
  /**
   * 根据主题和描述生成场景大纲
   *
   * @param {string} topic - 视频的主题
   * @param {string} description - 视频内容的描述
   * @param {string} sessionId - 会话标识符
   * @returns {Promise<string>} - 生成的场景大纲
   */
  async generateSceneOutline(topic, description) {
    // 如果启用了 RAG，预先检测相关插件
    await this.isUseRagSetPlugins(topic, description);

    let prompt = getPromptScenePlan(topic, description);

    if (this.useContextLearning && this.scenePlanExamples) {
      prompt += `\n\nHere are some example scene plans for reference:\n${this.scenePlanExamples}`;
    }
    console.log('xxr: 调用plannerModel生成场景大纲,提示词：', prompt);
    // 使用规划模型生成计划
    const responseText = await this.plannerModel(prepareTextInputs(prompt), {
      metadata: {
        generation_name: 'scene_outline',
        tags: [topic, 'scene-outline'],
      },
    });

    // 场景大纲提示词已经包含了<SCENE_1>, <SCENE_2>, <SCENE_3>标签
    // 直接返回完整响应文本供parseSceneOutline函数解析
    return responseText;
  }
  // 生成故事板
  async _generateStoryboardSingle({
    topic,
    description,
    sceneContent,
    sceneNum,
    sceneOutline = '', // 添加场景大纲参数，默认为空字符串
  }) {
    let promptVisionStoryboard = getPromptSceneVisionStoryboard(
      sceneNum,
      topic,
      description,
      sceneOutline,
      sceneContent,
      this.relevantPlugins
    );
    console.log(
      `xxr: 获取提示词,第${sceneNum}个场景，如果有上下文学习，添加上下文学习示例，如果启用rag，添加rag查询`
    );
    // 如果可用，仅为此阶段添加视觉故事板示例
    if (this.useContextLearning && this.visionStoryboardExamples) {
      promptVisionStoryboard += `\n\nHere are some example storyboards:\n${this.visionStoryboardExamples}`;
    }

    if (this.ragIntegration) {
      // 生成 RAG 查询
      const ragQueries =
        await this.ragIntegration._generateRagQueriesStoryboard({
          scene_plan: sceneContent,
          topic: topic,
          scene_number: sceneNum,
          relevant_plugins: this.relevantPlugins,
        });

      const retrievedDocs = await this.ragIntegration.getRelevantDocs({
        rag_queries: ragQueries,
        topic: topic,
        scene_number: sceneNum,
      });

      // 将文档添加到提示中
      promptVisionStoryboard += `\n\n${retrievedDocs}`;
    }
    console.log(
      `xxr: 得到故事面板提示词，使用plannerModel${promptVisionStoryboard}`
    );
    const visionStoryboardPlan = await this.plannerModel(
      prepareTextInputs(promptVisionStoryboard),
      {
        metadata: {
          generation_name: 'scene_vision_storyboard',
          tags: [topic, `scene${sceneNum}`],
        },
      }
    );

    // 提取视觉故事板计划 <SCENE_VISION_STORYBOARD_PLAN> ... </SCENE_VISION_STORYBOARD_PLAN>
    const visionMatch = visionStoryboardPlan.match(
      /<SCENE_VISION_STORYBOARD_PLAN>([\s\S]*?)<\/SCENE_VISION_STORYBOARD_PLAN>/
    );
    const extractedVisionStoryboardPlan = visionMatch
      ? visionMatch[1]
      : visionStoryboardPlan;
    return extractedVisionStoryboardPlan;
  }
  // 生成技术实现
  async _generateTechnicalImplementationSingle({
    topic,
    description,
    sceneContent,
    storyContent,
    sceneNum,
  }) {
    console.log(
      `xxr: 通过故事板计划获取技术实现提示词,第${sceneNum}个场景，上下文学习，rag查询`
    );
    let promptTechnicalImplementation = getPromptSceneTechnicalImplementation(
      sceneNum,
      topic,
      description,
      sceneContent,
      storyContent,
      this.relevantPlugins
    );

    // 如果可用，仅为此阶段添加技术实现示例
    if (this.useContextLearning && this.technicalImplementationExamples) {
      promptTechnicalImplementation += `\n\nHere are some example technical implementations:\n${this.technicalImplementationExamples}`;
    }

    if (this.ragIntegration) {
      // 生成 RAG 查询
      const ragQueries = await this.ragIntegration._generateRagQueriesTechnical(
        {
          storyboard: storyContent,
          topic,
          scene_number: sceneNum,
          relevant_plugins: this.relevantPlugins,
        }
      );

      const retrievedDocs = await this.ragIntegration.getRelevantDocs({
        rag_queries: ragQueries,
        topic,
        scene_number: sceneNum,
      });

      // 将文档添加到提示中
      promptTechnicalImplementation += `\n\n${retrievedDocs}`;
    }

    console.log(
      `xxr: 得到技术实现提示词，使用plannerModel${promptTechnicalImplementation}`
    );

    const technicalImplementationPlan = await this.plannerModel(
      prepareTextInputs(promptTechnicalImplementation),
      {
        metadata: {
          generation_name: 'scene_technical_implementation',
          tags: [topic, `scene${sceneNum}`],
        },
      }
    );

    // 提取技术实现计划 <SCENE_TECHNICAL_IMPLEMENTATION_PLAN> ... </SCENE_TECHNICAL_IMPLEMENTATION_PLAN>
    const technicalMatch = technicalImplementationPlan.match(
      /<SCENE_TECHNICAL_IMPLEMENTATION_PLAN>([\s\S]*?)<\/SCENE_TECHNICAL_IMPLEMENTATION_PLAN>/
    );
    const extractedTechnicalImplementationPlan = technicalMatch
      ? technicalMatch[1]
      : technicalImplementationPlan;
    return extractedTechnicalImplementationPlan;
  }
  // 生成动画和旁白计划
  async _generateAnimationNarrationSingle({
    topic,
    description,
    sceneContent,
    storyContent,
    sceneOutline,
    sceneNum,
  }) {
    console.log(
      `xxr: 通过故事板计划和技术实现计划获取动画和旁白提示词,第${sceneNum}个场景，上下文学习，rag查询`
    );
    let promptAnimationNarration = getPromptSceneAnimationNarration({
      sceneNum,
      topic,
      description,
      sceneContent,
      storyContent,
      sceneOutline,
      relevantPlugins: this.relevantPlugins,
    });
    // 如果可用，仅为此阶段添加动画和旁白示例
    if (this.useContextLearning && this.animationNarrationExamples) {
      promptAnimationNarration += `\n\nHere are some example animation and narration plans:\n${this.animationNarrationExamples}`;
    }
    if (this.ragIntegration) {
      // 生成 RAG 查询
      const ragQueries = await this.ragIntegration._generateRagQueriesNarration(
        {
          storyboard: storyContent,
          topic,
          scene_number: sceneNum,
          relevant_plugins: this.relevantPlugins,
        }
      );
      const retrievedDocs = await this.ragIntegration.getRelevantDocs({
        rag_queries: ragQueries,
        topic,
        scene_number: sceneNum,
      });
      // 将文档添加到提示中
      promptAnimationNarration += `\n\n${retrievedDocs}`;
    }
    console.log(
      `xxr: 得到动画和旁白提示词，使用plannerModel${promptAnimationNarration}`
    );
    const animationNarrationPlan = await this.plannerModel(
      prepareTextInputs(promptAnimationNarration),
      {
        metadata: {
          generation_name: 'scene_animation_narration',
          tags: [topic, `scene${sceneNum}`],
        },
      }
    );
    // 提取动画和旁白计划 <SCENE_ANIMATION_NARRATION_PLAN>... </SCENE_ANIMATION_NARRATION_PLAN>
    const animationMatch = animationNarrationPlan.match(
      /<SCENE_ANIMATION_NARRATION_PLAN>([\s\S]*?)<\/SCENE_ANIMATION_NARRATION_PLAN>/
    );
    const extractedAnimationNarrationPlan = animationMatch
      ? animationMatch[1]
      : animationNarrationPlan;
    return extractedAnimationNarrationPlan;
  }
}
// 修改导出方式
module.exports = { VideoPlanner };
