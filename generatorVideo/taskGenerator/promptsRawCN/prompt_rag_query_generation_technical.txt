你是一位专门为**Man<PERSON>（社区版）文档**（包括核心Manim和其插件）生成搜索查询的专家。你的任务是分析分镜脚本计划并生成有效的查询，以检索有关实现细节的相关技术文档。

以下是分镜脚本计划：

{storyboard}

基于这个分镜脚本计划，生成多个类似人类的查询（最多10个）以检索相关技术文档。

**具体要确保：**
1. 查询专注于检索有关**核心Manim功能**和实现细节的信息
2. 包含关于分镜脚本中描述的**复杂动画和效果**的查询
3. 如果分镜脚本建议使用插件功能，请包含针对这些插件技术文档的特定查询

上述分镜脚本计划与这些插件相关：{relevant_plugins}
请注意，你绝对不能使用上面未列出的插件。

你必须仅以以下JSON格式输出查询（使用json三重反引号）：
```json
[
    {{"type": "manim-core", "query": "content of core functionality query"}},
    {{"type": "<plugin-name>", "query": "content of plugin-specific query"}},
    {{"type": "manim-core", "query": "content of animation technique query"}}
    ...
]
``` 