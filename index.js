const express = require('express');
const bodyParser = require('body-parser');
const db = require('./db'); // 引入 db 模块
const { sendData } = require('./yach'); // 引入 yach 模块
const cors = require('cors'); // 引入 cors 中间件
const { default: axios } = require('axios');
const { v4: uuidv4 } = require('uuid');
const AWS = require('aws-sdk');
const useGenratorVideoApi = require('./generatorVideo/app.js');

// 引入日志服务
const { logger } = require('./src/utils/logger');
const {
  requestLogger,
  errorLogger,
  setupGlobalErrorHandling,
} = require('./src/utils/middleware');
const useLoggerApi = require('./loggerApi');

// 设置全局错误处理
setupGlobalErrorHandling();

const app = express();
const PORT = process.env.PORT || 3000;

// 记录应用启动
logger.info('应用程序启动中', {
  port: PORT,
  nodeVersion: process.version,
  platform: process.platform,
});

// AWS S3 配置
// 配置 AWS S3 凭证（推荐使用环境变量或配置文件）
AWS.config.update({
  accessKeyId: '********************',
  secretAccessKey: 'Eq8XWHCEf/ESUs/WGDBtuapkIKPLLMPn0feR2rVN',
  region: 'us-west-2',
});
const s3 = new AWS.S3({ apiVersion: '2006-03-01' });

// 使用 cors 中间件
app.use(cors());
// 使用 body-parser 解析请求体
app.use(bodyParser.json());

// 静态文件服务
app.use('/public', express.static('public'));

// 使用请求日志中间件
app.use(
  requestLogger({
    skip: (req) => req.url.includes('/api/logs/stream'), // 跳过日志流请求
    includeBody: true,
    maxBodyLength: 500,
  })
);

const prefix = '/frontend-chat';

// 简单的日志中间件（已被requestLogger替代，这里移除）
// app.use((req, res, next) => {
//   console.log(`${req.method} ${req.url}`);
//   next();
// });

// 错误处理中间件
function errorHandler(err, req, res, next) {
  logger.error('Express错误处理器捕获到错误', {
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
  });
  res.status(500).send({ error: '服务器内部错误!' });
}

app.get(`${prefix}/test`, (req, res) => {
  res.json({ message: 'Hello, world!' });
});
const recordMessage = async ({
  sender_id,
  receiver_id,
  content,
  is_read = false,
  wamid = '',
  message_type = 'text',
  status = 'received',
}) => {
  console.log(123, sender_id, receiver_id, content);
  // 存储消息到 message 表
  try {
    await db.query(
      'INSERT INTO messages (sender_id, receiver_id, content, is_read, wamid, status, message_type) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [sender_id, receiver_id, content, is_read, wamid, status, message_type]
    );
  } catch (error) {
    console.log(error);
  }
};
const token =
  'EAAjUOZBGAps4BO5obK8l6qZBUusIPCIYhM7NzaZANHJncDYg4W9afHJ8ZAOQvsfbD8QKDQwEcEssZC94UhQISNAnZCCJSMv2QnNnhjT9DKDwtBxrHAVq1pCERHkU2FWNh48R1K3O6ynlG5KiX8LnTfZBZBuam53ZCXZASyT5ZAyFPo94juTYGIVC6kf1nbqAf8L3ob8ZCQZDZD';
// 处理 webhook 消息
app.post(`${prefix}/webhook`, async (req, res) => {
  const body = req.body;
  if (body.object === 'whatsapp_business_account') {
    let errorInfo = null;
    for (const entry of body.entry) {
      console.log(1111, entry.changes[0].value);
      if (entry.changes[0].value.messages) {
        const wa_phone = entry.changes[0].value.messages[0].from;
        let message = '';
        if (
          entry.changes[0].value.messages[0].type == 'image' ||
          entry.changes[0].value.messages[0].type == 'document' ||
          entry.changes[0].value.messages[0].type == 'video'
        ) {
          const type = entry.changes[0].value.messages[0].type;

          let messageObj = {};
          if (type === 'image') {
            messageObj = entry.changes[0].value.messages[0].image;
          } else if (type === 'document') {
            messageObj = entry.changes[0].value.messages[0].document;
          } else if (type === 'video') {
            messageObj = entry.changes[0].value.messages[0].video;
          }
          console.log('messageObj', messageObj);
          const id = messageObj.id;
          const basename = messageObj.mime_type.split('/')[1];
          const fileName = messageObj.filename || `${uuidv4()}.${basename}`;
          const result = await axios.get(
            `https://graph.facebook.com/v11.0/${id}`,
            {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          );
          const imageUrl = result.data.url;
          console.log('图片地址', imageUrl);
          // 下载图片
          try {
            const imageResponse = await axios.get(imageUrl, {
              responseType: 'arraybuffer',
              headers: {
                Authorization: `Bearer ${token}`,
              },
            });
            const buffer = Buffer.from(imageResponse.data, 'binary');

            // 上传图片到S3
            const params = {
              Bucket: 'pa-s3-prod', // 替换为你的S3存储桶名称
              Key: `think-chat-serve/${fileName}`,
              Body: buffer,
              ContentType: imageResponse.headers['content-type'],
            };

            const res = await s3.upload(params).promise();
            console.log(`文件已上传至 S3: ${res.Location}`);
            message = {
              basename: basename,
              fileName: fileName,
              url: res.Location,
            };
          } catch (error) {
            console.error('Error uploading image:', error);
          }
        } else {
          console.log('messageObj', entry.changes[0].value.messages[0].text);

          message = {
            text: entry.changes[0].value.messages[0].text.body,
          };
        }
        // 检查 relate 表中是否存在相同的 wa_phone
        const results = await db.query(
          'SELECT * FROM relate WHERE wa_phone = ?',
          [wa_phone]
        );
        if (results.length === 0) {
          // 如果不存在，创建一条新数据
          await db.query(
            'INSERT INTO relate (wa_phone, name, isBind) VALUES (?, ?, ?)',
            [wa_phone, '', false]
          );
        }
        recordMessage({
          sender_id: wa_phone,
          receiver_id: '1',
          content: JSON.stringify(message),
          wamid: entry.changes[0].value.messages[0].id,
          status: 'received',
          message_type: entry.changes[0].value.messages[0].type,
        });
        // 发送消息到前端
        // sendData({
        //   msgtype: 'text',
        //   text: { content: `New message from ${wa_phone}: ${message}` }
        // });
      }
      if (entry.changes[0].value.statuses) {
        const statuses = entry.changes[0].value.statuses[0];
        const id = statuses.id;
        const status = statuses.status;
        if (statuses.errors) {
          console.log('发送失败', statuses.errors);
          errorInfo = statuses.errors[0];
        }
        // 更新数据库里这条消息status,delivered,read
        await db.query('UPDATE messages SET status = ? WHERE wamid = ?', [
          status,
          id,
        ]);
      }
    }

    if (errorInfo) {
      res
        .status(200)
        .send({ code: errorInfo.code, message: errorInfo.error_data.details });
    } else {
      res.status(200).send('EVENT_RECEIVED');
    }
  } else {
    res.sendStatus(404);
  }
});

// 添加 webhook 验证端点
app.get(`${prefix}/webhook`, (req, res) => {
  // 您的验证令牌，应该在 Meta 开发者平台设置相同的值
  const VERIFY_TOKEN = 'your_verify_token_here';

  // 从 URL 获取查询参数
  const mode = req.query['hub.mode'];
  const token = req.query['hub.verify_token'];
  const challenge = req.query['hub.challenge'];

  // 检查模式和令牌是否匹配
  if (mode === 'subscribe' && token === VERIFY_TOKEN) {
    // 验证成功，响应 challenge
    console.log('WEBHOOK_VERIFIED');
    res.status(200).send(challenge);
  } else {
    // 验证失败
    console.log('验证失败');
    res.sendStatus(403);
  }
});
const sendToWhatsApp = async (receiver_id, message) => {
  try {
    console.log(111122, receiver_id, message);
    const res = await axios.post(
      'https://graph.facebook.com/v21.0/555520454306058/messages',
      {
        messaging_product: 'whatsapp',
        to: receiver_id,
        type: 'text',
        preview_url: true,
        text: { body: message },
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );
    console.log('Message sent:', res.data);
    return res.data;
  } catch (error) {
    console.error(
      'Error sending message:',
      error.response ? error.response.data : error.message
    );
    return error.response ? error.response.data : error.message;
  }
};
app.post(`${prefix}/send`, async (req, res) => {
  const { receiver_id, message } = req.body;
  try {
    // 发送消息到前端
    // sendData({
    //   msgtype: 'text',
    //   text: { content: message }
    // });
    const resq = await sendToWhatsApp(receiver_id, message);
    if (resq.error) {
      res.status(200).json({ code: 2, message: res.error.message });
      console.log('权限失效');
      return;
    }
    const resId = resq.messages[0].id;
    // 存储数据库
    recordMessage({
      sender_id: '1',
      receiver_id: receiver_id,
      content: JSON.stringify({
        text: message,
      }),
      wamid: resId,
      status: 'sent',
      message_type: 'text',
    });
    res.status(200).json({ code: 0, message: 'Send success', wamid: resId });
  } catch (err) {
    res.status(500).json({ error: err });
  }
});

app.post(`${prefix}/sendImage`, async (req, res) => {
  const { receiver_id, message } = req.body;
  try {
    const sendRes = await axios.post(
      'https://graph.facebook.com/v21.0/555520454306058/messages',
      {
        messaging_product: 'whatsapp',
        to: receiver_id,
        type: 'image',
        image: { link: message.url },
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );
    console.log('Message sent:', sendRes.data);
    const resq = sendRes.data;
    if (resq.error) {
      res.status(200).json({ code: 2, message: sendRes?.error?.message });
      console.log('权限失效');
      return;
    }
    const resId = resq.messages[0].id;
    // 存储数据库
    recordMessage({
      sender_id: '1',
      receiver_id: receiver_id,
      content: JSON.stringify(message),
      wamid: resId,
      status: 'sent',
      message_type: 'image',
    });
    res.status(200).json({ code: 0, message: 'Send success', wamid: resId });
  } catch (err) {
    console.error(
      'Error sending message:',
      err.response ? err.response.data : err.message
    );
    const message = err.response ? err.response.data : err.message;
    res.status(500).json({ error: message });
  }
});

app.post(`${prefix}/sendDocument`, async (req, res) => {
  const { receiver_id, message } = req.body;
  try {
    const sendRes = await axios.post(
      'https://graph.facebook.com/v21.0/555520454306058/messages',
      {
        messaging_product: 'whatsapp',
        to: receiver_id,
        type: 'document',
        document: { link: message.url, filename: message.fileName },
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );
    console.log('Message sent:', sendRes.data);
    const resq = sendRes.data;
    if (resq.error) {
      res.status(200).json({ code: 2, message: sendRes?.error?.message });
      console.log('权限失效');
      return;
    }
    const resId = resq.messages[0].id;
    // 存储数据库
    recordMessage({
      sender_id: '1',
      receiver_id: receiver_id,
      content: JSON.stringify(message),
      wamid: resId,
      status: 'sent',
      message_type: 'document',
    });
    res.status(200).json({ code: 0, message: 'Send success', wamid: resId });
  } catch (err) {
    console.error(
      'Error sending message:',
      err.response ? err.response.data : err.message
    );
    const message = err.response ? err.response.data : err.message;
    res.status(500).json({ error: message });
  }
});

app.post(`${prefix}/sendVideo`, async (req, res) => {
  const { receiver_id, message } = req.body;
  try {
    const sendRes = await axios.post(
      'https://graph.facebook.com/v21.0/555520454306058/messages',
      {
        messaging_product: 'whatsapp',
        to: receiver_id,
        type: 'video',
        video: { link: message.url },
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );
    console.log('Message sent:', sendRes.data);
    const resq = sendRes.data;
    if (resq.error) {
      res.status(200).json({ code: 2, message: sendRes?.error?.message });
      console.log('权限失效');
      return;
    }
    const resId = resq.messages[0].id;
    // 存储数据库
    recordMessage({
      sender_id: '1',
      receiver_id: receiver_id,
      content: JSON.stringify(message),
      wamid: resId,
      status: 'sent',
      message_type: 'video',
    });
    res.status(200).json({ code: 0, message: 'Send success', wamid: resId });
  } catch (err) {
    console.error(
      'Error sending message:',
      err.response ? err.response.data : err.message
    );
    const message = err.response ? err.response.data : err.message;
    res.status(500).json({ error: message });
  }
});

app.post(`${prefix}/mark-read`, async (req, res) => {
  const { wa_phone } = req.body; // 从请求体中获取用户ID和聊天用户ID
  try {
    // 标记所有未读消息为已读
    await db.query(
      'UPDATE messages SET is_read = true WHERE sender_id = ? AND receiver_id = 1 AND is_read = false',
      [wa_phone]
    );
    res.status(200).json({ code: 0, message: 'Mark read success' });
  } catch (err) {
    res.status(500).json({ error: err });
  }
});
// 绑定接口
app.post(`${prefix}/bind`, async (req, res) => {
  const { wa_phone, userId } = req.body;
  try {
    const user = await db.query('SELECT name FROM users WHERE userId = ?', [
      userId,
    ]);
    if (user.length === 0) {
      return res.status(200).json({ code: 1, message: 'User not found' });
    }
    if (user.wa_phone) {
      return res.status(200).json({ code: 2, message: 'User already bound' });
    }
    console.log(user[0].name, userId, wa_phone);
    await db.query(
      'UPDATE relate SET name = ?, isBind = ?, userId = ? WHERE wa_phone = ?',
      [user[0].name, true, userId, wa_phone]
    );
    await db.query('UPDATE users SET wa_phone = ? WHERE userId = ?', [
      wa_phone,
      userId,
    ]);

    res.status(200).json({ code: 0, message: 'Bind success' });
  } catch (err) {
    res.status(500).json({ error: 'Internal server error' });
  }
});
// 解除绑定接口
app.post(`${prefix}/unbind`, async (req, res) => {
  const { userId } = req.body;
  try {
    await db.query(
      'UPDATE relate SET name = ?, isBind = ?, userId = ? WHERE userId = ?',
      ['', false, null, userId]
    );
    await db.query('UPDATE users SET wa_phone = ? WHERE userId = ?', [
      '',
      userId,
    ]);

    res.status(200).json({ code: 0, message: 'unBind success' });
  } catch (err) {
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 获取聊天记录
app.post(`${prefix}/chats`, async (req, res) => {
  const { wa_phone, page = 1, limit = 10 } = req.body;
  const offset = (page - 1) * limit;

  try {
    const user = await db.query('SELECT * FROM relate WHERE wa_phone = ?', [
      wa_phone,
    ]);
    const name = user[0].name || wa_phone;
    const results = await db.query(
      'SELECT * FROM messages WHERE (sender_id = ? OR receiver_id = ?) ORDER BY timestamp ASC LIMIT ? OFFSET ?',
      [wa_phone, wa_phone, parseInt(limit), parseInt(offset)]
    );
    results.forEach((result) => {
      if (result.sender_id === wa_phone) {
        result.name = name;
      } else {
        result.name = '我';
      }
      result.content = JSON.parse(result.content);
    });
    res.status(200).json(results);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

app.post(`${prefix}/new_messages`, async (req, res) => {
  const { wa_phone, lastMessageId } = req.body;
  try {
    let lastId = 0;
    if (lastMessageId != 0) {
      // 根据 wamid 获取对应的 id
      const idResult = await db.query(
        'SELECT id FROM messages WHERE wamid = ?',
        [lastMessageId]
      );
      if (idResult.length === 0) {
        return res.status(200).json({ code: 2, error: 'Message not found' });
      }
      lastId = idResult[0].id;
    }

    const user = await db.query('SELECT * FROM relate WHERE wa_phone = ?', [
      wa_phone,
    ]);
    const name = user[0].name || wa_phone;
    const results = await db.query(
      'SELECT * FROM messages WHERE sender_id = ? AND id > ? ORDER BY timestamp ASC',
      [wa_phone, lastId]
    );
    results.forEach((result) => {
      if (result.sender_id === wa_phone) {
        result.name = name;
      } else {
        result.name = '我';
      }
      result.content = JSON.parse(result.content);
    });
    res.status(200).json(results);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});
app.post(`${prefix}/getMessageStatus`, async (req, res) => {
  const { wamids } = req.body;
  try {
    if (!Array.isArray(wamids) || wamids.length === 0) {
      return res.status(400).json({ error: 'Invalid input' });
    }

    const placeholders = wamids.map(() => '?').join(',');
    const query = `SELECT wamid, status FROM messages WHERE sender_id = 1 AND wamid IN (${placeholders})`;
    const messages = await db.query(query, wamids);

    const result = messages.map((message) => ({
      wamid: message.wamid,
      status: message.status,
    }));

    res.status(200).json(result);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});
// 查询未读消息接口
app.post(`${prefix}/unread`, async (req, res) => {
  const { wa_phone } = req.body;
  try {
    const results = await db.query(
      'SELECT COUNT(*) AS unreadCount FROM chats_record WHERE wa_phone = ? AND isRead = false',
      [wa_phone]
    );
    res.status(200).json(results[0]);
  } catch (err) {
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 获取 relate 表数据接口
app.post(`${prefix}/relate`, async (req, res) => {
  try {
    const results = await db.query('SELECT * FROM relate');
    // 查找每个用户的未读消息数量
    for (const result of results) {
      const unread = await db.query(
        'SELECT COUNT(*) AS unreadCount FROM messages WHERE sender_id = ? AND is_read = false',
        [result.wa_phone]
      );
      result.unreadCount = unread[0].unreadCount;
    }
    res.status(200).json(results);
  } catch (err) {
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 获取 userList 接口
app.post(`${prefix}/userList`, async (req, res) => {
  try {
    const results = await db.query('SELECT * FROM users');
    results.forEach((result) => {
      result.tags = result.tags.split(',');
    });
    res.status(200).json(results);
  } catch (err) {
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post(`${prefix}/userInfo`, async (req, res) => {
  const { userId } = req.body;
  try {
    const results = await db.query('SELECT * FROM users WHERE userId = ?', [
      userId,
    ]);
    if (results.length === 0) {
      return res.status(200).json({ code: 1, message: 'User not found' });
    }
    results.forEach((result) => {
      result.tags = result.tags.split(',');
    });
    res.status(200).json(results[0]);
  } catch (err) {
    logger.error('获取用户信息失败', {
      userId: req.body.userId,
      error: err.message,
    });
    res.status(500).json({ error: '服务器内部错误' });
  }
});

// 注册日志管理API
useLoggerApi(app);

// 使用错误处理中间件
app.use(errorLogger);
app.use(errorHandler);

// 注册视频生成API
useGenratorVideoApi(app, db);

app.listen(PORT, () => {
  logger.info(`服务器启动成功`, {
    port: PORT,
    environment: process.env.NODE_ENV || 'development',
  });
  console.log(`服务器运行在端口 ${PORT}`);
});
