/**
 * 将文本字符串列表转换为 Agent 模型的输入格式。
 *
 * @param {string|Array<string>} texts - 要处理的文本字符串或字符串列表
 * @returns {Array<Object>} - 为 Agent 模型格式化的字典列表
 */
const prepareTextInputs = (texts) => {
  const inputs = [];

  // 如果输入是字符串，将其转换为数组
  if (typeof texts === "string") {
    texts = [texts];
  }

  // 将每个文本字符串添加到输入中
  for (const text of texts) {
    inputs.push({
      type: "text",
      content: text,
    });
  }

  return inputs;
};

// 修改导出方式
module.exports = { prepareTextInputs };
