# 故事板提示词更新总结

## 更新完成 ✅

### 主要改进

1. **更清晰的技术规格**
   - 明确16:9视频比例和坐标系范围
   - 添加安全区域建议：X轴 [-6.5, 6.5]，Y轴 [-3.5, 3.5]
   - 强调精确坐标描述要求

2. **更结构化的输出格式**
   - **6个核心部分**：开场设定、核心元素清单、动画时间轴、文本与公式展示、重点强调策略、场景转换
   - **详细的子要求**：每个部分都有具体的指导内容
   - **技术实现提示**：包含Manim对象类型、动画方法、相关插件等

3. **更实用的指导内容**
   - **坐标示例**：提供具体的坐标变化示例
   - **时间轴描述**：按时间段详细描述动画过程
   - **LaTeX格式**：明确数学公式的表示方法
   - **插件支持**：集成相关插件信息

### 文件修改

1. **提示词文件**：`generatorVideo/taskGenerator/promptsRaw/prompts.js`
   - 函数：`_promptSceneVisionStoryboard`
   - 新增：`{relevant_plugins}` 参数支持

2. **函数接口**：`generatorVideo/taskGenerator/index.js`
   - 函数：`getPromptSceneVisionStoryboard`
   - 参数：`sceneNumber, topic, description, sceneOutline, sceneContent, relevantPlugins`

3. **测试文件**：`test_storyboard_prompt.js`
   - 18项检查全部通过
   - 验证所有关键元素和格式

### 新提示词特点

#### 输入参数
- **场景编号**：当前场景的序号
- **主题和描述**：项目基本信息
- **完整场景大纲**：所有场景的概览
- **当前场景内容**：具体场景的详细信息
- **相关插件**：可用的Manim插件列表

#### 输出结构
```
<SCENE_VISION_STORYBOARD_PLAN>
1. 开场设定
2. 核心元素清单
3. 动画时间轴
4. 文本与公式展示
5. 重点强调策略
6. 场景转换
技术实现提示
</SCENE_VISION_STORYBOARD_PLAN>
```

### 预期效果

新的故事板提示词将生成：
1. **更精确的坐标描述** - 所有图形都有明确的坐标位置
2. **更详细的时间轴** - 按时间段描述动画过程
3. **更实用的技术指导** - 直接指导Manim代码编写
4. **更完整的视觉规划** - 从开场到结尾的完整描述

### 下一步

故事板提示词优化完成，下一个流程是：
**技术实现生成** (`generateTechnicalImplementation`)

这个流程将基于场景大纲和故事板，生成具体的Manim代码实现计划。

### 使用方法

1. 启动后端服务
2. 生成场景大纲
3. 点击"一键生成所有故事板"
4. 查看优化后的故事板描述

新的提示词将为后续的技术实现提供更好的基础！
