const fs = require('fs');
const path = require('path');
// 将 import 改为 require
const { getBannedReasonings } = require('./taskGenerator/index.js');
const { VideoPlanner } = require('./src/core/videoPlanner.js');
const { CodeGenerator } = require('./src/core/codeGenerator.js');
const { VideoRenderer } = require('./src/core/videoRenderer.js');
const { llmLangChainWrapper } = require('./mllmTools/llmLangChain.js');
const AWS = require('aws-sdk');
const {
  _codeFontSize,
  _codeDisable,
  _codeLimit,
  _promptManimCheatsheet,
} = require('./taskGenerator/promptsRaw/prompts.js');
// 配置 AWS S3 凭证（推荐使用环境变量或配置文件）
AWS.config.update({
  accessKeyId: '********************',
  secretAccessKey: 'Eq8XWHCEf/ESUs/WGDBtuapkIKPLLMPn0feR2rVN',
  region: 'us-west-2',
});
const s3 = new AWS.S3({ apiVersion: '2006-03-01' });

class VideoGenerator {
  /**
   * 用于使用AI模型生成manim视频的类
   *
   * 该类通过管理场景规划、代码生成和视频渲染来协调视频生成流程。
   * 它支持并发场景处理、视觉代码修复和RAG（检索增强生成）。
   */
  constructor({
    modelName,
    verbose = false,
    temperature = 0.7,
    helperModel = null,
    rootPath,
    useRag = false,
    useContextLearning = false,
    contextLearningPath = 'data/context_learning',
    chromaDbPath = 'data/rag/chroma_db',
    manimDocsPath = 'data/rag/manim_docs',
    embeddingModel = 'text-embedding-v3',
    useVisualFixCode = false,
    useLangfuse = true,
  }) {
    this.outputDir = 'output';
    this.codeModelName = 'claude-sonnet3.7'; // 使用Claude Sonnet，代码生成质量更好
    // this.codeModelName = 'qwen-';
    // this.codeModelName = 'gpt-4o'; // GPT-4o代码生成质量较差
    // this.codeModelName = "qwq-1";

    this.modelName = 'gpt-4o'; // 规划模型也使用GPT-4o
    this.temperature = 0.7;
    this.verbose = false;
    // 主题 描述 大纲
    // 定义三个模型
    this.plannerModel = llmLangChainWrapper({
      modelName: this.modelName,
      temperature: this.temperature,
      verbose: this.verbose,
    });
    this.helperModel = llmLangChainWrapper({
      modelName: this.modelName,
      temperature: this.temperature,
      verbose: this.verbose,
    });
    this.sceneModel = llmLangChainWrapper({
      modelName: this.codeModelName,
      temperature: this.temperature,
      verbose: this.verbose,
    });
    this.bannedReasonings = getBannedReasonings();

    // 初始化各个模块
    this.planner = new VideoPlanner({
      plannerModel: this.plannerModel,
      helperModel: this.helperModel || this.plannerModel,
      useContextLearning,
      contextLearningPath,
      useRag,
      sessionId: this.sessionId,
      chromaDbPath,
      manimDocsPath,
      embeddingModel,
      useLangfuse,
    });

    this.codeGenerator = new CodeGenerator({
      sceneModel: this.sceneModel,
      helperModel: this.helperModel || this.sceneModel,
      outputDir: this.outputDir,
      printResponse: verbose,
      useRag,
      useContextLearning,
      contextLearningPath,
      chromaDbPath,
      manimDocsPath,
      embeddingModel,
      useVisualFixCode,
      useLangfuse,
      sessionId: this.sessionId,
    });

    this.videoRenderer = new VideoRenderer({
      outputDir: this.outputDir,
      printResponse: verbose,
      useVisualFixCode,
    });
  }
  async generateSceneOutline({ topic = null, description = null }) {
    const sceneOutline = await this.planner.generateSceneOutline(
      topic,
      description
    );
    return sceneOutline;
  }
  async generateStoryboard({
    topic,
    description,
    sceneNum,
    sceneContent,
    sceneOutline = '',
  }) {
    const storyboard = await this.planner._generateStoryboardSingle({
      topic,
      description,
      sceneNum,
      sceneContent,
      sceneOutline,
    });
    return storyboard;
  }
  async generateTechnicalImplementationSingle({
    topic,
    description,
    sceneNum,
    sceneContent,
    storyContent,
  }) {
    const implementationPlan =
      await this.planner._generateTechnicalImplementationSingle({
        topic,
        description,
        sceneContent,
        storyContent,
        sceneNum,
      });
    return implementationPlan;
  }

  async generateAnimationNarrationSingle({
    topic,
    description,
    sceneNum,
    sceneContent,
    storyContent,
    sceneOutline,
  }) {
    const narration = await this.planner._generateAnimationNarrationSingle({
      topic,
      description,
      sceneNum,
      sceneContent,
      storyContent,
      sceneOutline,
    });
    return narration;
  }

  /**
   * 处理单个场景
   * @param {number} i - 场景索引
   * @param {string} sceneOutline - 整体场景大纲
   * @param {string} sceneImplementation - 此场景的实现计划
   * @param {string} topic - 视频主题
   * @param {string} description - 视频内容描述
   * @param {number} maxRetries - 代码修复尝试的最大次数
   * @param {string} filePrefix - 文件命名前缀
   * @param {string} sessionId - 会话ID
   * @param {string} sceneTraceId - 此场景的跟踪ID
   * @returns {Promise<void>}
   */
  async processScene({
    sceneNum,
    sceneContent,
    sceneOutline,
    sceneImplementation,
    topic,
    description,
    maxRetries,
    sessionId,
  }) {
    let currVersion = 0;
    const ragQueriesCache = {}; // 初始化RAG查询缓存
    const codeDir = path.join(this.outputDir, sessionId, `code`);
    const mediaDir = path.join(this.outputDir, sessionId, `media`);
    fs.mkdirSync(mediaDir, { recursive: true });
    fs.mkdirSync(codeDir, { recursive: true });

    console.log(`xxr: 创建media目录mediaDir:${mediaDir}`);

    // 步骤3A：生成初始manim代码
    const code = await this.codeGenerator.generateManimCode({
      topic,
      description,
      sceneContent,
      sceneOutline,
      sceneImplementation,
      sceneNumber: sceneNum,
      additionalContext: [
        _promptManimCheatsheet,
        _codeFontSize,
        // _codeLimit,
        _codeDisable,
      ],
      sessionId,
      ragQueriesCache,
    });

    // 保存初始代码和日志
    fs.writeFileSync(
      path.join(codeDir, `scene${sceneNum}_v${currVersion}.py`),
      code
    );

    // 步骤3B：编译并在需要时修复代码
    let errorMessage = null;
    let currentCode = code;
    console.log('xxr: 开始循环渲染场景');
    while (true) {
      // 重试循环由break语句控制
      const result = await this.videoRenderer.renderScene({
        code: currentCode,
        sceneNum,
        currVersion,
        codeDir,
        mediaDir,
        maxRetries,
        useVisualFixCode: this.useVisualFixCode,
        visualSelfReflectionFunc: this.codeGenerator.visualSelfReflection,
        bannedReasonings: this.bannedReasonings,
        topic,
      });
      console.log(`xxr: 渲染结果：`, result);
      currentCode = result[0];
      errorMessage = result[1];

      if (errorMessage === null) {
        // 如果errorMessage为null，则渲染成功
        console.log(`xxr: 渲染成功scene ${sceneNum}`);
        return { success: true, error: null };
      }

      if (currVersion >= maxRetries) {
        // 达到最大重试次数
        console.log(
          `xxr: 重试完成，渲染失败scene ${sceneNum}, error: ${errorMessage}`
        );
        return { success: false, error: errorMessage };
      }
      currVersion++;
      // 如果程序运行到这里，说明代码未成功渲染
      const fixResult = await this.codeGenerator.fixCodeErrors({
        implementationPlan: sceneImplementation,
        code: currentCode,
        error: errorMessage,
        topic,
        sceneNumber: sceneNum,
        ragQueriesCache,
      });

      currentCode = fixResult[0];
      const fixLog = fixResult[1];

      fs.writeFileSync(
        path.join(codeDir, `scene${sceneNum}_v${currVersion}_fix_log.txt`),
        fixLog
      );
      fs.writeFileSync(
        path.join(codeDir, `scene${sceneNum}_v${currVersion}.py`),
        currentCode
      );
      console.log(
        `xxr:修复后的提取代码前文案保存到：scene${sceneNum}_v${currVersion}_fix_log.txt`
      );

      console.log(
        `xxr:修复后的代码保存到：${codeDir}/scene${sceneNum}_v${currVersion}.py`
      );
    }
  }
  async generateVideoMerge({ sessionId, sceneCount }) {
    return await this.videoRenderer.combineVideos({
      sessionId,
      sceneCount,
    });
  }
  async uploadAllToS3(sessionId) {
    const videoOutputDir = path.join(this.outputDir, sessionId);
    const outputVideoPath = path.join(
      videoOutputDir,
      `${sessionId}_combined.mp4`
    );
    const outputSrtPath = path.join(
      videoOutputDir,
      `${sessionId}_combined.srt`
    );
    await this.uploadToS3(
      outputVideoPath,
      'pa-s3-prod',
      path.join(`generateVideo/${sessionId}`, path.basename(outputVideoPath))
    );
    await this.uploadToS3(
      outputSrtPath,
      'pa-s3-prod',
      path.join(`generateVideo/${sessionId}`, path.basename(outputSrtPath))
    );
    return {
      videoUrl: `https://download-pa-s3.thethinkacademy.com/generateVideo/${sessionId}/${path.basename(
        outputVideoPath
      )}`,
      srtUrl: `https://download-pa-s3.thethinkacademy.com/generateVideo/${sessionId}/${path.basename(
        outputSrtPath
      )}`,
    };
  }
  async uploadToS3(filePath, bucketName, key) {
    // 根据文件扩展名设置 Content-Type
    const extname = path.extname(filePath);
    let contentType = 'application/octet-stream'; // 默认值

    switch (extname) {
      case '.html':
        contentType = 'text/html';
        break;
      case '.css':
        contentType = 'text/css';
        break;
      case '.js':
        contentType = 'application/javascript';
        break;
      case '.jpeg':
      case '.jpg':
        contentType = 'image/jpeg';
        break;
      case '.png':
        contentType = 'image/png';
        break;
      case '.gif':
        contentType = 'image/gif';
        break;
      case '.bmp':
        contentType = 'image/bmp';
        break;
      case '.svg':
        contentType = 'image/svg+xml';
        break;
      case '.webp':
        contentType = 'image/webp';
        break;
      case '.mp4':
        contentType = 'video/mp4';
        break;
      case '.srt':
        contentType = 'application/x-subrip';
        break;
      // 添加其他类型（如字体文件、视频文件等），根据需求扩展
      default:
        // 保持默认值
        break;
    }

    try {
      const fileContent = await fs.promises.readFile(filePath);
      const params = {
        Bucket: bucketName,
        Key: key,
        Body: fileContent,
      };
      if (extname !== '.zip') {
        params.ContentType = contentType;
      }
      const data = await s3.upload(params).promise();
      console.log(`文件已上传至 S3: ${data.Location}`);
    } catch (err) {
      console.error('上传到 S3 失败:', err);
    }
  }
}
module.exports = VideoGenerator;
