-- AI交互日志表
CREATE TABLE IF NOT EXISTS ai_interaction_log (
  id INT AUTO_INCREMENT PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  step_type VARCHAR(50) NOT NULL COMMENT '步骤类型：scene_outline, storyboard, animation, etc.',
  scene_num VARCHAR(10) DEFAULT NULL COMMENT '场景编号，如果适用',
  
  -- 输入数据
  input_prompt TEXT NOT NULL COMMENT '发送给AI的完整提示词',
  input_params JSON DEFAULT NULL COMMENT '输入参数（topic, description等）',
  
  -- AI返回数据
  ai_raw_response LONGTEXT DEFAULT NULL COMMENT 'AI的原始完整返回',
  ai_response_tokens INT DEFAULT NULL COMMENT 'AI返回的token数量',
  ai_model VARCHAR(100) DEFAULT NULL COMMENT '使用的AI模型',
  
  -- 解析结果
  parsed_data JSON DEFAULT NULL COMMENT '解析后的结构化数据',
  parsing_success BOOLEAN DEFAULT FALSE COMMENT '解析是否成功',
  parsing_error TEXT DEFAULT NULL COMMENT '解析错误信息',
  
  -- 存储结果
  storage_success BOOLEAN DEFAULT FALSE COMMENT '数据库存储是否成功',
  storage_error TEXT DEFAULT NULL COMMENT '存储错误信息',
  
  -- 时间和状态
  request_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '请求时间',
  response_time TIMESTAMP DEFAULT NULL COMMENT '响应时间',
  duration_ms INT DEFAULT NULL COMMENT '处理耗时（毫秒）',
  status ENUM('pending', 'success', 'failed', 'partial') DEFAULT 'pending',
  
  -- 索引
  INDEX idx_session_id (session_id),
  INDEX idx_step_type (step_type),
  INDEX idx_request_time (request_time),
  INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI交互完整日志表';

-- 创建视图，方便查询会话的所有步骤
CREATE OR REPLACE VIEW v_session_ai_steps AS
SELECT 
  session_id,
  step_type,
  scene_num,
  status,
  parsing_success,
  storage_success,
  request_time,
  duration_ms,
  CASE 
    WHEN ai_raw_response IS NOT NULL THEN CHAR_LENGTH(ai_raw_response)
    ELSE 0 
  END as response_length,
  CASE 
    WHEN parsing_error IS NOT NULL THEN 'YES'
    ELSE 'NO'
  END as has_error
FROM ai_interaction_log
ORDER BY session_id, request_time;

-- 创建调试查询存储过程
DELIMITER //
CREATE PROCEDURE GetSessionDebugInfo(IN p_session_id VARCHAR(255))
BEGIN
  -- 会话基本信息
  SELECT 'SESSION_INFO' as info_type, t.* FROM topic t WHERE t.session_id = p_session_id;
  
  -- AI交互步骤概览
  SELECT 'AI_STEPS_OVERVIEW' as info_type, * FROM v_session_ai_steps WHERE session_id = p_session_id;
  
  -- 失败的步骤详情
  SELECT 'FAILED_STEPS' as info_type, 
    step_type, scene_num, parsing_error, storage_error, ai_raw_response
  FROM ai_interaction_log 
  WHERE session_id = p_session_id AND (parsing_success = FALSE OR storage_success = FALSE);
  
  -- 场景数据
  SELECT 'SCENE_DATA' as info_type, * FROM topic_scene WHERE session_id = p_session_id ORDER BY scene_num;
END //
DELIMITER ;
