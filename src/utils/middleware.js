const { logger } = require('./logger');

// HTTP请求日志中间件
function requestLogger(options = {}) {
  const skip = options.skip || (() => false);
  const includeBody = options.includeBody !== false;
  const maxBodyLength = options.maxBodyLength || 1000;

  return (req, res, next) => {
    // 跳过某些请求
    if (skip(req, res)) {
      return next();
    }

    const startTime = Date.now();

    // 记录请求开始
    const requestMeta = {
      method: req.method,
      url: req.originalUrl || req.url,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      headers: req.headers,
    };

    // 如果需要记录请求体
    if (includeBody && req.body) {
      let body = JSON.stringify(req.body);
      if (body.length > maxBodyLength) {
        body = body.substring(0, maxBodyLength) + '...';
      }
      requestMeta.body = body;
    }

    logger.info('HTTP请求开始', requestMeta);

    // 重写res.end方法来记录响应
    const originalEnd = res.end;
    res.end = function (chunk, encoding) {
      const duration = Date.now() - startTime;

      // 记录响应
      logger.httpRequest(req, res, duration);

      // 调用原始的end方法
      originalEnd.call(this, chunk, encoding);
    };

    next();
  };
}

// 错误日志中间件
function errorLogger(err, req, res, next) {
  const errorMeta = {
    method: req.method,
    url: req.originalUrl || req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip || req.connection.remoteAddress,
    stack: err.stack,
    statusCode: err.statusCode || 500,
  };

  logger.error('HTTP请求发生错误', errorMeta);
  next(err);
}

// 未捕获异常处理
function setupGlobalErrorHandling() {
  process.on('uncaughtException', (error) => {
    logger.error('未捕获的异常', {
      error: error.message,
      stack: error.stack,
    });

    // 优雅关闭
    process.exit(1);
  });

  process.on('unhandledRejection', (reason, promise) => {
    logger.error('未处理的Promise拒绝', {
      reason: reason,
      promise: promise,
    });
  });

  process.on('SIGTERM', () => {
    logger.info('收到SIGTERM信号，正在优雅关闭');
    process.exit(0);
  });

  process.on('SIGINT', () => {
    logger.info('收到SIGINT信号，正在优雅关闭');
    process.exit(0);
  });
}

module.exports = {
  requestLogger,
  errorLogger,
  setupGlobalErrorHandling,
};
