/**
 * 打印视频生成过程中的格式化响应
 *
 * 打印带有分隔符和标题的格式化响应，以提高可读性
 *
 * @param {string} responseType - 响应类型（例如，'Scene Plan', 'Implementation Plan'）
 * @param {string} theoremName - 正在处理的定理名称
 * @param {string} content - 要打印的内容
 * @param {string} separator - 用于视觉区分的分隔符字符串，默认为50个等号
 * @returns {void}
 */
function printResponse(
  responseType,
  theoremName,
  content,
  separator = "=".repeat(50)
) {
  console.log(`\n${separator}`);
  console.log(`${responseType} for ${theoremName}:`);
  console.log(`${separator}\n`);
  console.log(content);
  console.log(`\n${separator}`);
}

/**
 * 从文本响应中提取代码块
 *
 * 提取由```python标记分隔的Python代码块。如果找不到代码块，则返回整个响应文本
 *
 * @param {string} responseText - 包含代码块的文本响应
 * @returns {string} - 通过换行符连接的提取代码块，或者如果找不到块，则返回完整响应
 */
function extractCode(responseText) {
  let code = "";
  const codeBlockRegex = /```python\n([\s\S]*?)\n```/g;
  const codeBlocks = [];
  let match;

  while ((match = codeBlockRegex.exec(responseText)) !== null) {
    codeBlocks.push(match[1]);
  }

  if (codeBlocks.length > 0) {
    code = codeBlocks.join("\n\n");
  } else if (!responseText.includes("```")) {
    // 如果没有代码块，返回整个响应
    code = responseText;
  }

  return code;
}

/**
 * 从文本响应中提取并解析JSON内容
 *
 * 尝试直接将响应解析为JSON，如果直接解析失败，则尝试从代码块中提取JSON
 *
 * @param {string} response - 包含JSON内容的文本响应
 * @returns {object} - 解析为字典的JSON内容，如果解析失败则返回空数组
 */
function extractJson(response) {
  try {
    return JSON.parse(response);
  } catch (e) {
    console.log(`Warning: Failed to parse JSON content: ${e}`);
    // 如果JSON解析失败，尝试提取```json和```之间的内容
    let match = response.match(/```json\n([\s\S]*?)\n```/);
    if (!match) {
      // 如果没有匹配到```json，尝试提取```和```之间的内容
      match = response.match(/```\n([\s\S]*?)\n```/);
    }

    if (match) {
      const evaluationContent = match[1];
      try {
        return JSON.parse(evaluationContent);
      } catch (innerError) {
        console.log(
          `Warning: Failed to parse extracted JSON content: ${innerError}`
        );
        return [];
      }
    } else {
      // 返回空数组
      console.log(
        `Warning: Failed to extract valid JSON content from ${response}`
      );
      return [];
    }
  }
}

/**
 * 将Unicode符号转换为LaTeX源代码
 *
 * 将Unicode下标和上标转换为LaTeX格式，可选择进行完整的Unicode解析
 *
 * @param {string} text - 包含要转换的Unicode符号的文本
 * @param {boolean} parseUnicode - 是否执行完整的Unicode到LaTeX转换，默认为true
 * @returns {string} - 将Unicode符号转换为LaTeX格式的文本
 */
function fixUnicodeToLatex(text, parseUnicode = true) {
  // Unicode下标到latex格式的映射
  const subscripts = {
    "₀": "_0",
    "₁": "_1",
    "₂": "_2",
    "₃": "_3",
    "₄": "_4",
    "₅": "_5",
    "₆": "_6",
    "₇": "_7",
    "₈": "_8",
    "₉": "_9",
    "₊": "_+",
    "₋": "_-",
  };

  // Unicode上标到latex格式的映射
  const superscripts = {
    "⁰": "^0",
    "¹": "^1",
    "²": "^2",
    "³": "^3",
    "⁴": "^4",
    "⁵": "^5",
    "⁶": "^6",
    "⁷": "^7",
    "⁸": "^8",
    "⁹": "^9",
    "⁺": "^+",
    "⁻": "^-",
  };

  // 合并下标和上标映射
  const allMappings = { ...subscripts, ...superscripts };

  // 替换所有Unicode字符
  for (const [unicodeChar, latexFormat] of Object.entries(allMappings)) {
    text = text.replace(new RegExp(unicodeChar, "g"), latexFormat);
  }

  if (parseUnicode) {
    // 注意：JavaScript中没有直接等价于pylatexenc的库
    // 这里需要实现或使用第三方库来处理完整的Unicode到LaTeX转换
    console.log(
      "Warning: Full Unicode to LaTeX conversion requires additional implementation"
    );
    // 可以考虑使用如mathjax-node等库来处理
  }

  return text;
}

/**
 * 从文本响应中提取XML内容
 *
 * 提取```xml标记之间的XML内容。如果找不到XML块，则返回完整响应
 *
 * @param {string} response - 包含XML内容的文本响应
 * @returns {string} - 提取的XML内容，如果找不到XML块，则返回完整响应
 */
function extractXml(response) {
  try {
    const match = response.match(/```xml\n([\s\S]*?)\n```/);
    return match ? match[1] : response;
  } catch (error) {
    console.error("Error extracting XML:", error);
    return response;
  }
}

module.exports = {
  printResponse,
  extractCode,
  extractJson,
  fixUnicodeToLatex,
  extractXml,
};
