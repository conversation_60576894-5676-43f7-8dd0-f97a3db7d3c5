// 从prompts_raw.js导入所有模板字符串
const {
  _promptCodeGeneration,
  _promptFixError,
  _promptVisualFixError,
  _promptScenePlan,
  _promptSceneVisionStoryboard,
  _promptSceneTechnicalImplementation,
  _promptSceneAnimationNarration,
  _promptAnimationSimple,
  _promptAnimationFixError,
  _promptAnimationRagQueryGeneration,
  _promptAnimationRagQueryGenerationFixError,
  _bannedReasonings,
  _promptContextLearningScenePlan,
  _promptContextLearningVisionStoryboard,
  _promptContextLearningTechnicalImplementation,
  _promptContextLearningAnimationNarration,
  _promptContextLearningCode,
  _promptDetectPlugins,
  _promptRagQueryGenerationCode,
  _promptRagQueryGenerationVisionStoryboard,
  _promptRagQueryGenerationTechnical,
  _promptRagQueryGenerationNarration,
  _promptRagQueryGenerationFixError,
  _promptVisualSelfReflection,
} = require('./promptsRaw/prompts.js');

/**
 * 生成场景计划提示
 * @param {string} topic - 主题
 * @param {string} description - 描述
 * @returns {string} 格式化后的提示
 */
function getPromptScenePlan(topic, description) {
  const prompt = _promptScenePlan
    .replace(/{topic}/g, topic)
    .replace(/{description}/g, description);

  return prompt;
}

/**
 * 生成场景视觉故事板提示
 * @param {number} sceneNumber - 场景编号
 * @param {string} topic - 主题
 * @param {string} description - 描述
 * @param {string} sceneOutline - 场景大纲
 * @param {Array<string>} relevantPlugins - 相关插件列表
 * @returns {string} 格式化后的提示
 */
function getPromptSceneVisionStoryboard(
  sceneNumber,
  topic,
  description,
  sceneOutline,
  sceneContent,
  relevantPlugins
) {
  const prompt = _promptSceneVisionStoryboard
    .replace(/{scene_number}/g, sceneNumber)
    .replace(/{topic}/g, topic)
    .replace(/{description}/g, description)
    .replace(/{scene_outline}/g, sceneOutline)
    .replace(/{scene_content}/g, sceneContent)
    .replace(
      '{relevant_plugins}',
      relevantPlugins ? relevantPlugins.join(', ') : '无特殊插件'
    );

  return prompt;
}

/**
 * 生成场景技术实现提示
 * @param {number} sceneNumber - 场景编号
 * @param {string} topic - 主题
 * @param {string} description - 描述
 * @param {string} sceneOutline - 场景大纲
 * @param {string} sceneVisionStoryboard - 场景视觉故事板
 * @param {Array<string>} relevantPlugins - 相关插件列表
 * @param {string|Array<string>} additionalContext - 附加上下文
 * @returns {string} 格式化后的提示
 */
function getPromptSceneTechnicalImplementation(
  sceneNumber,
  topic,
  description,
  sceneOutline,
  sceneVisionStoryboard,
  relevantPlugins,
  additionalContext = null
) {
  let prompt = _promptSceneTechnicalImplementation
    .replace('{scene_number}', sceneNumber)
    .replace('{topic}', topic)
    .replace('{description}', description)
    .replace('{scene_outline}', sceneOutline)
    .replace('{scene_vision_storyboard}', sceneVisionStoryboard)
    .replace('{relevant_plugins}', relevantPlugins.join(', '));

  if (additionalContext !== null) {
    if (typeof additionalContext === 'string') {
      prompt += `\nAdditional context: ${additionalContext}`;
    } else if (
      Array.isArray(additionalContext) &&
      additionalContext.length > 0
    ) {
      prompt += `\nAdditional context: ${additionalContext[0]}`;
      if (additionalContext.length > 1) {
        prompt += `\n${additionalContext.slice(1).join('\n')}`;
      }
    }
  }

  return prompt;
}

/**
 * 生成场景动画和旁白提示
 * @param {number} sceneNumber - 场景编号
 * @param {string} topic - 主题
 * @param {string} description - 描述
 * @param {string} sceneOutline - 场景大纲
 * @param {string} sceneVisionStoryboard - 场景视觉故事板
 * @param {string} technicalImplementationPlan - 技术实现计划
 * @param {Array<string>} relevantPlugins - 相关插件列表
 * @returns {string} 格式化后的提示
 */
function getPromptSceneAnimationNarration({
  sceneNum,
  topic,
  description,
  sceneContent,
  storyContent,
  sceneOutline,
  relevantPlugins,
}) {
  const prompt = _promptSceneAnimationNarration
    .replace(/{scene_number}/g, sceneNum)
    .replace(/{topic}/g, topic)
    .replace(/{description}/g, description)
    .replace(/{scene_vision_storyboard}/g, storyContent)
    .replace(/{scene_content}/g, sceneContent)
    .replace(/{scene_outline}/g, sceneOutline)
    .replace(/{relevant_plugins}/g, relevantPlugins.join(', '));

  return prompt;
}

/**
 * 生成代码生成提示
 * @param {string} topic - 主题
 * @param {string} description - 描述
 * @param {string} sceneOutline - 场景大纲
 * @param {string} sceneImplementation - 场景实现
 * @param {number} sceneNumber - 场景编号
 * @param {string|Array<string>} additionalContext - 附加上下文
 * @returns {string} 格式化后的提示
 */
function getPromptCodeGeneration({
  sceneContent,
  sceneOutline,
  sceneImplementation,
  topic,
  description,
  sceneNumber,
  additionalContext = null,
}) {
  let prompt = _promptCodeGeneration
    .replace(/{topic}/g, topic)
    .replace(/{description}/g, description)
    .replace(/{scene_content}/g, sceneContent)
    .replace(/{scene_outline}/g, sceneOutline)
    .replace(/{scene_implementation}/g, sceneImplementation)
    .replace(/{scene_number}/g, sceneNumber);

  if (additionalContext !== null) {
    if (typeof additionalContext === 'string') {
      prompt += `\nAdditional context: ${additionalContext}`;
    } else if (
      Array.isArray(additionalContext) &&
      additionalContext.length > 0
    ) {
      prompt += `\nAdditional context: ${additionalContext[0]}`;
      if (additionalContext.length > 1) {
        prompt += `\n${additionalContext.slice(1).join('\n')}`;
      }
    }
  }

  return prompt;
}

/**
 * 生成修复错误提示
 * @param {string} implementationPlan - 实现计划
 * @param {string} manimCode - Manim代码
 * @param {string} error - 错误信息
 * @param {string|Array<string>} additionalContext - 附加上下文
 * @returns {string} 格式化后的提示
 */
function getPromptFixError({
  implementationPlan,
  manimCode,
  error,
  additionalContext = null,
}) {
  let prompt = _promptFixError
    .replace('{implementation_plan}', implementationPlan)
    .replace('{manim_code}', manimCode)
    .replace('{error_message}', error);

  if (additionalContext !== null) {
    if (typeof additionalContext === 'string') {
      prompt += `\nAdditional context: ${additionalContext}`;
    } else if (
      Array.isArray(additionalContext) &&
      additionalContext.length > 0
    ) {
      prompt += `\nAdditional context: ${additionalContext[0]}`;
      if (additionalContext.length > 1) {
        prompt += `\n${additionalContext.slice(1).join('\n')}`;
      }
    }
  }

  return prompt;
}

/**
 * 生成视觉修复错误提示
 * @param {string} implementation - 实现
 * @param {string} generatedCode - 生成的代码
 * @returns {string} 格式化后的提示
 */
function getPromptVisualFixError(implementation, generatedCode) {
  const prompt = _promptVisualFixError
    .replace('{implementation}', implementation)
    .replace('{generated_code}', generatedCode);

  return prompt;
}

/**
 * 获取禁止的推理列表
 * @returns {Array<string>} 禁止的推理列表
 */
function getBannedReasonings() {
  return _bannedReasonings.split('\n');
}

/**
 * 生成RAG查询生成视觉故事板提示
 * @param {string} scenePlan - 场景计划
 * @param {string} relevantPlugins - 相关插件
 * @returns {string} 格式化后的提示
 */
function getPromptRagQueryGenerationVisionStoryboard(
  scenePlan,
  relevantPlugins
) {
  const prompt = _promptRagQueryGenerationVisionStoryboard
    .replace('{scene_plan}', scenePlan)
    .replace('{relevant_plugins}', relevantPlugins);

  return prompt;
}

/**
 * 生成RAG查询生成技术提示
 * @param {string} storyboard - 故事板
 * @param {string} relevantPlugins - 相关插件
 * @returns {string} 格式化后的提示
 */
function getPromptRagQueryGenerationTechnical(storyboard, relevantPlugins) {
  const prompt = _promptRagQueryGenerationTechnical
    .replace('{storyboard}', storyboard)
    .replace('{relevant_plugins}', relevantPlugins);

  return prompt;
}

/**
 * 生成RAG查询生成旁白提示
 * @param {string} storyboard - 故事板
 * @param {string} relevantPlugins - 相关插件
 * @returns {string} 格式化后的提示
 */
function getPromptRagQueryGenerationNarration(storyboard, relevantPlugins) {
  const prompt = _promptRagQueryGenerationNarration
    .replace('{storyboard}', storyboard)
    .replace('{relevant_plugins}', relevantPlugins);

  return prompt;
}

/**
 * 生成RAG查询生成代码提示
 * @param {string} implementationPlan - 实现计划
 * @param {string} relevantPlugins - 相关插件
 * @returns {string} 格式化后的提示
 */
function getPromptRagQueryGenerationCode(implementationPlan, relevantPlugins) {
  const prompt = _promptRagQueryGenerationCode
    .replace('{implementation_plan}', implementationPlan)
    .replace('{relevant_plugins}', relevantPlugins);

  return prompt;
}

/**
 * 生成RAG查询生成修复错误提示
 * @param {string} error - 错误信息
 * @param {string} code - 代码
 * @param {string} relevantPlugins - 相关插件
 * @returns {string} 格式化后的提示
 */
function getPromptRagQueryGenerationFixError(error, code, relevantPlugins) {
  const prompt = _promptRagQueryGenerationFixError
    .replace('{error}', error)
    .replace('{code}', code)
    .replace('{relevant_plugins}', relevantPlugins);

  return prompt;
}
function getPromptVisualSelfReflection(code) {
  const prompt = _promptVisualSelfReflection.replace('{code}', code);

  return prompt;
}

/**
 * 生成上下文学习场景计划提示
 * @param {string} examples - 示例
 * @returns {string} 格式化后的提示
 */
function getPromptContextLearningScenePlan(examples) {
  const prompt = _promptContextLearningScenePlan.replace(
    '{examples}',
    examples
  );

  return prompt;
}

/**
 * 生成上下文学习视觉故事板提示
 * @param {string} examples - 示例
 * @returns {string} 格式化后的提示
 */
function getPromptContextLearningVisionStoryboard(examples) {
  const prompt = _promptContextLearningVisionStoryboard.replace(
    '{examples}',
    examples
  );

  return prompt;
}

/**
 * 生成上下文学习技术实现提示
 * @param {string} examples - 示例
 * @returns {string} 格式化后的提示
 */
function getPromptContextLearningTechnicalImplementation(examples) {
  const prompt = _promptContextLearningTechnicalImplementation.replace(
    '{examples}',
    examples
  );

  return prompt;
}

/**
 * 生成上下文学习动画旁白提示
 * @param {string} examples - 示例
 * @returns {string} 格式化后的提示
 */
function getPromptContextLearningAnimationNarration(examples) {
  const prompt = _promptContextLearningAnimationNarration.replace(
    '{examples}',
    examples
  );

  return prompt;
}

/**
 * 生成上下文学习代码提示
 * @param {string} examples - 示例
 * @returns {string} 格式化后的提示
 */
function getPromptContextLearningCode({ examples }) {
  const prompt = _promptContextLearningCode.replace('{examples}', examples);

  return prompt;
}

/**
 * 生成检测插件提示
 * @param {string} topic - 主题
 * @param {string} description - 描述
 * @param {string} pluginDescriptions - 插件描述
 * @returns {string} 格式化后的提示
 */
function getPromptDetectPlugins(topic, description, pluginDescriptions) {
  const prompt = _promptDetectPlugins
    .replace('{topic}', topic)
    .replace('{description}', description)
    .replace('{plugin_descriptions}', pluginDescriptions);

  return prompt;
}

/**
 * 生成动画提示
 * @param {string} topic - 主题
 * @param {string} description - 描述
 * @param {string|Array<string>} additionalContext - 附加上下文
 * @returns {string} 格式化后的提示
 */
function getPromptAnimation(topic, description, additionalContext = null) {
  let prompt = _promptAnimationSimple
    .replace('{topic}', topic)
    .replace('{description}', description);

  if (additionalContext !== null) {
    if (typeof additionalContext === 'string') {
      prompt += `\nAdditional context: ${additionalContext}`;
    } else if (
      Array.isArray(additionalContext) &&
      additionalContext.length > 0
    ) {
      prompt += `\nAdditional context: ${additionalContext[0]}`;
      if (additionalContext.length > 1) {
        prompt += `\n${additionalContext.slice(1).join('\n')}`;
      }
    }
  }

  return prompt;
}

/**
 * 生成动画修复错误提示
 * @param {string} textExplanation - 文本解释
 * @param {string} manimCode - Manim代码
 * @param {string} error - 错误信息
 * @param {string|Array<string>} additionalContext - 附加上下文
 * @returns {string} 格式化后的提示
 */
function getPromptAnimationFixError(
  textExplanation,
  manimCode,
  error,
  additionalContext = null
) {
  let prompt = _promptAnimationFixError
    .replace('{text_explanation}', textExplanation)
    .replace('{manim_code}', manimCode)
    .replace('{error_message}', error);

  if (additionalContext !== null) {
    if (typeof additionalContext === 'string') {
      prompt += `\nAdditional context: ${additionalContext}`;
    } else if (
      Array.isArray(additionalContext) &&
      additionalContext.length > 0
    ) {
      prompt += `\nAdditional context: ${additionalContext[0]}`;
      if (additionalContext.length > 1) {
        prompt += `\n${additionalContext.slice(1).join('\n')}`;
      }
    }
  }

  return prompt;
}

/**
 * 生成动画RAG查询生成提示
 * @param {string} topic - 主题
 * @param {string} context - 上下文
 * @param {string} relevantPlugins - 相关插件
 * @returns {string} 格式化后的提示
 */
function getPromptAnimationRagQueryGeneration(topic, context, relevantPlugins) {
  if (context === null) {
    context = '';
  }

  const prompt = _promptAnimationRagQueryGeneration
    .replace('{topic}', topic)
    .replace('{context}', context)
    .replace('{relevant_plugins}', relevantPlugins);

  return prompt;
}

/**
 * 生成动画RAG查询生成修复错误提示
 * @param {string} textExplanation - 文本解释
 * @param {string} error - 错误信息
 * @param {string} code - 代码
 * @returns {string} 格式化后的提示
 */
function getPromptAnimationRagQueryGenerationFixError(
  textExplanation,
  error,
  code
) {
  const prompt = _promptAnimationRagQueryGenerationFixError
    .replace('{text_explanation}', textExplanation)
    .replace('{error}', error)
    .replace('{code}', code);

  return prompt;
}

// 导出所有函数
module.exports = {
  getPromptScenePlan,
  getPromptSceneVisionStoryboard,
  getPromptSceneTechnicalImplementation,
  getPromptSceneAnimationNarration,
  getPromptCodeGeneration,
  getPromptFixError,
  getPromptVisualFixError,
  getBannedReasonings,
  getPromptRagQueryGenerationVisionStoryboard,
  getPromptRagQueryGenerationTechnical,
  getPromptRagQueryGenerationNarration,
  getPromptRagQueryGenerationCode,
  getPromptRagQueryGenerationFixError,
  getPromptContextLearningScenePlan,
  getPromptContextLearningVisionStoryboard,
  getPromptContextLearningTechnicalImplementation,
  getPromptContextLearningAnimationNarration,
  getPromptContextLearningCode,
  getPromptDetectPlugins,
  getPromptAnimation,
  getPromptAnimationFixError,
  getPromptAnimationRagQueryGeneration,
  getPromptAnimationRagQueryGenerationFixError,
  getPromptVisualSelfReflection,
};
