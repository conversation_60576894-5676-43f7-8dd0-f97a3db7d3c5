// 此文件通过 parse_prompt.js 自动生成

const _bannedReasonings = `evaluation cannot
can't assist
cannot assist
can't provide
cannot provide
can't evaluate
cannot evaluate
cannot be evaluated
cannot be rated
cannot be completed
cannot be assessed
cannot be scored
cannot be conducted
unable to evaluate
do not have the capability
do not have the ability
are photographs and not AI-generated
unable to provide the evaluation`;

const _codeBackground = `PLEASE DO NOT create another color background Rectangles. Default background (WHITE) is enough.
PLEASE DO NOT use WHITE color for any text. Use dark colors like BLA<PERSON>K, BLUE, GREEN for text visibility.
`;

const _codeColorCheatsheet = `MUST include the following color definitions if you use the colors in your code. ONLY USE THE COLORS BELOW.

WHITE = '#FFFFFF'
RED = '#FF0000'
GREEN = '#00FF00'
BLUE = '#0000FF'
YELLOW = '#FFFF00'
CYAN = '#00FFFF'
MAGENTA = '#FF00FF'
ORANGE = '#FFA500'
PURPLE = '#800080'
PINK = '#FFC0CB'
BROWN = '#A52A2A'
GRAY = '#808080'
TEAL = '#008080'
NAVY = '#000080'
OLIVE = '#808000'
MAROON = '#800000'
LIME = '#00FF00'
AQUA = '#00FFFF'
FUCHSIA = '#FF00FF'
SILVER = '#C0C0C0'
GOLD = '#FFD700'`;

const _codeDisable = ``;

const _codeFontSize = `If there is title text, font size is highly recommended to be 28.
If there are side labels, font size is highly recommended to be 24.
If there are formulas, font size is highly recommended to be 24.

However, if the text has more than 10 words, font size should be reduced further and mutiple lines should be used.`;

const _codeLimit = `Note that the frame width and height are 14.222222222222221 and 8.0 respectively. And the center of the frame is (0, 0, 0).
It means to avoid putting any object out of the frame, you should limit the x and y coordinates of the objects.
limit x to be within -7.0 and 7.0 for objects, and limit y to be within -4.0 and 4.0 for objects.
Place the objects near the center of the frame, without overlapping with each other.`;

const _promptAnimationFixError = `You are an expert Manim developer specializing in debugging and error resolution. Analyze the provided code and error message to provide a comprehensive fix and explanation.

<CONTEXT>
Text Explanation:
{text_explanation}

Manim Code Animation to complement the Text Explanation:
\`\`\`python
{manim_code}
\`\`\`

Error Message on code running:
{error_message}
</CONTEXT>

You MUST only output the following format (make sure to include the \`\`\`python and \`\`\` in the code):

<ERROR_ANALYSIS>
Error Type: [Syntax/Runtime/Logic/Other]
Error Location: [File/Line number/Component]
Root Cause: [Brief explanation of what caused the error]
Impact: [What functionality is affected]
</ERROR_ANALYSIS>

<SOLUTION>
[FIXES_REQUIRED]
- Fix 1: [Description]
  - Location: [Where to apply]
  - Change: [What to modify]
- Fix 2: [If applicable]
  ...

[CORRECTED_CODE]
\`\`\`python
# Complete corrected and fully implemented code, don't be lazy
# Include all necessary imports, definitions, and any additional code for the script to run successfully
\`\`\`

</SOLUTION>

Requirements:
1. Provide complete error analysis with specific line numbers where possible.
2. Include exact instructions for every code change.
3. Ensure that the [CORRECTED_CODE] section contains complete, executable Python code (not just code snippets). Do not assume context from the prompt.
4. Explain why the error occurred in plain language.
5. Include verification steps to confirm the error is resolved.
6. Suggest preventive measures for avoiding similar errors in the future.
7. If external assets (e.g., images, audio, video) are referenced, remove them.
8. Preserve all original code that is not causing the reported error. Do not remove or alter any intentional elements unnecessarily.
9. Follow best practices for code clarity and the current Manim version.`;

const _promptAnimationRagQueryGeneration = `You are an expert in Manim (Community Edition) and its plugins. Your task is to transform a topic for a Manim animation scene into queries that can be used to retrieve relevant documentation from both Manim core and any relevant plugins.

Your queries should include keywords related to the specific Manim classes, methods, functions, and *concepts* that are likely to be used to implement the scene, including any plugin-specific functionality. Focus on extracting the core concepts, actions, and vocabulary from the *entire* scene plan. Generate queries that are concise and target different aspects of the documentation (class reference, method usage, animation examples, conceptual explanations) across both Manim core and relevant plugins.

Here is the Topic (and the context):

{topic}. {context}

Based on the topic and the context, generate multiple human-like queries (maximum 5-7) for retrieving relevant documentation. Please ensure that the search targets are different so that the RAG can retrieve a diverse set of documents covering various aspects of the implementation.

**Specifically, ensure that:**
1. At least 1-2 queries are focused on retrieving information about Manim *function usage* in Manim scenes
2. If the topic and the context can be linked to the use of plugin functionality, include at least 1 query specifically targeting plugin documentation
3. Queries should be specific enough to distinguish between core Manim and plugin functionality when relevant

The above text explanations are relevant to these plugins: {relevant_plugins}

Output the queries in the following format:
\`\`\`json
[
    {{"query": "content of query 1", "type": "manim_core/name_of_the_plugin"}},
    {{"query": "content of query 2", "type": "manim_core/name_of_the_plugin"}},
    {{"query": "content of query 3", "type": "manim_core/name_of_the_plugin"}},
    {{"query": "content of query 4", "type": "manim_core/name_of_the_plugin"}},
    {{"query": "content of query 5", "type": "manim_core/name_of_the_plugin"}},
    {{"query": "content of query 6", "type": "manim_core/name_of_the_plugin"}},
    {{"query": "content of query 7", "type": "manim_core/name_of_the_plugin"}},
]
\`\`\``;

const _promptAnimationRagQueryGenerationFixError = `You are an expert in Manim (Community Edition) and its plugins. Your task is to transform a complete implementation plan for a Manim animation scene into queries that can be used to retrieve relevant documentation from both Manim core and any relevant plugins. The implementation plan will describe the scene's vision, technical implementation, and animation strategy.

Here is the Text Explanation (Implementation Plan) as the context:

{text_explanation}

The error message will describe a problem encountered while running Manim code. Your queries should include keywords related to the specific Manim classes, methods, functions, and *concepts* that are likely related to the error, including any plugin-specific functionality. Focus on extracting the core concepts, actions, and vocabulary from the error message itself and the code snippet that produced the error. Generate queries that are concise and target different aspects of the documentation (class reference, method usage, animation examples, conceptual explanations) across both Manim core and relevant plugins.

Here is the error message and the code snippet:

**Error Message:**
{error}

**Code Snippet:**
{code}

Based on the error message and the code snippet, generate multiple human-like queries (maximum 5-7) for retrieving relevant documentation to fix this error. Please ensure that the search targets are different so that the RAG can retrieve a diverse set of documents covering various aspects of the error and its potential solutions.

**Specifically, ensure that:**
1. At least 1-2 queries are focused on retrieving information about Manim *function or class usage* that might be causing the error.
2. If the error message or code suggests the use of plugin functionality, include at least 1 query specifically targeting plugin documentation related to the error.
3. Queries should be specific enough to distinguish between core Manim and plugin functionality when relevant.

Output the queries in the following format:
[
    {{"query": "content of query 1", "type": "manim_core/name_of_the_plugin"}},
    {{"query": "content of query 2", "type": "manim_core/name_of_the_plugin"}},
    {{"query": "content of query 3", "type": "manim_core/name_of_the_plugin"}},
    {{"query": "content of query 4", "type": "manim_core/name_of_the_plugin"}},
    {{"query": "content of query 5", "type": "manim_core/name_of_the_plugin"}},
    {{"query": "content of query 6", "type": "manim_core/name_of_the_plugin"}},
    {{"query": "content of query 7", "type": "manim_core/name_of_the_plugin"}},
] `;

const _promptAnimationSimple = `Given a topic and the context, you need to explain the topic by text.

Also generate a Manim script that visually illustrates a key aspect of {topic} without including explanatory text in the animation itself.
Your text can mention the animation, but it should not be the main focus.
Context about the topic {topic}: {description}.

The animation should focus on:
* Illustrating the significant part of the theorem or concept – Use geometric figures, graphs, number lines, or any relevant visualization.
* Providing an intuitive example – Instead of proving the theorem, show a concrete example or transformation that visually supports understanding.
* Separately, provide a written explanation of the theorem as text that can be displayed outside the animation.

Ensure that:

* The animation is concise.
* The Manim code is compatible with the latest version of community manim.
* The visual elements are clear and enhance understanding.

Please provide the only output as:

1. A text explanation of the theorem.
2. A complete Manim script that generates the animation. Only give the code.

Output format:

(Text Explanation Output)
--- (split by ---)
(Manim Code Output)

Please do not include any other text or headers in your output.
Only use one --- to split the text explanation and the Manim code.`;

const _promptBestPractices = `# Best practices for generating educational videos with manim

1. Specify positions as relative to other objects whenever it makes sense.
   * For example, if you want to place a label for a geometric object.
2. Objects should be of different color from the white background. Use dark colors for visibility.
3. Keep the text on screen concise.
   * On-screen elements should focus on showcasing the concept, examples and visuals. Labels and illustrative text are still encouraged.
   * For explanations and observations, prefer narrations over on-screen text.
   * You should still show calculations and algorithms in full on screen.
   * For examples and practice problems, it is reasonable to show more text, especially key statements.
   * Longer text should appear smaller to fit on screen.
4. To control the timing of objects appearing:
   * \`add\` has instantaneous effect, best used for the initial setup of the scene.
   * Animations are best used during narration.
   * Make sure the animations make sense. If an object is already on screen, it makes no sense to fade it in or create it again.
5. Use TeX or MathTeX whenever you want to display math, including symbols and formulas.
`;

const _promptCodeGeneration = `你是教育内容的专业Manim（社区版）开发者。生成可执行的Manim代码来实现指定的动画，*严格遵循提供的Manim文档上下文、场景技术实现、动画和旁白*。

考虑可重用的动画组件，以创建清洁、模块化和可维护的库，*优先考虑代码结构和最佳实践，如Manim文档上下文中所示*。

**技术规格**：
- 视频比例：16:9
- 坐标系范围：X轴 [-7.1, 7.1]，Y轴 [-4.0, 4.0]
- 画布中心：原点 (0, 0)
- 安全区域：建议重要元素保持在 X轴 [-6.5, 6.5]，Y轴 [-3.5, 3.5] 范围内

**坐标描述要求**：
- 所有图形必须使用精确坐标描述，不允许模糊表述
- 三角形、矩形等多边形必须详细列出每个顶点坐标
- 动画变化必须明确起始坐标和结束坐标
- 示例：三角形顶点 A(0,0), B(2,0), C(1,1.5) → 变化后 A(1,0), B(3,0), C(2,1.5)

**项目背景**：
主题：{topic}
描述：{description}

**完整场景大纲**：
{scene_outline}

**当前场景详情（场景 {scene_number}/3）**：
{scene_content}

**场景技术实现**：
{scene_implementation}


**代码生成指南：**

1.  **场景类：** 类名\`Scene{scene_number}\`，其中\`{scene_number}\`被场景编号替换（例如，\`Scene1\`，\`Scene2\`）。场景类至少应继承自\`VoiceoverScene\`。但是，如果需要，你可以在VoiceoverScene之上添加更多Manim场景类以进行多重继承。
2.  **导入：** 在文件顶部明确包含所有必要的导入，基于使用的Manim类、函数、颜色和常量。不要依赖隐式导入。仔细检查所需的模块、类、函数、颜色和常量，*确保所有导入都有效且与Manim文档一致*。**包含任何使用的Manim插件的导入。**
3.  **语音服务：** 初始化\`KokoroService()\`。你必须这样导入：\`from src.utils.kokoro_voiceover import KokoroService\`，因为这是我们的自定义语音服务。
4.  **可重用动画：** 为每个动画序列实现函数，以创建模块化和可重用的代码。将代码结构化为定义良好的函数，遵循Manim文档中的函数定义模式。
5.  **解说：** 使用\`with self.voiceover(text="...")\`进行语音同步，精确匹配动画和解说计划中的解说脚本和动画时间。
6.  **注释：** 为复杂动画、空间逻辑（定位、排列）和对象生命周期管理添加清晰简洁的注释。*广泛使用注释来解释代码逻辑，特别是空间定位、动画序列和约束执行，反映Manim文档中的注释风格*。**添加注释来解释任何Manim插件的目的和用法。**
7.  **错误处理和约束验证：** 如果Manim文档中建议或示例了错误处理策略，则实现基本错误处理。**重要的是，在代码生成期间，实现明确的检查来验证每个对象的位置和动画是否遵循安全区域边距（0.5单位）和最小间距（0.3单位）。**
8.  **性能：** 遵循Manim文档中推荐的高效代码和渲染性能的最佳实践。
9.  **Manim插件：** 如果已建立的、文档齐全的Manim插件可以简化代码、提高效率或提供核心Manim中不易获得的功能，则允许并鼓励使用它们。
    *   **如果使用插件：**
        *   在文件顶部包含必要的导入语句。
        *   添加注释指示使用的插件及其目的：\`### 插件：<plugin_name> - <简要说明>\`。
        *   确保所有插件使用都遵循插件的文档。
10. **无外部资源：** 无外部文件（图像、音频、视频）。*仅使用Manim内置元素和程序生成，或批准的Manim插件提供的元素。不允许外部资源*。
11. **无主函数：** 仅场景类。无\`if __name__ == "__main__":\`。
12. **空间精度（至关重要）：** 实现技术实现计划中描述的精确空间定位，*严格使用相对定位方法（\`next_to\`，\`align_to\`，\`shift\`，VGroups）并执行安全区域边距和最小0.3单位间距，如Manim文档上下文中所记录*。*空间精度和约束遵循是代码生成中的最高优先级。*
13. **VGroup结构：** 按照技术实现计划中定义的精确实现VGroup层次结构，使用记录的VGroup方法进行对象分组和操作。
14. **间距和边距（严格执行）：**
    - **安全区域**：所有元素必须在X轴[-6.5, 6.5]，Y轴[-3.5, 3.5]范围内
    - **最小间距**：元素间距至少0.5单位，文本行间距0.3单位
    - **边界检查**：每个对象创建后必须验证位置是否在安全区域内
    - **防重叠**：使用\`next_to()\`、\`align_to()\`等相对定位方法
    - **试题布局**：题目区域占用上半部分[-3.5, 1.5]，解答区域占用下半部分[-1.5, 3.5]
15. **背景：** 默认背景（白色）就足够了。不要创建自定义颜色背景矩形。
16. **文本颜色：** 不要对任何文本使用白色。使用深色预定义颜色（BLACK, BLUE, GREEN, RED, PURPLE, GRAY）确保在白色背景上清晰可见。
17. **试题讲解专用要求：**
    - 题目文本使用较大字体（font_size=0.8）并居中显示
    - 解题步骤使用编号列表，每步骤间距0.5单位
    - 关键数据用不同颜色高亮（如：长蜡烛用BLUE，短蜡烛用RED）
    - 图表和示意图必须在安全区域内，距离边界至少1单位
    - 计算过程逐步显示，避免信息过载
17. **默认颜色：** 如果在代码中使用颜色，必须使用提供的颜色定义。仅使用先前定义的颜色。
18. **动画时间和解说同步：** 使用精确的\`run_time\`值实现动画，并根据动画和解说计划与解说脚本同步。使用具有指定持续时间的\`Wait()\`命令进行过渡缓冲。
19. **代码生成不要偷懒：** 生成完整的代码，包括所有辅助函数。确保输出是全面的，代码是完全功能的，包含所有必要的辅助方法和完整的场景实现细节。
20. **LaTeX包处理：** 如果技术实现计划指定需要额外的LaTeX包：
    *   创建一个\`TexTemplate\`对象。
    *   使用\`myTemplate = TexTemplate()\`
    *   使用\`myTemplate.add_to_preamble(r"\\\\usepackage{{package_name}}")\`添加所需的包。
    *   将此模板传递给\`Tex\`或\`MathTex\`对象：\`tex = Tex(..., tex_template=myTemplate)\`。
    *   如果涉及到公式，请使用 \`MathTex\` 或 \`Tex\` 对象，并正确书写LaTeX代码。对于数学表达式，我们应该使用 MathTex 而不是 Tex。对于普通文本标签，我们应该使用 Text 而不是 Tex。MathTex 不接受 font 参数

**要模仿的示例代码风格和结构：**

*   **辅助类：** 利用辅助类（如\`Scene2_Helper\`）来封装对象创建和场景逻辑，促进模块化和可重用性。
*   **基于阶段的\`construct\`方法：** 将\`construct\`方法结构化为逻辑阶段（例如，阶段1，阶段2，阶段3），并使用注释来组织场景流程。
*   **可重用对象创建函数：** 在辅助类中定义可重用函数，用于创建特定的Manim对象（例如，\`create_axes\`，\`create_formula_tex\`，\`create_explanation_text\`）。
*   **清晰的注释和变量名：** 使用清晰简洁的注释来解释代码部分和逻辑。使用描述性变量名（例如，\`linear_function_formula\`，\`logistic_plot\`）以提高可读性。
*   **文本元素：** 使用\`Tex\`或\`MathTex\`创建公式和解释的文本元素，根据需要使用\`color\`和\`font_size\`进行样式设置。
*   **Manim最佳实践：** 遵循Manim最佳实践，包括使用\`VoiceoverScene\`、\`KokoroService\`、常见Manim对象、动画、相对定位和预定义颜色。

您必须按以下格式生成Python代码（从<CODE>到</CODE>）：
<CODE>
\\\`\\\`\\\`python
from manim import *
from manim import config as global_config
from manim_voiceover import VoiceoverScene
from src.utils.kokoro_voiceover import KokoroService # 您必须这样导入，因为这是我们的自定义语音服务。

# 插件导入，不要更改导入语句
from manim_circuit import *
from manim_physics import *
from manim_chemistry import *
from manim_dsa import *
from manim_ml import *

# 辅助函数/类（实现并使用辅助类和函数以提高代码可重用性和组织性）
class Scene{scene_number}_Helper:  # 示例：class Scene1_Helper:
    # 包含场景{scene_number}实用函数的辅助类。
    def __init__(self, scene):
        self.scene = scene
        # ...（添加任何必要的初始化）

    # 可重用对象创建函数（根据计划实现对象创建函数以实现模块化和可重用性）
    def get_center_of_edges(self, polygon, buff=SMALL_BUFF*3):
        # 计算多边形（三角形、正方形等）中每条边的中心点，带有可选缓冲区。
        # 获取多边形的顶点
        vertices = polygon.get_vertices()
        n_vertices = len(vertices)
        # 初始化列表以存储边中心
        coords_vertices = []
        # 计算每条边的中心点和法线
        for i in range(n_vertices):
            # 获取当前和下一个顶点（环绕到第一个顶点）
            v1 = vertices[i]
            v2 = vertices[(i + 1) % n_vertices]
            # 计算边中心
            edge_center = (v1 + v2) / 2
            # 计算边向量并归一化
            edge_vector = v2 - v1
            edge_length = np.linalg.norm(edge_vector)
            normal = np.array([-edge_vector[1], edge_vector[0], 0]) / edge_length
            # 在法线方向添加缓冲区
            coords_vertices.append(edge_center + normal * buff)

        return coords_vertices

    def create_formula_tex(self, formula_str, color):
        # 创建具有指定颜色的MathTex公式的示例函数。
        # 检查是否需要自定义TexTemplate（来自技术计划）。
        if hasattr(self.scene, 'tex_template'):
            formula = MathTex(formula_str, color=color, tex_template=self.scene.tex_template)
        else:
            formula = MathTex(formula_str, color=color)
        return formula

    # ...（根据需要为对象创建和场景逻辑添加更多辅助函数）


class Scene{scene_number}(VoiceoverScene, MovingCameraScene):  # 注意：如果需要，您可以在当前模板之上添加更多Manim场景类以进行多重继承。
    # 提醒：此场景类是完全自包含的。不依赖于前面或后续场景的实现。
    def construct(self):
        # 初始化语音服务
        self.set_speech_service(KokoroService())

        # 实例化辅助类（根据计划）
        helper = Scene{scene_number}_Helper(self)  # 示例：helper = Scene1_Helper(self)

        # 检查LaTeX包并在需要时创建TexTemplate。
        # 此部分应根据技术实现计划生成。
        # 例如，如果计划包括："需要：\\\\usepackage{{amsmath}}"
        # 然后生成：
        #
        # my_template = TexTemplate()
        # my_template.add_to_preamble(r"\\\\usepackage{{amsmath}}")
        # self.tex_template = my_template

        # --- 阶段1：场景设置（根据您的场景调整阶段编号和描述，遵循计划） ---
        with self.voiceover(text="[阶段1的解说 - 来自动画和解说计划]") as tracker:  # 阶段1的解说
            # 使用辅助函数创建对象（根据计划）
            axes = helper.create_axes()  # 示例：axes = helper.create_axes()
            formula = helper.create_formula_tex("...", BLUE_C)  # 示例：formula = helper.create_formula_tex("...", BLUE_C)
            explanation = helper.create_explanation_text("...")  # 示例：explanation = helper.create_explanation_text("...")

            # 定位对象（相对定位，约束验证 - 根据计划）
            formula.to_corner(UL)  # 示例定位
            axes.move_to(ORIGIN)  # 示例定位
            explanation.next_to(axes, RIGHT)  # 示例定位

            # 阶段1的动画（与解说同步 - 根据计划）
            self.play(Write(formula), Write(axes), run_time=tracker.duration)  # 示例动画
            self.wait(0.5)  # 过渡缓冲

        # --- 阶段2：...（以类似的模块化和结构化方式实现阶段2、阶段3等，遵循计划） ---
        with self.voiceover(text="[阶段2的解说 - 来自动画和解说计划]") as tracker:  # 阶段2的解说
            # ...（阶段2的对象创建、定位和动画，使用辅助函数和约束验证）
            pass  # 用实际的阶段2代码替换

        # ...（以类似的模块化和结构化方式实现其余阶段，遵循动画和解说计划以及技术实现计划，并在每个阶段严格验证空间约束）

        self.wait(1)  # 场景结束过渡缓冲
\\\`\\\`\\\`
</CODE>

注释：
\\\`get_center_of_edges\\\`辅助函数特别有用于：
1. 查找多边形边的中点用于标签放置
2. 计算与多边形不重叠的侧标签的偏移位置
3. 在不同多边形大小和方向之间创建一致的标签定位

在您的场景中的示例用法：
\\\`\\\`\\\`python
def label_triangle_sides(self, triangle, labels=["a", "b", "c"]):
    # 标记三角形边的辅助函数。
    edge_centers = self.helper.get_center_of_edges(triangle)
    labeled_sides = VGroup()
    for center, label in zip(edge_centers, labels):
            tex = MathTex(label).move_to(center)
            labeled_sides.add(tex)
        return labeled_sides
\\\`\\\`\\\``;

const _promptContextLearningAnimationNarration = `Here are some example animation and narration plans to help guide your planning:

{examples}

Please follow a similar structure while maintaining creativity and relevance to the current scene.`;

const _promptContextLearningCode = `Here are some example Manim code implementations to help guide your code generation:

{examples}

Please follow similar patterns and best practices while implementing the current scene.`;

const _promptContextLearningScenePlan = `Here are some example scene plans to help guide your scene planning:

{examples}

Please follow a similar structure while maintaining creativity and relevance to the current topic.`;

const _promptContextLearningTechnicalImplementation = `Here are some example technical implementation plans to help guide your implementation:

{examples}

Please follow a similar structure while maintaining creativity and relevance to the current scene.`;

const _promptContextLearningVisionStoryboard = `Here are some example vision and storyboard plans to help guide your planning:

{examples}

Please follow a similar structure while maintaining creativity and relevance to the current scene.`;

const _promptDetectPlugins = `You are a Manim plugin detection system. Your task is to analyze a video topic and description to determine which Manim plugins would be most relevant for the actual animation implementation needs.

Topic:
{topic}

Description:
{description}

Available Plugins:
{plugin_descriptions}

Instructions:
1. Analyze the topic and description, focusing specifically on what needs to be animated
2. Review each plugin's capabilities and determine if they provide specific tools needed for the animations described
3. Only select plugins that provide functionality directly needed for the core animations
4. Consider these criteria for each plugin:
   - Does the plugin provide specific tools or components needed for the main visual elements?
   - Are the plugin's features necessary for implementing the core animations?
   - Would the animation be significantly more difficult to create without this plugin?
5. Exclude plugins that:
   - Only relate to the general topic area but don't provide needed animation tools
   - Might be "nice to have" but aren't essential for the core visualization
   - Could be replaced easily with basic Manim shapes and animations

Your response must follow the output format below:
<THINKING>
[brief description of your thinking process]
</THINKING>
<PLUGINS>
\`\`\`json
["plugin_name1", "plugin_name2"]
\`\`\`
</PLUGINS>`;

const _promptFixError = `You are an expert Manim developer specializing in debugging and error resolution. Based on the provided implementation plan and Manim code, analyze the error message to provide a comprehensive fix and explanation.

Implementation Plan of the Scene:
{implementation_plan}

Manim Code:
\`\`\`python
{manim_code}
\`\`\`

Error Message:
{error_message}

Requirements:
1. Provide complete error analysis with specific line numbers where possible.
2. Include exact instructions for every code change.
3. Explain why the error occurred in plain language.
4. If external assets (e.g., images, audio, video) are referenced, remove them.
5. **If voiceover is present in the original code, ensure it remains preserved in the corrected code.**
6. Preserve all original code that is not causing the reported error. Do not remove or alter any intentional elements unnecessarily.
7. Follow best practices for code clarity and the current Manim version.

You MUST only output the following format (from <THINKING> to </FULL_CORRECTED_CODE>). You MUST NOT come up with any other format like JSON.

<THINKING>
Error Type: [Syntax/Runtime/Logic/Other]
Error Location: [File/Line number/Component]
Root Cause: [Brief explanation of what caused the error]
Impact: [What functionality is affected]
Solution:
[FIXES_REQUIRED]
- Fix 1: [Description]
  - Location: [Where to apply]
  - Change: [What to modify]
- Fix 2: [If applicable]
...
</THINKING>
<FULL_CORRECTED_CODE>
\`\`\`python
# Complete corrected and fully implemented Python code
# Include all necessary imports, definitions, and any additional code for the script to run successfully
\`\`\`
</FULL_CORRECTED_CODE>`;

const _promptManimCheatsheet = `The followings are the inheritance diagram of the Manim library. You can take as reference to select which class to use for the animation.

\`\`\` 
digraph Animation {
    "AddTextLetterByLetter"
    "ShowIncreasingSubsets"
    "ShowIncreasingSubsets" -> "AddTextLetterByLetter"
    "AddTextWordByWord";
    "Succession";
    "Succession" -> "AddTextWordByWord";
    "AnimatedBoundary";
    "VGroup";
    "VGroup" -> "AnimatedBoundary";
    "Animation";
    "AnimationGroup";
    "Animation" -> "AnimationGroup";
    "ApplyComplexFunction";
    "ApplyMethod";
    "ApplyMethod" -> "ApplyComplexFunction";
    "ApplyFunction";
    "Transform";
    "Transform" -> "ApplyFunction";
    "ApplyMatrix";
    "ApplyPointwiseFunction";
    "ApplyPointwiseFunction" -> "ApplyMatrix";
    "ApplyMethod";
    "Transform" -> "ApplyMethod";
    "ApplyPointwiseFunction";
    "ApplyMethod" -> "ApplyPointwiseFunction";
    "ApplyPointwiseFunctionToCenter";
    "ApplyPointwiseFunction" -> "ApplyPointwiseFunctionToCenter";
    "ApplyWave";
    "Homotopy";
    "Homotopy" -> "ApplyWave";
    "Broadcast";
    "LaggedStart";
    "LaggedStart" -> "Broadcast";
    "ChangeDecimalToValue";
    "ChangingDecimal";
    "ChangingDecimal" -> "ChangeDecimalToValue";
    "ChangeSpeed";
    "Animation" -> "ChangeSpeed";
    "ChangingDecimal";
    "Animation" -> "ChangingDecimal";
    "Circumscribe";
    "Succession" -> "Circumscribe";
    "ClockwiseTransform";
    "Transform" -> "ClockwiseTransform";
    "ComplexHomotopy";
    "Homotopy" -> "ComplexHomotopy";
    "CounterclockwiseTransform";
    "Transform" -> "CounterclockwiseTransform";
    "Create";
    "ShowPartial";
    "ShowPartial" -> "Create";
    "CyclicReplace";
    "Transform" -> "CyclicReplace";
    "DrawBorderThenFill";
    "Animation" -> "DrawBorderThenFill";
    "FadeIn";
    "FadeOut";
    "FadeToColor";
    "ApplyMethod" -> "FadeToColor";
    "FadeTransform";
    "Transform" -> "FadeTransform";
    "FadeTransformPieces";
    "FadeTransform" -> "FadeTransformPieces";
    "Flash";
    "AnimationGroup" -> "Flash";
    "FocusOn";
    "Transform" -> "FocusOn";
    "GrowArrow";
    "GrowFromPoint";
    "GrowFromPoint" -> "GrowArrow";
    "GrowFromCenter";
    "GrowFromPoint" -> "GrowFromCenter";
    "GrowFromEdge";
    "GrowFromPoint" -> "GrowFromEdge";
    "GrowFromPoint";
    "Transform" -> "GrowFromPoint";
    "Homotopy";
    "Animation" -> "Homotopy";
    "Indicate";
    "Transform" -> "Indicate";
    "LaggedStart";
    "AnimationGroup" -> "LaggedStart";
    "LaggedStartMap";
    "LaggedStart" -> "LaggedStartMap";
    "MaintainPositionRelativeTo";
    "Animation" -> "MaintainPositionRelativeTo";
    "Mobject";
    "MoveAlongPath";
    "Animation" -> "MoveAlongPath";
    "MoveToTarget";
    "Transform" -> "MoveToTarget";
    "PhaseFlow";
    "Animation" -> "PhaseFlow";
    "RemoveTextLetterByLetter";
    "AddTextLetterByLetter" -> "RemoveTextLetterByLetter";
    "ReplacementTransform";
    "Transform" -> "ReplacementTransform";
    "Restore";
    "ApplyMethod" -> "Restore";
    "Rotate";
    "Transform" -> "Rotate";
    "Rotating";
    "Animation" ->  "Rotating";
    "ScaleInPlace";
    "ApplyMethod" -> "ScaleInPlace";
    "ShowIncreasingSubsets";
    "Animation" -> "ShowIncreasingSubsets";
    "ShowPartial";
    "Animation" -> "ShowPartial";
    "ShowPassingFlash";
    "ShowPartial" -> "ShowPassingFlash";
    "ShowPassingFlashWithThinningStrokeWidth";
    "AnimationGroup" ->  "ShowPassingFlashWithThinningStrokeWidth";
    "ShowSubmobjectsOneByOne";
    "ShowIncreasingSubsets" -> "ShowSubmobjectsOneByOne";
    "ShrinkToCenter";
    "ScaleInPlace" -> "ShrinkToCenter";
    "SmoothedVectorizedHomotopy";
    "Homotopy" -> "SmoothedVectorizedHomotopy";
    "SpinInFromNothing";
    "GrowFromCenter" -> "SpinInFromNothing";
    "SpiralIn";
    "Animation" -> "SpiralIn";
    "Succession";
    "AnimationGroup" -> "Succession";
    "Swap";
    "CyclicReplace" -> "Swap";
    "TracedPath";
    "VMobject";
    "VMobject" -> "TracedPath";
    "Transform";
    "Animation" -> "Transform";
    "TransformAnimations";
    "Transform" -> "TransformAnimations";
    "TransformFromCopy";
    "Transform" -> "TransformFromCopy";
    "TransformMatchingAbstractBase";
    "AnimationGroup" -> "TransformMatchingAbstractBase";
    "TransformMatchingShapes";
    "TransformMatchingAbstractBase" -> "TransformMatchingShapes";
    "TransformMatchingTex";
    "TransformMatchingAbstractBase" ->  "TransformMatchingTex";
    "Uncreate";
    "Create" -> "Uncreate";
    "Unwrite";
    "Write";
    "Write" -> "Unwrite";
    "UpdateFromAlphaFunc";
    "UpdateFromFunc";
    "UpdateFromFunc" -> "UpdateFromAlphaFunc";
    "UpdateFromFunc";
    "Animation" -> "UpdateFromFunc";
    "VGroup";
    "VMobject" ->  "VGroup";
    "VMobject";
    "Mobject" -> "VMobject";

    "Wait";
    "Animation" -> "Wait";
    "Wiggle";
    "Animation" -> "Wiggle";
    "Write";
    "DrawBorderThenFill" ->  "Write";
}
\`\`\`


\`\`\`
digraph Camera {
    "BackgroundColoredVMobjectDisplayer"
    "Camera"
    "MappingCamera"
    "Camera" -> "MappingCamera"
    "MovingCamera"
    "Camera" -> "MovingCamera"
    "MultiCamera"
    "MovingCamera" -> "MultiCamera"
    "OldMultiCamera"
    "Camera" -> "OldMultiCamera"
    "SplitScreenCamera"
    "OldMultiCamera" -> "SplitScreenCamera"
    "ThreeDCamera"
    "Camera" -> "ThreeDCamera"
}
\`\`\`

\`\`\`
digraph MObject {
    "AbstractImageMobject"
    "Mobject" -> "AbstractImageMobject"
    "Angle"
    "VMobject" -> "Angle"
    "AnnotationDot"
    "Dot" -> "AnnotationDot"
    "AnnularSector"
    "Arc" -> "AnnularSector"
    "Annulus"
    "Circle" -> "Annulus"
    "Arc"
    "TipableVMobject" -> "Arc"
    "ArcBetweenPoints"
    "Arc" -> "ArcBetweenPoints"
    "ArcBrace"
    "Brace" -> "ArcBrace"
    "ArcPolygon"
    "VMobject" -> "ArcPolygon"
    "ArcPolygonFromArcs"
    "VMobject" -> "ArcPolygonFromArcs"
    "Arrow"
    "Line" -> "Arrow"
    "Arrow3D"
    "Line3D" -> "Arrow3D"
    "ArrowCircleFilledTip"
    "ArrowCircleTip" -> "ArrowCircleFilledTip"
    "ArrowCircleTip"
    "ArrowTip" -> "ArrowCircleTip"
    "Circle" -> "ArrowCircleTip"
    "ArrowSquareFilledTip"
    "ArrowSquareTip" -> "ArrowSquareFilledTip"
    "ArrowSquareTip"
    "ArrowTip" -> "ArrowSquareTip"
    "Square" -> "ArrowSquareTip"
    "ArrowTip"
    "VMobject" -> "ArrowTip"
    "ArrowTriangleFilledTip"
    "ArrowTriangleTip" -> "ArrowTriangleFilledTip"
    "ArrowTriangleTip"
    "ArrowTip" -> "ArrowTriangleTip"
    "Triangle" -> "ArrowTriangleTip"
    "ArrowVectorField"
    "VectorField" -> "ArrowVectorField"
    "Axes"
    "VGroup" -> "Axes"
    "CoordinateSystem" -> "Axes"
    "BackgroundRectangle"
    "SurroundingRectangle" -> "BackgroundRectangle"
    "BarChart"
    "Axes" -> "BarChart"
    "Brace"
    "svg_mobject.VMobjectFromSVGPath" -> "Brace"
    "BraceBetweenPoints"
    "Brace" -> "BraceBetweenPoints"
    "BraceLabel"
    "VMobject" -> "BraceLabel"
    "BraceText"
    "BraceLabel" -> "BraceText"
    "BulletedList"
    "Tex" -> "BulletedList"
    "Circle"
    "Arc" -> "Circle"
    "Code"
    "VGroup" -> "Code"
    "ComplexPlane"
    "NumberPlane" -> "ComplexPlane"
    "ComplexValueTracker"
    "ValueTracker" -> "ComplexValueTracker"
    "Cone"
    "Surface" -> "Cone"
    "CoordinateSystem"
    "Cross"
    "VGroup" -> "Cross"
    "Cube"
    "VGroup" -> "Cube"
    "CubicBezier"
    "VMobject" -> "CubicBezier"
    "CurvedArrow"
    "ArcBetweenPoints" -> "CurvedArrow"
    "CurvedDoubleArrow"
    "CurvedArrow" -> "CurvedDoubleArrow"
    "CurvesAsSubmobjects"
    "VGroup" -> "CurvesAsSubmobjects"
    "Cutout"
    "VMobject" -> "Cutout"
    "Cylinder"
    "Surface" -> "Cylinder"
    "DashedLine"
    "Line" -> "DashedLine"
    "DashedVMobject"
    "VMobject" -> "DashedVMobject"
    "DecimalMatrix"
    "Matrix" -> "DecimalMatrix"
    "DecimalNumber"
    "VMobject" -> "DecimalNumber"
    "DecimalTable"
    "Table" -> "DecimalTable"
    "DiGraph"
    "GenericGraph" -> "DiGraph"
    "Difference"
    "Dodecahedron"
    "Polyhedron" -> "Dodecahedron"
    "Dot"
    "Circle" -> "Dot"
    "Dot3D"
    "Sphere" -> "Dot3D"
    "DoubleArrow"
    "Arrow" -> "DoubleArrow"
    "Elbow"
    "VMobject" -> "Elbow"
    "Ellipse"
    "Circle" -> "Ellipse"
    "Exclusion"
    "FullScreenRectangle"
    "ScreenRectangle" -> "FullScreenRectangle"
    "FunctionGraph"
    "ParametricFunction" -> "FunctionGraph"
    "Generic"
    "GenericGraph"
    "Generic" -> "GenericGraph"
    "Graph"
    "GenericGraph" -> "Graph"
    "Group"
    "Mobject" -> "Group"
    "Icosahedron"
    "Polyhedron" -> "Icosahedron"
    "ImageMobject"
    "AbstractImageMobject" -> "ImageMobject"
    "ImageMobjectFromCamera"
    "AbstractImageMobject" -> "ImageMobjectFromCamera"
    "ImplicitFunction"
    "VMobject" -> "ImplicitFunction"
    "Integer"
    "DecimalNumber" -> "Integer"
    "IntegerMatrix"
    "Matrix" -> "IntegerMatrix"
    "IntegerTable"
    "Table" -> "IntegerTable"
    "Intersection"
    "LabeledDot"
    "Dot" -> "LabeledDot"
    "LayoutFunction"
    "Protocol" -> "LayoutFunction"
    "Line"
    "TipableVMobject" -> "Line"
    "Line3D"
    "Cylinder" -> "Line3D"
    "LinearBase"
    "LogBase"
    "ManimBanner"
    "VGroup" -> "ManimBanner"
    "MarkupText"
    "svg_mobject.SVGMobject" -> "MarkupText"
    "MathTable"
    "Table" -> "MathTable"
    "MathTex"
    "SingleStringMathTex" -> "MathTex"
    "Matrix"
    "VMobject" -> "Matrix"
    "Mobject"
    "Mobject1D"
    "PMobject" -> "Mobject1D"
    "Mobject2D"
    "PMobject" -> "Mobject2D"
    "MobjectMatrix"
    "Matrix" -> "MobjectMatrix"
    "MobjectTable"
    "Table" -> "MobjectTable"
    "NumberLine"
    "Line" -> "NumberLine"
    "NumberPlane"
    "Axes" -> "NumberPlane"
    "Octahedron"
    "Polyhedron" -> "Octahedron"
    "PGroup"
    "PMobject" -> "PGroup"
    "PMobject"
    "Mobject" -> "PMobject"
    "Paragraph"
    "VGroup" -> "Paragraph"
    "ParametricFunction"
    "VMobject" -> "ParametricFunction"
    "Point"
    "PMobject" -> "Point"
    "PointCloudDot"
    "Mobject1D" -> "PointCloudDot"
    "PolarPlane"
    "Axes" -> "PolarPlane"
    "Polygon"
    "Polygram" -> "Polygon"
    "Polygram"
    "VMobject" -> "Polygram"
    "Polyhedron"
    "VGroup" -> "Polyhedron"
    "Prism"
    "Cube" -> "Prism"
    "Protocol"
    "Generic" -> "Protocol"
    "Rectangle"
    "Polygon" -> "Rectangle"
    "RegularPolygon"
    "RegularPolygram" -> "RegularPolygon"
    "RegularPolygram"
    "Polygram" -> "RegularPolygram"
    "RightAngle"
    "Angle" -> "RightAngle"
    "RoundedRectangle"
    "Rectangle" -> "RoundedRectangle"
    "SVGMobject"
    "VMobject" -> "SVGMobject"
    "SampleSpace"
    "Rectangle" -> "SampleSpace"
    "ScreenRectangle"
    "Rectangle" -> "ScreenRectangle"
    "Sector"
    "AnnularSector" -> "Sector"
    "SingleStringMathTex"
    "svg_mobject.SVGMobject" -> "SingleStringMathTex"
    "Sphere"
    "Surface" -> "Sphere"
    "Square"
    "Rectangle" -> "Square"
    "Star"
    "Polygon" -> "Star"
    "StealthTip"
    "ArrowTip" -> "StealthTip"
    "StreamLines"
    "VectorField" -> "StreamLines"
    "Surface"
    "VGroup" -> "Surface"
    "SurroundingRectangle"
    "RoundedRectangle" -> "SurroundingRectangle"
    "Table"
    "VGroup" -> "Table"
    "TangentLine"
    "Line" -> "TangentLine"
    "Tetrahedron"
    "Polyhedron" -> "Tetrahedron"
    "Tex"
    "MathTex" -> "Tex"
    "Text"
    "svg_mobject.SVGMobject" -> "Text"
    "ThreeDAxes"
    "Axes" -> "ThreeDAxes"
    "ThreeDVMobject"
    "VMobject" -> "ThreeDVMobject"
    "TipableVMobject"
    "VMobject" -> "TipableVMobject"
    "Title"
    "Tex" -> "Title"
    "Torus"
    "Surface" -> "Torus"
    "Triangle"
    "RegularPolygon" -> "Triangle"
    "Underline"
    "Line" -> "Underline"
    "Union"
    "UnitInterval"
    "NumberLine" -> "UnitInterval"
    "VDict"
    "VMobject" -> "VDict"
    "VGroup"
    "VMobject" -> "VGroup"
    "VMobject"
    "Mobject" -> "VMobject"
    "VMobjectFromSVGPath"
    "VMobject" -> "VMobjectFromSVGPath"
    "ValueTracker"
    "Mobject" -> "ValueTracker"
    "Variable"
    "VMobject" -> "Variable"
    "Vector"
    "Arrow" -> "Vector"
    "VectorField"
    "VGroup" -> "VectorField"
    "VectorizedPoint"
    "VMobject" -> "VectorizedPoint"
}
\`\`\`

\`\`\`
digraph Scene {
    "LinearTransformationScene"
    "VectorScene"
    "VectorScene" -> "LinearTransformationScene"
    "MovingCameraScene"
    "Scene"
    "Scene" -> "MovingCameraScene"
    "RerunSceneHandler"
    "Scene"
    "SceneFileWriter"
    "SpecialThreeDScene"
    "ThreeDScene"
    "ThreeDScene" -> "SpecialThreeDScene"
    "ThreeDScene"
    "Scene" -> "ThreeDScene"
    "VectorScene"
    "Scene" -> "VectorScene"
    "ZoomedScene"
    "MovingCameraScene" -> "ZoomedScene"
}
\`\`\``;

const _promptRagQueryGenerationCode = `You are an expert in generating search queries specifically for **Manim (Community Edition) documentation** (both core Manim and its plugins). Your task is to transform a complete implementation plan for a Manim video scene into effective queries that will retrieve relevant information from Manim documentation. The implementation plan describes the scene's vision, storyboard, technical implementation, and animation/narration strategy.

Here is the complete scene implementation plan:

{implementation_plan}

Based on the complete implementation plan, generate multiple human-like queries (maximum 10) for retrieving relevant documentation. Please ensure that the search targets are different so that the RAG can retrieve a diverse set of documents covering various aspects of the implementation.

**Specifically, ensure that:**
1.  At least some queries are focused on retrieving information about **Manim function usage** in scenes. Frame these queries to target function definitions, usage examples, and parameter details within Manim documentation.
2.  If the implementation suggests using plugin functionality, include at least 1 query specifically targeting **plugin documentation**.  Clearly mention the plugin name in these queries to focus the search.
3.  Queries should be specific enough to distinguish between core Manim and plugin functionality when relevant, and to target the most helpful sections of the documentation (API reference, tutorials, examples).

The above implementation plans are relevant to these plugins: {relevant_plugins}.
Note that you MUST NOT use the plugins that are not listed above.

You MUST only output the queries in the following JSON format (with json triple backticks):
\`\`\`json
[
    {{"type": "manim-core", "query": "content of function usage query"}},
    {{"type": "<plugin-name>", "query": "content of plugin-specific query"}},
    {{"type": "manim-core", "query": "content of API reference query"}}
    ...
]
\`\`\``;

const _promptRagQueryGenerationFixError = `You are an expert in generating search queries specifically for **Manim (Community Edition) documentation** (both core Manim and its plugins). Your task is to transform a Manim error and its associated code into effective queries that will retrieve relevant information from Manim documentation.

Here is the error message:
{error}

Here is the Manim code that caused the error:
{code}

Based on the error and code, generate multiple human-like queries (maximum 10) for retrieving relevant documentation. Please ensure that the search targets are different so that the RAG can retrieve a diverse set of documents covering various aspects of the implementation.

**Specifically, ensure that:**
1.  At least some queries are focused on retrieving information about **Manim function usage** in scenes. Frame these queries to target function definitions, usage examples, and parameter details within Manim documentation.
2.  If the error suggests using plugin functionality, include at least 1 query specifically targeting **plugin documentation**.  Clearly mention the plugin name in these queries to focus the search.
3.  Queries should be specific enough to distinguish between core Manim and plugin functionality when relevant, and to target the most helpful sections of the documentation (API reference, tutorials, examples).

The above error and code are relevant to these plugins: {relevant_plugins}.
Note that you MUST NOT use the plugins that are not listed above.

You MUST only output the queries in the following JSON format (with json triple backticks):
\`\`\`json
[
    {{"type": "manim-core", "query": "content of function usage query"}},
    {{"type": "<plugin-name>", "query": "content of plugin-specific query"}},
    {{"type": "manim-core", "query": "content of API reference query"}}
    ...
]
\`\`\` `;

const _promptRagQueryGenerationNarration = `You are an expert in generating search queries specifically for **Manim (Community Edition) documentation** (both core Manim and its plugins). Your task is to analyze a storyboard and generate effective queries that will retrieve relevant documentation about narration, text animations, and audio-visual synchronization.

Here is the storyboard:

{storyboard}

Based on this storyboard, generate multiple human-like queries (maximum 10) for retrieving relevant documentation about narration and text animation techniques.

**Specifically, ensure that:**
1. Queries focus on retrieving information about **text animations** and their properties
2. Include queries about **timing and synchronization** techniques
3. If the storyboard suggests using plugin functionality, include specific queries targeting those plugin's narration capabilities

The above storyboard is relevant to these plugins: {relevant_plugins}.
Note that you MUST NOT use the plugins that are not listed above.

You MUST only output the queries in the following JSON format (with json triple backticks):
\`\`\`json
[
    {{"type": "manim-core", "query": "content of text animation query"}},
    {{"type": "<plugin-name>", "query": "content of plugin-specific query"}},
    {{"type": "manim-core", "query": "content of timing synchronization query"}}
    ...
]
\`\`\``;

const _promptRagQueryGenerationStoryboard = `You are an expert in generating search queries specifically for **Manim (Community Edition) documentation** (both core Manim and its plugins). Your task is to transform a storyboard plan for a Manim video scene into effective queries that will retrieve relevant information from Manim documentation. The storyboard plan describes the scene's visual elements and narrative flow.

Here is the storyboard plan:

{storyboard}

Based on the storyboard plan, generate multiple human-like queries (maximum 10) for retrieving relevant documentation. Please ensure that the search targets are different so that the RAG can retrieve a diverse set of documents covering various aspects of the implementation.

**Specifically, ensure that:**
1.  At least some queries are focused on retrieving information about **Manim core functionalities**, like general visual elements or animations. Frame these queries using Manim terminology (classes, methods, concepts).
2.  If the storyboard suggests using specific visual effects or complex animations that might be plugin-related, include at least 1 query specifically targeting **plugin documentation**.  Make sure to mention the plugin name if known or suspected.
3.  Queries should be general enough to explore different possibilities within Manim and its plugins based on the storyboard's visual and narrative descriptions, but also specific enough to target Manim documentation effectively.

The above storyboard might be relevant to these plugins: {relevant_plugins}.
Note that you MUST NOT use the plugins that are not listed above.

Output the queries in the following format:
\`\`\`json
[
    {{"query": "content of query 1", "type": "manim_core/{relevant_plugins}"}},
    {{"query": "content of query 2", "type": "manim_core/{relevant_plugins}"}},
    {{"query": "content of query 3", "type": "manim_core/{relevant_plugins}"}},
    {{"query": "content of query 4", "type": "manim_core/{relevant_plugins}"}},
    {{"query": "content of query 5", "type": "manim_core/{relevant_plugins}"}},
    {{"query": "content of query 6", "type": "manim_core/{relevant_plugins}"}},
    {{"query": "content of query 7", "type": "manim_core/{relevant_plugins}"}},
]
\`\`\` `;

const _promptRagQueryGenerationTechnical = `You are an expert in generating search queries specifically for **Manim (Community Edition) documentation** (both core Manim and its plugins). Your task is to analyze a storyboard plan and generate effective queries that will retrieve relevant technical documentation about implementation details.

Here is the storyboard plan:

{storyboard}

Based on this storyboard plan, generate multiple human-like queries (maximum 10) for retrieving relevant technical documentation.

**Specifically, ensure that:**
1. Queries focus on retrieving information about **core Manim functionality** and implementation details
2. Include queries about **complex animations and effects** described in the storyboard
3. If the storyboard suggests using plugin functionality, include specific queries targeting those plugin's technical documentation

The above storyboard plan is relevant to these plugins: {relevant_plugins}
Note that you MUST NOT use the plugins that are not listed above.

You MUST only output the queries in the following JSON format (with json triple backticks):
\`\`\`json
[
    {{"type": "manim-core", "query": "content of core functionality query"}},
    {{"type": "<plugin-name>", "query": "content of plugin-specific query"}},
    {{"type": "manim-core", "query": "content of animation technique query"}}
    ...
]
\`\`\` `;

const _promptRagQueryGenerationVisionStoryboard = `You are an expert in generating search queries specifically for **Manim (Community Edition) documentation** (both core Manim and its plugins). Your task is to analyze a scene plan for a Manim animation and generate effective queries that will retrieve relevant documentation about visual elements and scene composition.

Here is the scene plan:

{scene_plan}

Based on this scene plan, generate multiple human-like queries (maximum 10) for retrieving relevant documentation about visual elements and scene composition techniques.

**Specifically, ensure that:**
1. Queries focus on retrieving information about **visual elements** like shapes, objects, and their properties
2. Include queries about **scene composition techniques** like layout, positioning, and grouping
3. If the scene plan suggests using plugin functionality, include specific queries targeting those plugin's visual capabilities
4. Queries should be high-level, aiming to discover what Manim features can be used, rather than focusing on low-level implementation details.
    - For example, instead of "how to set the color of a circle", ask "what visual properties of shapes can I control in Manim?".

The above scene plan is relevant to these plugins: {relevant_plugins}.
Note that you MUST NOT use the plugins that are not listed above.

You MUST only output the queries in the following JSON format (with json triple backticks):
\`\`\`json
[
    {{"type": "manim-core", "query": "content of visual element query"}},
    {{"type": "<plugin-name>", "query": "content of plugin-specific query"}},
    {{"type": "manim-core", "query": "content of composition technique query"}}
    ...
]
\`\`\``;

// const _promptSceneAnimationNarration = `You are an expert in educational video production and Manim animation, skilled in creating engaging and pedagogically effective learning experiences.
// **Reminder:** This animation and narration plan is entirely self-contained; there is no dependency on any previous or subsequent scene implementations. However, the narration should flow smoothly as part of a larger, single video.

// Your task is to create a **detailed animation and narration plan for Scene {scene_number}**, ensuring it is not just visually appealing but also serves a clear educational purpose within the overall video topic.

// Remember, the narration should not simply describe what's happening visually, but rather **teach a concept step-by-step**, guiding the viewer to a deeper understanding.  Animations should be spatially coherent, contribute to a clear visual flow, and strictly respect safe area margins (0.5 units) and minimum spacing (0.3 units).  **Consider the scene number {scene_number} and the overall scene context to ensure smooth transitions and a logical flow within the larger video narrative.**

// Topic: {topic}
// Description: {description}

// Scene Overview:
// {scene_outline}

// Scene Vision and Storyboard:
// {scene_vision_storyboard}

// Technical Implementation Plan:
// {technical_implementation_plan}

// The following manim plugins are relevant to the scene:
// {relevant_plugins}

// **Spatial Constraints (Strictly Enforced Throughout Animations):**
// *   **Safe area margins:** 0.5 units. *Maintain objects and VGroups within margins.*
// *   **Minimum spacing:** 0.3 units. *Ensure minimum spacing between all objects and VGroups.*

// **Animation Timing and Pacing Requirements:**
// *   Specify \`run_time\` for all animations.
// *   Use \`Wait()\` for transition buffers, specifying durations and **pedagogical purpose**.
// *   Coordinate animation timings with narration cues for synchronized pedagogical presentation.

// **Visual Flow and Pedagogical Clarity:**
// *   Ensure animations create a clear and logical visual flow, **optimized for learning and concept understanding.**
// *   Use animation pacing and transition buffers to visually separate ideas and **enhance pedagogical clarity.**
// *   Maintain spatial coherence for predictable and understandable animations, strictly adhering to spatial constraints.

// **Diagrams/Sketches (Optional but Highly Recommended for Complex Scenes):**
// *   For complex animations, include diagrams/sketches to visualize animation flow and object movements. This aids clarity and reduces errors.

// Your plan must demonstrate a strong understanding of pedagogical narration and how animations can be used to effectively teach concepts, while strictly adhering to spatial constraints and timing requirements.

// You MUST generate a **detailed and comprehensive** animation and narration plan for **Scene {scene_number}**, in the following format, similar to the example provided (from \`\`\`xml to </SCENE_ANIMATION_NARRATION_PLAN>\`\`\`):

// \`\`\`xml
// <SCENE_ANIMATION_NARRATION_PLAN>

// [ANIMATION_STRATEGY]
// 1. **Pedagogical Animation Plan:** Provide a detailed plan for all animations in the scene, explicitly focusing on how each animation contributes to **teaching the core concepts** of this scene.
//     - **Parent VGroup transitions (if applicable):**
//         - If VGroups are used, specify transitions (\`Shift\`, \`Transform\`, \`FadeIn\`, \`FadeOut\`) with \`Animation\` type, direction, magnitude, target VGroup, and \`run_time\`.
//         - **Explain the pedagogical rationale** for each VGroup transition. How does it guide the viewer's attention or contribute to understanding the scene's learning objectives? Ensure spatial coherence and respect for constraints.
//     - **Element animations within VGroups and for individual Mobjects:**
//         - Specify animation types (\`Create\`, \`Write\`, \`FadeIn\`, \`Transform\`, \`Circumscribe\`, \`AnimationGroup\`, \`Succession\`) for elements.
//         - For each element animation, specify \`Animation\` type, target object(s), and \`run_time\`. Detail sequences and timing for \`AnimationGroup\` or \`Succession\`.
//         - **Explain the pedagogical purpose** of each element animation. How does it break down complex information, highlight key details, or improve visual clarity for learning? Ensure spatial coherence and minimum spacing.
//         - **Coordinate element animations with VGroup transitions:**
//             - Clearly describe the synchronization between element animations and VGroup transitions (if any).
//             - Specify relative timing and \`run_time\` to illustrate coordination.
//             - **Explain how this animation sequence and coordination creates a pedagogical flow**, guiding the viewer's eye and attention logically through the learning material.

// 2. **Scene Flow - Pedagogical Pacing and Clarity:** Detail the overall flow of the scene, emphasizing pedagogical effectiveness.
//     - **Overall animation sequence, spatial progression for learning:**
//         - Describe the complete animation sequence, broken down into pedagogical sub-sections (e.g., "Introducing the Problem", "Step-by-step Solution", "Concept Reinforcement").
//         - Outline the spatial progression of objects and VGroups, focusing on how it supports the **pedagogical narrative** and concept development.
//         - Ensure a clear and logical visual flow optimized for learning, respecting spatial constraints.
//     - **Transition buffers for pedagogical pauses:**
//         - Specify \`Wait()\` times between animation sections for visual separation and **learner processing time**.
//         - For each \`Wait()\`, specify duration and **explain the pedagogical reason** for this buffer (e.g., "Allow viewers time to process the formula", "Create a pause for reflection before moving to the next concept").
//     - **Coordinate animation timing with narration for engagement and comprehension:**
//         - Describe how animation timings are coordinated with the narration script to **maximize viewer engagement and comprehension**.
//         - Specify animation cues within the narration script and explain how these cues are synchronized with animations to **reinforce learning points** at the optimal moment.

// [NARRATION]
// - **Pedagogical Narration Script:**
//     - Provide the full narration script for Scene {scene_number}.
//     - **Embed precise animation timing cues** within the narration script (as described before).
//     - **The script should be written as if delivered by a knowledgeable and engaging lecturer.** It should:
//         - **Clearly explain concepts step-by-step.**
//         - **Use analogies and real-world examples to enhance understanding.**
//         - **Pose questions to encourage active thinking.**
//         - **Summarize key points and transitions.**
//         - **Be detailed and knowledge-rich, not just visually descriptive.**
//         - **Connect smoothly with the previous and subsequent scenes, acting as a segment within a single, cohesive video.
//         - Avoid repetitive introductions or conclusions.**
//         - Consider using phrases like "Building on what we saw in the previous part..." or "Let's now move on to..." to create a sense of continuity.
//         - Reference the scene number when appropriate (e.g., "Now, let's explore...").
//     - **Crucially, the narration should seamlessly integrate with the animations to create a cohesive and effective learning experience.**
// - **Narration Sync - Pedagogical Alignment:**
//     - Detail the synchronization strategy between narration and animations, emphasizing **pedagogical alignment**.
//     - Explain how narration timing is aligned with animation start/end times to **guide viewer attention to key learning elements precisely when they animate.**
//     - Emphasize how narration cues and animation timings work together to **create a synchronized audiovisual presentation that maximizes learning and retention.**

// </SCENE_ANIMATION_NARRATION_PLAN>
// \`\`\`
// `;
const _promptSceneAnimationNarration = `作为一位资深的Manim动画制作人和旁白撰稿人，请根据以下信息，为一部关于“{topic}”的动画生成具体的Manim动画效果建议和完整的旁白文案。

**描述前提**：
我要生成的视频比例是16/9的。
坐标系范围（默认16:9画布）：
X轴范围：[-7.1, 7.1]（宽度14.2单位）
Y轴范围：[-4.0, 4.0]（高度8单位）
视频中心点为原点(0, 0)。
图形元素要使用坐标的方式描述位置、变化后的大小、位置等，例如我描述一个三角形，顶点A（0,0）,顶点B（1.2， 0），顶点C（0，1.2），变化到....
涉及到三角形、矩形等常见图形时，不要概括描述，一定要详细描述每个图形的每个顶点坐标，包括新生成的图形也要详细描述，不能概括。
**原始用户请求**：制作关于“{topic}”的动画。
 
**项目背景**：
主题：{topic}
描述：{description}

**可用插件**：{relevant_plugins}

**整体脚本大纲**：
{scene_outline}
 
**详细场景描述**：
**场景{scene_number}：**
{scene_content}

**场景{scene_number}故事板**
{scene_vision_storyboard}
**任务**：
你的任务是为** 场景 {scene_number}**创建详细的动画和旁白，确保它不仅在视觉上有吸引力，而且在整个视频主题中服务于明确的教育目的。
记住，旁白不应该简单地从视觉上描述发生了什么，而是**一步一步地教授一个概念**，引导观众更深层次地理解。
动画应该在空间上是连贯的，有助于清晰的视觉流。
**考虑场景编号{scene_number}和整个场景上下文，以确保在更大的视频叙事中平滑过渡和逻辑流。**

**输出格式**：
 
<SCENE_ANIMATION_NARRATION_PLAN>
[ANIMATION_STRATEGY]
1. 1. **教学动画计划**：为场景中的所有动画提供详细的计划，明确关注每个动画如何帮助**教授该场景的核心概念**。
    - **父级VGroup过渡效果（如果适用）：**
        - 如果使用了VGroup，请指定过渡效果（\`Shift\`（位移）、\`Transform\`（变换）、\`FadeIn\`（淡入）、\`FadeOut\`（淡出）），包括\`Animation\`动画类型、方向、幅度、目标VGroup和\`run_time\`运行时间。
        - **解释每个VGroup过渡效果的教学理念**。它如何引导观看者的注意力或有助于理解场景的学习目标？确保空间连贯性并遵守约束条件。
    - **VGroup内部元素动画和单个Mobject对象的动画：**
        - 为元素指定动画类型（\`Create\`（创建）、\`Write\`（书写）、\`FadeIn\`（淡入）、\`Transform\`（变换）、\`Circumscribe\`（环绕）、\`AnimationGroup\`（动画组）、\`Succession\`（连续动画））。
        - 对于每个元素动画，指定\`Animation\`动画类型、目标对象和\`run_time\`运行时间。详细说明\`AnimationGroup\`或\`Succession\`的序列和时间安排。
        - **解释每个元素动画的教学目的**。它如何分解复杂信息、突出关键细节，或提高学习的视觉清晰度？确保空间连贯性和最小间距。
        - **协调元素动画与VGroup过渡效果：**
            - 清楚描述元素动画与VGroup过渡效果之间的同步关系（如果有的话）。
            - 指定相对时间和\`run_time\`运行时间以说明协调性。
            - **解释这种动画序列和协调如何创建教学流程**，逻辑性地引导观看者的视线和注意力贯穿学习材料。
2. **场景流程 - 教学节奏和清晰度：** 详细说明场景的整体流程，强调教学效果。
    - **整体动画序列，学习的空间推进：**
        - 描述完整的动画序列，分解为教学子部分（例如："问题介绍"、"逐步解决方案"、"概念强化"）。
        - 概述对象和VGroup的空间推进，重点关注它如何支持**教学叙事**和概念发展。
        - 确保清晰且合乎逻辑的视觉流程，针对学习进行优化，遵守空间约束。
    - **教学暂停的过渡缓冲：**
        - 指定动画部分之间的\`Wait()\`等待时间，用于视觉分离和**学习者处理时间**。
        - 对于每个\`Wait()\`，指定持续时间并**解释教学原因**（例如："让观看者有时间处理公式"、"在进入下一个概念前创建反思暂停"）。
    - **协调动画时间与解说以提高参与度和理解力：**
        - 描述动画时间如何与解说脚本协调，以**最大化观看者参与度和理解力**。
        - 在解说脚本中指定动画提示，并解释这些提示如何与动画同步，以在最佳时刻**强化学习要点**。
[ANIMATION_STRATEGY]
[NARRATION]
- **教学解说脚本：**
    - 提供场景{scene_number}的完整解说脚本。
    - **在解说脚本中嵌入精确的动画时间提示**（如前所述）。
    - **脚本应该像一位知识渊博且富有吸引力的讲师在讲授一样。** 它应该：
        - **逐步清晰地解释概念。**
        - **使用类比和现实世界的例子来增强理解。**
        - **提出问题以鼓励主动思考。**
        - **总结关键要点和过渡。**
        - **内容详细且知识丰富，而不仅仅是视觉描述。**
        - **与前后场景顺畅连接，作为单一连贯视频中的一个片段。
        - 避免重复的开头或结尾。**
        - 考虑使用诸如"基于我们在前面部分看到的..."或"现在让我们继续..."等短语来创造连续感。
        - 在适当时引用场景编号（例如："现在，让我们探索..."）。
    - **关键是，解说应该与动画无缝集成，创造连贯且有效的学习体验。**
- **解说同步 - 教学对齐：**
    - 详述解说与动画之间的同步策略，强调**教学对齐**。
    - 解释解说时间如何与动画开始/结束时间对齐，以**在关键学习元素进行动画时精确引导观看者注意力。**
    - 强调解说提示和动画时间如何共同作用，**创造同步的视听呈现，最大化学习和记忆效果。**
[NARRATION]
</SCENE_ANIMATION_NARRATION_PLAN>`;
const _promptSceneImplementation = `You are an expert in educational video production and Manim (Community Edition) animation development. Your task is to create a detailed implementation plan for Scene {scene_number}.

<BASE_INFORMATION>
Topic: {topic}
Description: {description}
</BASE_INFORMATION>

<SCENE_CONTEXT>
Scene Overview:
{scene_outline}
</SCENE_CONTEXT>

<IMPLEMENTATION_PLAN>

[SCENE_VISION]
1.  **Overall Narrative**:
    - Describe the overall story or message of the scene. What is the key takeaway for the viewer?
    - How does this scene fit into the larger narrative of the video?
    - What is the desired emotional impact on the viewer?

2.  **Learning Objectives**:
    - What specific knowledge or skills should the viewer gain from this scene?
    - How will the visual elements and animations support these learning objectives?
    - What are the key concepts that need to be emphasized?

[STORYBOARD]
1.  **Visual Flow**:
    - Describe the sequence of visual elements and animations in the scene.
    - Provide a rough sketch or description of the key visual moments.
    - How will the scene transition between different ideas or concepts?
    - What is the pacing of the scene? Are there moments of pause or rapid action?

[TECHNICAL_IMPLEMENTATION]
1.  **High-Level Components (VGroups)**:
    - **Identify the main conceptual sections of the scene.** Think of this like outlining chapters in a story or sections in a presentation.
    - **Define the purpose of each high-level component.** What should the viewer learn or understand from each section?
    - **Describe how these components relate to each other and the overall scene flow.** How will you transition between these sections to create a cohesive narrative?
    - **Provide a brief rationale for your choice of high-level components.** Why did you choose these specific sections?

2.  **VGroup Hierarchy**:
    - **For each high-level component, define a parent VGroup.** This VGroup will act as a container for all elements within that section.
    - **Break down each parent VGroup into nested VGroups for sub-components as needed.** Think about logical groupings of elements.
    - **Specify the relative positioning of these VGroups within the scene using \`next_to()\`, \`align_to()\`, and \`shift()\` where possible.** How will the parent VGroups be arranged on the screen relative to each other? (e.g., stacked vertically, side-by-side, etc.) Prioritize relative positioning using the following references:
        - \`ORIGIN\`: the center of the scene
        - scene margins (e.g., corners, edges)
        - other VGroups as references.
        - **MUST NOT use absolute coordinates.**
    - **Define the scale relationships between different levels of the VGroup hierarchy.** Will sub-VGroups inherit scale from parent VGroups? How will scaling be managed to maintain visual consistency?
    - **Provide a brief rationale for your VGroup hierarchy.** Why did you choose this specific structure?

    For each VGroup level (from high-level down to sub-components):
    - Name: [Descriptive name for the VGroup, e.g., "TitleSection", "ProblemStatementGroup", "Explanation1Group"]
    - Purpose: [What is the purpose of this VGroup? What should the viewer learn or understand from this VGroup?]
    - Contents: [List all child VGroups and individual elements (Text, MathTex, Shapes, etc.) that belong to this VGroup.]
    - Positioning:
        * Reference: [Specify what this VGroup is positioned relative to. Do not use absolute coordinates.]
        * Alignment: [How is it aligned relative to the reference? Use \`align_to()\` with options like \`UP\`, \`DOWN\`, \`LEFT\`, \`RIGHT\`, \`ORIGIN\`, etc.]
        * Spacing: [Describe any spacing considerations relative to sibling VGroups or elements within the parent. Use \`buff\` argument in \`next_to()\` or \`arrange()\`. Refer to the defined minimum spacing value.]
    - Scale: [Specify the scale of this VGroup relative to its parent VGroup. Use relative scaling factors (e.g., 1.0 for same scale, 0.8 for smaller).]
    - Rationale: [Explain the reasoning behind the structure and organization of this VGroup. Why did you group these elements together?]

3.  **Element Specification**:
    For each individual element (Text, MathTex, Shapes, etc.) within a VGroup:
    - Name: [Descriptive name for the element, e.g., "ProblemTitleText", "Equation1", "HighlightCircle"]
    - Type: [Manim object type. Examples: Text, MathTex, Circle, Rectangle, Arrow, Line, etc.]
    - Parent VGroup: [Specify the VGroup this element belongs to. This establishes the hierarchical relationship.]
    - Positioning:
        * Reference: [Specify what this element is positioned relative to. Use its parent VGroup, other elements, \`ORIGIN\`, or scene margins as references. Do not use absolute coordinates.]
        * Alignment: [How is it aligned within its parent VGroup? Use \`align_to()\` or \`next_to()\` with appropriate directions, e.g. \`UP\`, \`DOWN\`, \`LEFT\`, \`RIGHT\`, \`ORIGIN\`, \`UL\`, \`UR\`, \`DL\`, \`DR\`]
        * Spacing: [If applicable, describe spacing relative to other elements using \`buff\` in \`next_to()\`. Refer to the defined minimum spacing value.]
    - Style Properties:
        * Color: [Hex code or named color (e.g., "RED", "BLUE"). Use hex codes for specific colors. e.g., #FF0000 for red]
        * Opacity: [Value between 0 and 1. 1 for fully opaque, 0 for fully transparent.]
        * Stroke Width: [Specify stroke width using levels: \`thin\`, \`medium\`, or \`thick\`.]
        * Font: [Font family name, if applicable.]
        * Font Size: [Specify font size using levels: \`heading1\`, \`heading2\`, \`heading3\`, \`heading4\`, \`heading5\`, \`heading6\`, or \`body\`. Refer to the defined font size levels.]
        * Fill Color: [Hex code for fill color, if applicable.]
        * ... [Include any other relevant style properties]
    - Z-Index: [Integer value for layering order within the VGroup. Higher values are on top.]
    - Required Imports: [List specific Manim classes that need to be imported to create this element. e.g., \`from manim import Text, Circle\`]

[ANIMATION_STRATEGY]
1.  **VGroup Transitions**:
    - **Define how parent VGroups will transition onto and off of the scene, and between different sections.** Describe the movement patterns for these high-level groups. Examples: 'Slide in from left', 'Fade in and scale up', 'Move to top of screen'.
    - **Specify the timing and coordination of VGroup transitions.** How long will each transition take? Will transitions overlap or be sequential?
    - **Describe any transformation sequences applied to VGroups during transitions.** Will VGroups rotate, scale, or change shape during transitions?

2.  **Element Animations**:
    - **Define the animations for individual elements within each VGroup.** What animations will bring each element to life? Examples: 'Write in text', 'Draw a circle', 'Highlight an equation', 'Fade in an image'.
    - **Group related element animations using Manim's animation grouping features (e.g., \`AnimationGroup\`, \`Succession\`).** Explain how these groups will be used to create cohesive animation sequences.
    - **Coordinate element animations with parent VGroup movements and transitions.** Ensure element animations are synchronized with the overall scene flow.
    - **Specify the timing of element animations relative to VGroup transitions and other element animations.** Create a timeline or sequence of animations.

3.  **Scene Flow**:
    - **Describe the overall animation sequence for the entire scene.** Outline the order in which VGroups and elements will be animated.
    - **Specify transition buffers or pauses between major sections of the scene.** How much time will be left between animations for the viewer to process information?
    - **Consider how the animation timing will coordinate with the narration (if narration is planned).** Animations should complement and reinforce the spoken content.

[NARRATION]
- **Narration Script:** [Provide the full script for the narration, including timing cues or markers for when specific animations should occur. The script should be clear, detailed, and engaging, and should align with the visual elements and animations.]
- **Narration Sync:** [Describe how the narration should be synchronized with the animations. Specify how timing cues in the narration script will be used to trigger animations. Are there specific points where the narration and animations should be perfectly synchronized? Explain how you will achieve this synchronization.]

[VIEWER_EXPERIENCE]
1.  **Cognitive Load**:
    - How will you manage the amount of information presented at any given time?
    - Are there any complex concepts that need to be broken down into smaller steps?
    - How will you use visual cues to guide the viewer's attention?

2.  **Pacing**:
    - Is the pacing of the scene appropriate for the content?
    - Are there moments where the viewer needs time to pause and reflect?
    - How will you use animation timing to control the pace of the scene?

3.  **Accessibility**:
    - How will you ensure that the scene is accessible to viewers with different needs?
    - Are there any specific considerations for color contrast or text readability?

[TECHNICAL_CHECKS]
- **VGroup boundary validation:** Ensure all elements are contained within their intended VGroup boundaries and are not overflowing unexpectedly.
- **Hierarchy scale consistency:** Verify that scaling is applied consistently throughout the VGroup hierarchy and that text and elements remain readable at all scales.
- **Animation coordination between levels:** Check that animations at different VGroup levels are coordinated and do not clash or look disjointed.
- **Performance optimization for nested groups:** Consider the performance implications of deeply nested VGroups and optimize structure and animations for smooth playback.
- **Text readability:** Ensure all text elements are legible in terms of size, color contrast, and positioning.
- **Color contrast:** Verify sufficient color contrast between text and background, and between different visual elements for accessibility.
- **Animation smoothness:** Check for any jerky or abrupt animations and refine timing and easing for smoother transitions.

</IMPLEMENTATION_PLAN>

Requirements:
1. All elements must stay within safe area margins
2. Maintain minimum spacing between objects: [value]  (This value is defined in the project settings)
3. Use relative positioning when possible, leveraging \`next_to()\`, \`align_to()\`, and \`shift()\`. Only reference positions relative to \`ORIGIN\`, scene margins, or other object reference points. Do not use absolute coordinates.
4. Include transition buffers between animations
5. Specify z-index for overlapping elements
6. All colors must use hex codes or named colors
7. Define scale relative to base unit
8. No external dependencies
9. Currently, there are no images or other assets available locally or remotely for you to use in the scene. Only include elements that can be generated through manim.
10. **Do not generate any code in this plan, except for illustrative examples where necessary. This plan is for outlining the scene and should not include any python code.**
11. **The purpose of this plan is to be a detailed guide for a human to implement the scene in manim.**`;

const _promptScenePlan = `作为一名专业的动画编剧，请根据用户的描述制作相关的动画："{topic}"，为其设计一个包含三个场景的动画场景大纲。后续将使用Manim来实现，因此你设计的大纲内不要出现任何外部资源（如图片、音频、视频文件），只能使用基础的几何图形、文本、数学公式等可编程生成的元素。

主题：{topic}
描述：{description}

设计要求：
1. **场景划分**：清晰地划分三个场景，每个场景有明确的主题和目标，形成完整的教学逻辑链条
2. **内容递进**：逻辑连贯，能够循序渐进地解释和展示{topic}，从基础概念到深入理解再到实际应用
3. **教学效果**：每个场景都要有明确的教学目标和预期效果，确保观众能够逐步掌握知识点
4. **视觉设计**：提供具体的视觉表现建议，包括图形类型、动画效果、颜色运用等，但避免技术实现细节
5. **时长控制**：每个场景应控制在1分钟之内，总时长不超过2分钟

输出格式要求：

**动画描述**：{description}

**脚本大纲**：
<SCENE_1>
**场景标题**：[简洁明确的场景名称]
**核心内容**：[详细描述本场景要讲解的主要知识点，包括具体概念、公式、原理等]
**教学目标**：[明确说明观众看完后应该掌握什么，能够解决什么问题]
**视觉元素**：[描述需要的图形、图表、动画效果，如：圆形、直线、坐标系、文字标注等]
**动画流程**：[按时间顺序描述动画的展现过程，如：先显示什么，然后如何变化，最后呈现什么效果]
</SCENE_1>

<SCENE_2>
**场景标题**：[与场景一有逻辑衔接的场景名称]
**核心内容**：[在场景一基础上的进阶内容，注意知识点的连贯性]
**教学目标**：[在前一场景基础上的进一步学习目标]
**视觉元素**：[所需的视觉元素，可以复用场景一的元素并添加新元素]
**动画流程**：[详细的动画展现流程，注意与前一场景的视觉连贯性]
</SCENE_2>

<SCENE_3>
**场景标题**：[总结性或应用性的场景名称]
**核心内容**：[通常是应用、总结或拓展内容，形成完整的知识闭环]
**教学目标**：[最终的学习目标，整合前两个场景的知识]
**视觉元素**：[综合性的视觉元素，可能包含前两个场景的元素组合]
**动画流程**：[完整的动画流程，注重整体性和总结性]
</SCENE_3>

**整体要求**：
- 确保三个场景形成完整的教学体系，有明确的起承转合
- 视觉元素要简洁明了，避免过于复杂的设计
- 动画流程要符合认知规律，由浅入深，循序渐进
- 语言表达要准确专业，同时通俗易懂`;

const _promptSceneTechnicalImplementation = `You are an expert in educational video production and Manim (Community Edition), adept at translating pedagogical narration plans into robust and spatially accurate Manim code.  
**Reminder:** This technical implementation plan is fully self-contained. There is no dependency on the implementation from any previous or subsequent scenes.

Create a detailed technical implementation plan for Scene {scene_number} (Manim code focused), *informed by the provided Manim documentation context*, strictly adhering to defined spatial constraints (safe area margins: 0.5 units, minimum spacing: 0.3 units), and **addressing potential text bounding box overflow issues**.

Topic: {topic}
Description: {description}

Scene Overview:
{scene_outline}

Scene Vision and Storyboard:
{scene_vision_storyboard}

The following manim plugins are relevant to the scene:
{relevant_plugins}

**Spatial Constraints (Strictly Enforced):**
*   **Safe area margins:** 0.5 units on all sides from the scene edges.  All objects must be positioned within these margins.
*   **Minimum spacing:** 0.3 units between any two Manim objects (measured edge to edge). This prevents overlaps and maintains visual clarity.

**Positioning Requirements:**
1.  All positioning MUST be relative (\`next_to\`, \`align_to\`, \`shift\`) from ORIGIN, safe margins, or other objects. **No absolute coordinates are allowed.**
2.  Use transition buffers (\`Wait\` times) between sub-scenes and animation steps.

**Diagrams/Sketches (Highly Recommended):**
*   Include diagrams/sketches (even text-based) for complex layouts to visualize spatial relationships, improve clarity, and reduce spatial errors.

**Common Mistakes:**
*   The Triangle class in Manim creates equilateral triangles by default. To create a right-angled triangle, use the Polygon class instead.

**Manim Plugins:**
*   You may use established, well-documented Manim plugins if they offer significant advantages in terms of code clarity, efficiency, or functionality not readily available in core Manim.
*   **If a plugin is used:**
    *   Clearly state the plugin name and version (if applicable).
    *   Provide a brief justification for using the plugin (e.g., "Using \`manim-plugin-name\` for its advanced graph layout capabilities").
    *   Ensure all plugin usage adheres to the plugin's documentation.
    *   Include a comment in the plan: \`### Plugin: <plugin_name> - <brief justification>\`.

**Focus:**
*   Creating *pedagogically sound and spatially correct Manim code*.
*   Detailed technical descriptions, referencing Manim documentation.
*   Strict adherence to spatial constraints and relative positioning.

You MUST generate the technical implementation plan for the scene in the following format (from \`\`\`xml to </SCENE_TECHNICAL_IMPLEMENTATION_PLAN>\`\`\`):

\`\`\`xml
<SCENE_TECHNICAL_IMPLEMENTATION_PLAN>
0. **Dependencies**:
    - **Manim API Version**: Target the latest stable Manim release, using only documented API elements.
    - **Allowed Imports**: \`manim\`, \`numpy\`, and any explicitly approved and documented Manim plugins.  No external assets (e.g., images, audio, or video files) are allowed, but established Manim plugins are permitted.
    
1. **Manim Object Selection & Configuration (Text and Shapes)**:
    - Clearly define the Manim objects (e.g., \`Tex\`, \`MathTex\`, \`Circle\`, \`Line\`, etc.) used to construct the scene.  Also include any objects provided by used plugins.
    - Specify all key parameters such as text content, font size, color, stroke, or shape dimensions.
    - **Text Considerations**:
        - **Use \`MathTex\` for mathematical expressions and equations, ensuring valid LaTeX syntax.** For example: \`MathTex("x^2 + y^2 = r^2")\`.
        - **Use \`Tex\` for all non-mathematical text, including titles, labels, explanations, and general text.** For example: \`Tex("This is a circle")\`.
        - **If you need to include regular text *within* a \`MathTex\` environment (e.g., for explanations alongside a formula), use the \`\\text{{}}\` command.** For example: \`MathTex(r"\\text{{Area of circle}} = \\pi r^2")\`.
        - **Do not use \`MathTex\` for regular text, as it will result in incorrect spacing and formatting.**
        - **LaTeX Packages**: If any \`Tex\` or \`MathTex\` objects require LaTeX packages beyond those included in Manim's default template, specify them here.  For example: "Requires: \`\\usepackage{{amssymb}}\`".  Create a \`TexTemplate\` object and add the necessary packages using \`add_to_preamble()\`.
        - **Font Size Recommendations**:
            - If there is title text, font size is highly recommended to be 28.
            - If there are side labels or formulas, font size is highly recommended to be 24.
            - However, if the text has more than 10 words, the font size should be reduced further and multiple lines should be used.
    - Confirm all objects begin within the safe area (0.5 units from all edges) and maintain at least 0.3 units spacing to avoid overlaps.
    
2. **VGroup Structure & Hierarchy**:
    - Organize related elements into \`VGroup\`s for efficient spatial and animation management.  If a plugin provides a specialized group-like object, consider using it.
    - For each \`VGroup\`, define the parent-child relationships and ensure internal spacing of at least 0.3 units.
    - Clearly document the purpose for each grouping (e.g., "formula_group" for mathematical expressions).
    
3. **Spatial Positioning Strategy**:
    - Mandate the exclusive use of relative positioning methods (\`next_to\`, \`align_to\`, \`shift\`), based on ORIGIN, safe margins, or other objects.
    - For every object, specify:
        - The reference object (or safe edge) used for positioning.
        - The specific method (and direction/aligned edge) along with a \`buff\` value (minimum 0.3 units).
    - Outline the layout in sequential stages, inserting visual checkpoints to verify that every element continues to respect safe margins and spacing.
    - Highlight measures to safeguard text bounding boxes, especially for multi-line text.
    - Reference the font size recommendations under "Text Considerations" to ensure appropriate sizing and prevent overflow.
    
4. **Animation Methods & Object Lifecycle Management**:
    - Define clear animation sequences using documented methods such as \`Create\`, \`Write\`, \`FadeIn\`, \`Transform\`, and corresponding removal animations (\`FadeOut\`, \`Uncreate\`). Include animation methods from plugins if they are used.
    - For each animation, specify parameters like \`run_time\`, \`lag_ratio\`, and the use of \`Wait()\` for transition buffers.
    - Ensure every object's appearance and removal is managed to prevent clutter and maintain scene clarity.
    
5. **Code Structure & Reusability**:
    - Propose modular functions for creating and animating common objects to promote code reusability.
    - Organize the overall code structure into logical sections: dependencies, object definitions, individual layout stages, and the main \`construct\` method.
    - Include inline comments to document the rationale for configuration choices, referencing the Manim Documentation *and the plugin documentation where applicable*.
    
***Mandatory Safety Checks***:
    - **Safe Area Enforcement**: All objects, including text bounding boxes, must remain within 0.5 unit margins.
    - **Minimum Spacing Validation**: Confirm a minimum of 0.3 units spacing between every pair of objects.
    - **Transition Buffers**: Use explicit \`Wait()\` calls to separate animation steps and sub-scenes.
</SCENE_TECHNICAL_IMPLEMENTATION_PLAN>
\`\`\`
`;

const _promptSceneVisionStoryboard = `作为一名经验丰富的Manim动画设计师，请为以下动画场景生成一份详细的视觉故事板描述。这份描述将直接用于指导Manim代码的编写和实现。

**技术规格**：
- 视频比例：16:9
- 坐标系范围：X轴 [-7.1, 7.1]，Y轴 [-4.0, 4.0]
- 画布中心：原点 (0, 0)
- 安全区域：建议重要元素保持在 X轴 [-6.5, 6.5]，Y轴 [-3.5, 3.5] 范围内

**坐标描述要求**：
- 所有图形必须使用精确坐标描述，不允许模糊表述
- 三角形、矩形等多边形必须详细列出每个顶点坐标
- 动画变化必须明确起始坐标和结束坐标
- 示例：三角形顶点 A(0,0), B(2,0), C(1,1.5) → 变化后 A(1,0), B(3,0), C(2,1.5)

**项目背景**：
主题：{topic}
描述：{description}

**完整场景大纲**：
{scene_outline}

**当前场景详情（场景 {scene_number}/3）**：
{scene_content}

**任务要求**：
请为当前场景创建详细的视觉故事板，包含以下六个核心部分：

**输出格式**：
<SCENE_VISION_STORYBOARD_PLAN>

**1. 开场设定**
- 场景开始时的初始画面状态
- 背景元素的设置（如坐标系、网格等）
- 初始可见元素及其精确位置

**2. 核心元素清单**
- 数学图形：[列出所有几何图形，包含精确坐标]
- 公式文本：[使用LaTeX格式，如 $a^2 + b^2 = c^2$]
- 标签文字：[位置坐标和内容]
- 颜色方案：[为不同元素指定颜色，如 RED, BLUE, GREEN 等]
- 尺寸规格：[线条粗细、字体大小等]

**3. 动画时间轴**
按时间顺序详细描述：
- [0-10秒] 动作描述：具体的动画效果和坐标变化
- [10-20秒] 动作描述：下一个动画步骤
- [继续按时间段描述...]
- 每个时间段包含：出现/消失的元素、移动路径、变形过程、颜色变化

**4. 文本与公式展示**
- 标题文字：位置坐标、内容、出现时机
- 解释文本：位置坐标、内容、出现时机
- 数学公式：LaTeX格式、位置坐标、出现时机
- 标注说明：箭头指向、标签内容、位置坐标

**5. 重点强调策略**
- 关键信息点：[明确指出本场景的核心知识点]
- 视觉强调方法：
  * 颜色高亮：[哪些元素用什么颜色强调]
  * 动画特效：[放大、闪烁、旋转等效果]
  * 指示元素：[箭头、圆圈、下划线的位置和样式]
- 强调时机：[在动画的哪个时间点进行强调]

**6. 场景转换**
- 结束状态：场景结束时所有元素的最终位置和状态
- 过渡准备：为下一场景保留或移除的元素
- 连接提示：与下一场景的逻辑和视觉连接点

**技术实现提示**：
- 建议的Manim对象类型（如 Polygon, Circle, Text, MathTex 等）
- 推荐的动画方法（如 Create, Transform, FadeIn, Write 等）
- 可用的相关插件：{relevant_plugins}
- 性能优化建议（避免过多同时动画、合理分组等）

</SCENE_VISION_STORYBOARD_PLAN>

**质量检查要求**：
- 确保所有坐标都在有效范围内
- 验证动画逻辑的连贯性和教学效果
- 检查视觉元素的可读性和美观性
- 确认与整体场景大纲的一致性`;

const _promptTeachingFramework = `# Comprehensive Educational Video Content Framework

## 1. Pre-Production Planning

### A. Learning Objectives
- **Knowledge Level (Remember & Understand)**
  Define specific, measurable learning outcomes that can be clearly assessed and evaluated. These outcomes should be concrete and observable, allowing instructors to verify that learning has occurred. Each outcome should be written using precise language that leaves no ambiguity about what constitutes success. For example, "After watching this video, learners will be able to define and explain the concept of variables in programming" provides a clear benchmark for assessment.

  Action verbs are essential tools for crafting effective learning objectives. Choose verbs like define, list, describe, explain, and identify that clearly indicate the expected cognitive processes. These verbs should align with Bloom's Taxonomy to ensure appropriate cognitive engagement. When applicable, ensure all objectives align with relevant curriculum standards to maintain educational consistency and meet institutional requirements.

- **Comprehension Level (Analyze & Evaluate)**
  Develop objectives that emphasize deeper understanding and connections between concepts. These objectives should go beyond simple recall to require analysis and evaluation of the material. Students should be able to make meaningful connections between different aspects of the content and explain their relationships. For example, "Learners will be able to compare different data types and explain when to use each" demonstrates this deeper level of understanding.

  Critical thinking elements should be deliberately incorporated into each objective. Create scenarios that challenge students to apply their knowledge in new contexts. These scenarios should require careful analysis and reasoned decision-making to solve problems effectively. Design learning experiences that encourage students to question assumptions and develop analytical skills.

- **Application Level (Apply & Create)**
  Develop practical skills that directly translate to real-world applications and scenarios. These objectives should focus on hands-on experience and tangible outcomes that demonstrate mastery. For example, "Learners will be able to write a basic program using variables and proper naming conventions" provides a clear, actionable goal that can be demonstrated through practical work.

  Include hands-on exercises that allow students to practice and refine their skills in a supported environment. These exercises should gradually increase in complexity to build confidence and competence. Provide real-world context by incorporating authentic scenarios and problems that students might encounter in their future careers or daily lives. This connection to reality helps maintain engagement and demonstrates the immediate value of the learning.

- **Target Audience Analysis**
  Conduct thorough demographic research to understand your learners' backgrounds, ages, and educational levels. This analysis should include assessment of prior knowledge and experience with the subject matter. Consider the technical capabilities of your audience, including their access to necessary tools and technologies.

  Evaluate different learning preferences and styles within your target audience. This understanding helps in designing varied content that appeals to visual, auditory, and kinesthetic learners. Consider cultural and linguistic factors that might impact learning effectiveness. Create content that is inclusive and accessible to learners from diverse backgrounds. Account for varying levels of technical proficiency and ensure your content can be accessed across different devices and platforms.

### B. Content Structure

- **Hook (5-10% of duration)**
  Begin each video with a compelling problem or scenario that immediately captures attention and creates interest. This hook should be relevant to the content while being unexpected or intriguing enough to maintain viewer engagement. Use surprising facts or statistics that challenge common assumptions or demonstrate the importance of the topic.

  Share relevant real-world applications that demonstrate immediate value to the learner. For example, "What if you could automate your daily tasks with just a few lines of code?" creates immediate interest by connecting to practical benefits. The hook should create an emotional connection and generate curiosity about the upcoming content. Consider using storytelling elements or real-world problems that your audience can relate to.

- **Context (10-15%)**
  Provide clear explanations of how the content relates to real-world situations and problems. This context should help learners understand why the material is relevant to their lives or career goals. Make explicit connections to previous knowledge and experiences that learners can build upon.

  Address the fundamental question of "Why should I learn this?" by demonstrating practical applications and benefits. This explanation should be concrete and specific to your audience's needs and interests. Set clear expectations for learning outcomes so students understand what they will gain from the content. Provide a roadmap for the learning journey ahead, including how this content connects to future topics and skills.

- **Core Content (60-70%)**
  Organize material in a logical progression that builds from fundamental concepts to more complex applications. This progression should be carefully planned to avoid overwhelming learners while maintaining engagement. Include multiple examples that demonstrate concepts from different angles and perspectives.

  Use varied teaching methods to accommodate different learning styles and maintain interest. These methods might include demonstrations, animations, code examples, and interactive elements. Implement frequent knowledge checks throughout the content to ensure understanding and maintain engagement. Break complex topics into manageable chunks that can be easily processed and remembered.

- **Practice/Application (10-15%)**
  Create guided practice opportunities that allow learners to apply new knowledge in a supported environment. These practice sessions should include clear instructions and immediate feedback mechanisms. Design interactive elements that engage learners and require active participation rather than passive viewing.

  Develop problem-solving scenarios that challenge learners to apply concepts in realistic situations. These scenarios should gradually increase in complexity as learners gain confidence. Include opportunities for peer learning and collaboration when possible. Provide scaffolded support that can be gradually removed as learners become more proficient.

- **Summary (5-10%)**
  Conclude each video with a comprehensive recap of key points and main takeaways. This summary should reinforce the most important concepts and their practical applications. Preview upcoming topics to create anticipation and show how current learning connects to future content.

  Provide specific action items that learners can implement immediately to reinforce their learning. These should be concrete, achievable tasks that build confidence and competence. Share additional resources for further learning, including reference materials, practice exercises, and advanced topics. Create clear connections between the current content and future learning objectives.

## 2. Instructional Design Elements

### A. Cognitive Load Management

- **Chunking Strategies**
  Break complex content into manageable segments of 3-5 minutes each. These chunks should focus on single concepts or closely related ideas that form a coherent unit. Use clear transitions between segments to maintain flow while allowing for cognitive processing.

  Implement progressive complexity by building from basic concepts to more advanced applications. This progression should be carefully planned to avoid overwhelming learners. Include strategic pauses and processing time between segments to allow for reflection and integration of new information. Use visual and verbal cues to signal transitions between different concepts or levels of complexity.

- **Visual Organization**
  Develop a consistent visual hierarchy that guides learners through the content effectively. This hierarchy should use size, color, and placement to indicate the relative importance of different elements. Implement clean, uncluttered designs that minimize distractions and focus attention on key concepts.

  Apply color coding consistently to help learners identify and remember related concepts. This coding should be intentional and meaningful, not merely decorative. Use white space effectively to create visual breathing room and help separate different concepts. Ensure that visual elements support rather than compete with the learning objectives.

- **Information Processing**
  Carefully limit the introduction of new concepts to 5-7 per video to prevent cognitive overload. This limitation helps ensure that learners can effectively process and retain the information presented. Develop and use mnemonics and memory aids that help learners organize and remember key concepts.

  Provide visual anchors that learners can reference throughout the content. These anchors should help maintain context and show relationships between concepts. Include strategic review points that reinforce previous learning before introducing new material. Create clear connections between new information and existing knowledge to facilitate better retention.

### B. Engagement Techniques

- **Storytelling Elements**
  Develop a clear narrative flow that carries learners through the content naturally. This narrative should have a beginning, middle, and end that maintains interest and supports learning objectives. Use character-driven examples that learners can relate to and remember.

  Include elements of conflict and resolution to create tension and maintain engagement. These elements should be relevant to the learning objectives and help illustrate key concepts. Maintain an emotional connection through relatable scenarios and authentic problems. Create story arcs that span multiple videos or modules to maintain long-term engagement.

- **Visual Support**
  Create relevant graphics and animations that enhance understanding of key concepts. These visual elements should be purposeful and directly support learning objectives, not merely decorative. Implement a consistent visual style across all content to maintain professionalism and reduce cognitive load.

  Develop clear infographics that break down complex concepts into understandable components. These should use visual hierarchy and design principles effectively. Use motion and animation thoughtfully to direct attention to important elements and demonstrate processes. Ensure all visual elements are accessible and effectively communicate their intended message.

- **Interactive Components**
  Design and embed quiz questions that check understanding at key points in the content. These questions should be strategically placed to maintain engagement and reinforce learning. Include deliberate pause points that encourage reflection and active processing of information.

  Create coding challenges or practical exercises that allow immediate application of concepts. These should be scaffolded appropriately for the learner's skill level. Provide multiple opportunities for feedback, both automated and instructor-guided when possible. Design interactive elements that encourage experimentation and learning from mistakes.

## 3. Content Delivery Framework

### A. Teaching Sequence

1. **Activate**
   Begin each learning session by connecting to familiar concepts that students already understand. This activation of prior knowledge creates a foundation for new learning and helps students feel confident. Use carefully chosen analogies and metaphors that bridge the gap between known and new concepts. These comparisons should be relevant to your audience's experience and background.

   Create explicit connections to previous learning modules or related concepts. These connections help students build a coherent mental model of the subject matter. Assess prior knowledge through quick activities or questions that reveal students' current understanding. Use this assessment to adjust your teaching approach and address any misconceptions early in the lesson.

2. **Present**
   Deliver clear, structured explanations of new concepts that build upon activated knowledge. These explanations should use precise language while remaining accessible to your target audience. Employ multiple representation methods, including verbal explanations, visual diagrams, and interactive demonstrations. This variety helps accommodate different learning styles and reinforces understanding.

   Provide step-by-step demonstrations that break complex processes into manageable parts. Each step should be clearly explained and connected to the overall objective. Include real-world examples that illustrate practical applications of the concepts. These examples should be relevant to your audience's interests and career goals.

3. **Guide**
   Develop worked examples that demonstrate expert problem-solving processes and thinking strategies. These examples should include explicit explanations of decision-making and common pitfalls to avoid. Share expert thinking processes by "thinking aloud" through problem-solving steps. This transparency helps students understand the metacognitive aspects of learning.

   Create scaffolded learning experiences that gradually reduce support as students gain confidence. Begin with highly structured guidance and progressively move toward independent work. Address common misconceptions and errors proactively, explaining why they occur and how to avoid them. Provide clear strategies for troubleshooting and problem-solving.

4. **Practice**
   Design guided exercises that allow students to apply new knowledge with appropriate support. These exercises should be carefully sequenced to build confidence and competence gradually. Include opportunities for independent practice that reinforce learning and build autonomy. Ensure these practice sessions are aligned with learning objectives and provide clear success criteria.

   Create peer learning opportunities that allow students to learn from and teach others. These interactions can reinforce understanding and develop communication skills. Implement immediate feedback mechanisms that help students understand their progress and areas for improvement. This feedback should be specific, constructive, and actionable.

5. **Apply**
   Develop real-world projects that require students to integrate and apply their learning in authentic contexts. These projects should be challenging but achievable, with clear connections to practical applications. Create case studies that illustrate complex scenarios and require critical thinking and problem-solving skills. These studies should reflect realistic situations students might encounter in their careers.

   Design problem-solving scenarios that encourage creative application of knowledge and skills. These scenarios should have multiple possible solutions to encourage innovative thinking. Provide opportunities for creative applications that allow students to extend their learning in personally meaningful ways. Support experimentation and risk-taking in a safe learning environment.

### B. Presentation Techniques

- **Transitions**
   Implement clear verbal cues that signal shifts between concepts or activities. These cues help students maintain orientation and prepare for new information. Design visual transition elements that support cognitive processing and maintain engagement. These elements should be consistent throughout your content to establish familiar patterns.

   Create concept maps that show relationships between different topics and ideas. These maps help students understand how current learning connects to broader concepts. Use progress indicators that help students track their advancement through the material. These indicators should provide a sense of accomplishment and motivation.

- **Multiple Representations**
   Combine text and graphics effectively to convey information through multiple channels. This combination should be purposeful and coordinated to enhance understanding. Integrate audio and visual elements that complement each other and reinforce key concepts. Ensure these elements work together without creating cognitive overload.

   Develop interactive elements that encourage active engagement with the content. These elements should provide immediate feedback and support learning objectives. Include physical demonstrations when appropriate to illustrate concepts in tangible ways. These demonstrations should be clear, visible, and directly relevant to learning goals.

## 4. Assessment Integration

### A. Knowledge Verification
- **Formative Assessment**
   Implement regular quick checks for understanding throughout the learning process. These checks should be low-stakes and provide immediate feedback to both learner and instructor. Design self-assessment prompts that encourage students to reflect on their own learning progress. These prompts should help students develop metacognitive skills and self-awareness.

   Create opportunities for peer discussion and feedback that deepen understanding through explanation and debate. These discussions should be structured to ensure productive exchanges and learning outcomes. Develop reflection questions that help students connect new learning to existing knowledge and future applications. These questions should promote deep thinking and personal connection to the material.

- **Summative Assessment**
   Design project-based assessments that evaluate comprehensive understanding and practical application. These projects should integrate multiple concepts and skills learned throughout the course. Guide students in developing portfolios that demonstrate their learning journey and achievements. These portfolios should include examples of both process and product.

   Create opportunities for skill demonstration that allow students to show mastery in authentic contexts. These demonstrations should reflect real-world applications and standards. Develop knowledge application assessments that require students to transfer learning to new situations. These assessments should evaluate both understanding and adaptability.

### B. Learning Reinforcement
- **Review Strategies**
   Implement spaced repetition techniques that optimize long-term retention of information. This approach should strategically revisit concepts at increasing intervals. Create concept mapping exercises that help students visualize and understand relationships between ideas. These maps should become increasingly complex as understanding develops.

   Guide students in knowledge synthesis activities that combine multiple concepts into coherent understanding. These activities should help students see the bigger picture and make meaningful connections. Design application scenarios that require students to apply knowledge in new and challenging contexts. These scenarios should build confidence and demonstrate practical relevance.

## 5. Technical Considerations

### A. Video Production Elements
- **Duration Guidelines**
   Optimize video length to maintain engagement while effectively covering necessary content. The ideal duration of 6-12 minutes balances attention span with comprehensive coverage. Implement concept-based segmentation that breaks longer topics into digestible chunks. This segmentation should follow natural breaking points in the material.

   Consider attention span patterns when planning content structure and pacing. Include variety and interaction to maintain engagement throughout longer sessions. Adapt content length to platform-specific requirements and viewing habits. Consider mobile viewing habits and platform limitations in your planning.

- **Quality Standards**
   Ensure professional audio quality through proper equipment and recording techniques. This includes clear voice recording, minimal background noise, and appropriate volume levels. Maintain consistent lighting that enhances visibility and reduces viewer fatigue. Pay attention to both subject lighting and screen content visibility.

   Create clear visual presentations that effectively communicate key concepts. This includes appropriate font sizes, color contrast, and visual hierarchy. Maintain appropriate pacing that allows for processing time while maintaining engagement. Consider your audience's needs and learning objectives when determining pace.

### B. Accessibility Features
- **Universal Design**
   Create content that accommodates multiple learning modalities and preferences. This includes providing information through visual, auditory, and interactive channels. Ensure screen reader compatibility by following accessibility best practices and standards. This includes proper heading structure and alt text for images.

   Implement appropriate color contrast considerations for all visual elements. This ensures content is accessible to viewers with various visual abilities. Provide alternative text descriptions for all important images and graphics. These descriptions should convey the same information as the visual elements.

## 6. Follow-up Resources

### A. Supporting Materials
- **Resource Types**
   Develop comprehensive practice exercises that reinforce learning and build confidence. These exercises should range from basic to advanced, accommodating different skill levels. Create well-documented code samples that demonstrate best practices and common patterns. These samples should include comments explaining key concepts and decisions.

   Compile detailed reference guides that support independent learning and problem-solving. These guides should be easily searchable and regularly updated. Design cheat sheets that provide quick access to essential information and common procedures. These should be concise while including all crucial information.

### B. Implementation Guide
- **Learning Pathways**
   Create clear prerequisite maps that show relationships between different topics and skills. This mapping helps students understand learning dependencies and plan their progress. Provide advanced topic suggestions that help motivated learners extend their knowledge. These suggestions should include resources and guidance for self-directed learning.

   Develop skill progression guides that show clear paths from beginner to advanced levels. These guides should include milestones and checkpoints for measuring progress. Suggest project ideas that allow practical application of learned skills. These projects should be scalable to different skill levels and interests.`;

const _promptVisualFixError = `You are an expert in Manim animations. Your task is to ensure that the rendered animation frame (image) aligns with the intended teaching content based on the provided implementation plan.

Instructions:
Evaluate whether the object coordinates and positions in the image match the described plan and educational purpose.
The implementation plan serves as a reference, but your primary goal is to verify that the rendered animation frame supports effective teaching.
For example:
* If the object is supposed to be at the top of the screen, but it is at the bottom, you need to adjust the position.
* If the object is supposed to be at the left side but it is too far to the left, you need to adjust the position.
* If the two objects are not supposed to be overlapped but it is overlapped, you need to adjust the positions.

If adjustments are needed, provide the complete code of the adjusted version.
If the current code is correct, return it as is.

Manim Implementation Plan:
{implementation}

Generated Code:
{generated_code}

Return the complete code of the adjusted version if the code needs to be updated. If the code is correct, only return "<LGTM>" as output.
`;

const _promptVisualSelfReflection = `You are an expert in Manim animations and educational video quality assessment. Your task is to analyze a rendered Manim video and its corresponding audio narration to identify areas for visual and auditory improvement, ensuring alignment with the provided implementation plan and enhancing the video's teaching effectiveness.

Please analyze the provided Manim video and listen to the accompanying audio narration. Conduct a thorough self-reflection focusing on the following aspects:

**1. Visual Presentation and Clarity (Automated VLM Analysis & Expert Human-like Judgment):**

*   **Object Overlap:** Does the video exhibit any visual elements (text, shapes, equations, etc.) overlapping in a way that obscures information or makes the animation difficult to understand? If possible, Detect regions of significant overlap and highlight them in your reflection.
*   **Out-of-Bounds Objects:** Are any objects positioned partially or entirely outside of the visible frame of the video? Identify and report objects that appear to be clipped or outside the frame boundaries.
*   **Incorrect Object Positioning:** Based on your understanding of good visual design and the scene's educational purpose, are objects placed in positions that are illogical, distracting, or misaligned with their intended locations or relationships to other elements as described in the implementation plan? Consider:
    *   **Logical Flow:** Does the spatial arrangement support the intended visual flow and narrative progression of the scene?
    *   **Alignment and Balance:** Is the scene visually balanced? Are elements aligned in a way that is aesthetically pleasing and contributes to clarity, or does the layout appear haphazard or unbalanced?
    *   **Proximity and Grouping:** Are related elements positioned close enough to be visually grouped, and are unrelated elements sufficiently separated to avoid visual clutter?
*   **General Visual Clarity & Effectiveness:** Consider broader aspects of visual communication. Are there any other issues that detract from the video's clarity, impact, or overall effectiveness? This could include:
    *   **Visual Clutter:** Is the scene too busy or visually overwhelming at any point? Are there too many elements on screen simultaneously?
    *   **Poor Spacing/Layout:** Is the spacing between elements inconsistent or inefficient, making the scene feel cramped or unbalanced? Are margins and padding used effectively?
    *   **Ineffective Use of Color:** Are color choices distracting, clashing, or not contributing to the animation's message? Are colors used consistently and purposefully to highlight key information?
    *   **Pacing Issues (Visual):** Is the visual animation too fast or too slow in certain sections, hindering comprehension? Are visual transitions smooth and well-timed?
    *   **Animation Clarity:** Are the animations themselves clear and helpful in conveying the intended information? Do animations effectively guide the viewer's eye and focus attention?

**2. Narration Quality:**

*   **Narration Clarity and Pacing:** Is the narration clear, concise, and easy to understand? Is the pacing of the narration appropriate for the visual content and the target audience? Does the narration effectively support the visual explanations?
*   **Narration Sync with Visuals:** Does the narration effectively synchronize with the on-screen visuals? Use VLM to analyze the video and identify instances where the narration is misaligned with the animations or visual elements it is describing. Report specific timings of misalignment.

**3. Alignment with Implementation Plan:**

*   **Visual Fidelity:** Does the rendered video accurately reflect the visual elements and spatial arrangements described in the provided Manim Implementation Plan? Identify any deviations.
*   **Animation Fidelity:** Do the animations in the video match the animation methods and sequences outlined in the Implementation Plan? Report any discrepancies.

Manim Implementation Plan:
{implementation}

Generated Code:
{generated_code}

Output Format 1:
If any issues are identified in visual presentation, audio quality, narration, or plan alignment, please provide a detailed reflection on the issues and how to improve the video's visual and auditory quality, narration effectiveness, and code correctness. Then, you must return the updated Python code that directly addresses these issues. The code must be complete and executable.

<reflection>
[Detailed reflection on visual, auditory, narration, and plan alignment issues and improvement suggestions. Include specific timings for narration/visual sync issues and descriptions of object overlap/out-of-bounds problems if detected by VLM.  Be specific about code changes needed for improvement.]
</reflection>
<code>
[Improved Python Code - Complete and Executable - Directly Addressing Reflection Points]
</code>

Output Format 2:
If no issues are found and the video and audio are deemed high quality, visually clear, narratively effective, and fully aligned with the implementation plan, please explicitly only return "<LGTM>" as output.`;

module.exports = {
  _bannedReasonings,
  _codeBackground,
  _codeColorCheatsheet,
  _codeDisable,
  _codeFontSize,
  _codeLimit,
  _promptAnimationFixError,
  _promptAnimationRagQueryGeneration,
  _promptAnimationRagQueryGenerationFixError,
  _promptAnimationSimple,
  _promptBestPractices,
  _promptCodeGeneration,
  _promptContextLearningAnimationNarration,
  _promptContextLearningCode,
  _promptContextLearningScenePlan,
  _promptContextLearningTechnicalImplementation,
  _promptContextLearningVisionStoryboard,
  _promptDetectPlugins,
  _promptFixError,
  _promptManimCheatsheet,
  _promptRagQueryGenerationCode,
  _promptRagQueryGenerationFixError,
  _promptRagQueryGenerationNarration,
  _promptRagQueryGenerationStoryboard,
  _promptRagQueryGenerationTechnical,
  _promptRagQueryGenerationVisionStoryboard,
  _promptSceneAnimationNarration,
  _promptSceneImplementation,
  _promptScenePlan,
  _promptSceneTechnicalImplementation,
  _promptSceneVisionStoryboard,
  _promptTeachingFramework,
  _promptVisualFixError,
  _promptVisualSelfReflection,
};
