const mysql = require('mysql');
const config = require('./config');

const pool = mysql.createPool({
  ...config,
  connectTimeout: 10000 // 增加连接超时时间，单位为毫秒
});

// 封装查询函数，使用 Promise
function query(sql, params) {
  return new Promise((resolve, reject) => {
    pool.query(sql, params, (error, results) => {
      if (error) {
        return reject(error);
      }
      resolve(results);
    });
  });
}

// 测试数据库连接
function testConnection() {
  return new Promise((resolve, reject) => {
    pool.getConnection((err, connection) => {
      if (err) {
        return reject(err);
      }
      console.log('Database connected successfully');
      connection.release();
      resolve();
    });
  });
}

// 初始化数据库
async function initializeDb() {
  try {
    await testConnection(); // 测试数据库连接

    // await query("DROP TABLE IF EXISTS users");
    // await query("DROP TABLE IF EXISTS relate");
    // await query("DROP TABLE IF EXISTS chats_record");

    // await query(`
    //   CREATE TABLE users (
    //     userId INT AUTO_INCREMENT PRIMARY KEY,
    //     name VARCHAR(255),
    //     age INT,
    //     gender INT,
    //     wa_phone VARCHAR(20)
    //   )
    // `);
    // console.log('Table users created or already exists');

    // await query(`
    //   CREATE TABLE relate (
    //     wa_phone VARCHAR(20) PRIMARY KEY,
    //     name VARCHAR(255),
    //     isBind BOOLEAN,
    //     userId INT
    //   )
    // `);
    // // console.log('Table relate created or already exists');

    // // await query(`
    // //   CREATE TABLE chats_record (
    // //     id INT AUTO_INCREMENT PRIMARY KEY,
    // //     wa_phone VARCHAR(20),
    // //     message TEXT,
    // //     timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    // //   )
    // // `);
    // // console.log('Table chats_record created or already exists');


    // const users = [
    //   ['Alice', 30, 2, '************'],
    //   ['Bob', 25, 1, '************'],
    //   ['Charlie', 28, 1, '************'],
    //   ['Diana', 22, 2, '************'],
    //   ['Eve', 35, 2, '************']
    // ];
    // await query("INSERT INTO users (name, age, gender, phone) VALUES ?", [users.map(user => [user[0], user[1], user[2], user[3]])]);
    console.log('Initial data inserted into users');
  } catch (err) {
    console.error('Error initializing database:', err);
  }
}

// 初始化数据库
initializeDb();

module.exports = {
  query
};