const { logger } = require('./src/utils/logger');
const fs = require('fs');
const path = require('path');

function useLoggerApi(app) {
  const prefix = '/api/logs';

  // 获取日志统计信息
  app.get(`${prefix}/stats`, (req, res) => {
    try {
      const stats = logger.getLogStats();
      res.json({
        code: 0,
        message: '获取成功',
        data: stats,
      });
    } catch (error) {
      logger.error('获取日志统计信息失败', { error: error.message });
      res.status(500).json({
        code: 1,
        message: '获取日志统计信息失败',
        error: error.message,
      });
    }
  });

  // 获取特定级别的日志文件列表
  app.get(`${prefix}/files`, (req, res) => {
    try {
      const { level, date } = req.query;
      const logDir = logger.logDir;

      if (!fs.existsSync(logDir)) {
        return res.json({
          code: 0,
          message: '获取成功',
          data: { files: [] },
        });
      }

      let files = fs.readdirSync(logDir);

      // 过滤文件
      if (level) {
        files = files.filter((file) => file.startsWith(level));
      }

      if (date) {
        files = files.filter((file) => file.includes(date));
      }

      const fileList = files.map((file) => {
        const filePath = path.join(logDir, file);
        const stats = fs.statSync(filePath);
        return {
          name: file,
          size: stats.size,
          modified: stats.mtime,
          path: filePath,
        };
      });

      res.json({
        code: 0,
        message: '获取成功',
        data: { files: fileList },
      });
    } catch (error) {
      logger.error('获取日志文件列表失败', { error: error.message });
      res.status(500).json({
        code: 1,
        message: '获取日志文件列表失败',
        error: error.message,
      });
    }
  });

  // 读取日志文件内容
  app.get(`${prefix}/content/:filename`, (req, res) => {
    try {
      const { filename } = req.params;
      const { lines = 100, offset = 0 } = req.query;

      const filePath = path.join(logger.logDir, filename);

      if (!fs.existsSync(filePath)) {
        return res.status(404).json({
          code: 1,
          message: '日志文件不存在',
        });
      }

      const content = fs.readFileSync(filePath, 'utf8');
      const allLines = content.split('\n').filter((line) => line.trim());

      const startIndex = parseInt(offset);
      const endIndex = startIndex + parseInt(lines);
      const selectedLines = allLines.slice(startIndex, endIndex);

      // 解析JSON格式的日志
      const parsedLogs = selectedLines.map((line) => {
        try {
          return JSON.parse(line);
        } catch {
          return { message: line, level: 'UNKNOWN', timestamp: null };
        }
      });

      res.json({
        code: 0,
        message: '获取成功',
        data: {
          logs: parsedLogs,
          total: allLines.length,
          offset: startIndex,
          hasMore: endIndex < allLines.length,
        },
      });
    } catch (error) {
      logger.error('读取日志文件失败', {
        filename: req.params.filename,
        error: error.message,
      });
      res.status(500).json({
        code: 1,
        message: '读取日志文件失败',
        error: error.message,
      });
    }
  });

  // 实时日志流（SSE）
  app.get(`${prefix}/stream`, (req, res) => {
    const { level = 'info' } = req.query;

    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      Connection: 'keep-alive',
      'Access-Control-Allow-Origin': '*',
    });

    // 发送连接确认
    res.write('data: {"type":"connected","message":"日志流已连接"}\n\n');

    // 监听新日志文件的写入
    const logFileName = logger.getLogFileName(level);

    if (fs.existsSync(logFileName)) {
      const watcher = fs.watchFile(logFileName, { interval: 1000 }, () => {
        try {
          const content = fs.readFileSync(logFileName, 'utf8');
          const lines = content.split('\n').filter((line) => line.trim());
          const lastLine = lines[lines.length - 1];

          if (lastLine) {
            try {
              const logEntry = JSON.parse(lastLine);
              res.write(`data: ${JSON.stringify(logEntry)}\n\n`);
            } catch (parseError) {
              // 忽略解析错误
            }
          }
        } catch (error) {
          logger.error('读取日志流文件时出错', { error: error.message });
        }
      });

      // 客户端断开连接时清理
      req.on('close', () => {
        fs.unwatchFile(logFileName);
      });
    }
  });

  // 清理旧日志
  app.post(`${prefix}/cleanup`, (req, res) => {
    try {
      const { daysToKeep = 30 } = req.body;

      logger.cleanOldLogs(daysToKeep);
      logger.info('日志清理任务已启动', { daysToKeep });

      res.json({
        code: 0,
        message: '日志清理任务已启动',
        data: { daysToKeep },
      });
    } catch (error) {
      logger.error('清理日志失败', { error: error.message });
      res.status(500).json({
        code: 1,
        message: '清理日志失败',
        error: error.message,
      });
    }
  });

  // 搜索日志
  app.post(`${prefix}/search`, (req, res) => {
    try {
      const { query, level, startDate, endDate, limit = 100 } = req.body;

      if (!query) {
        return res.status(400).json({
          code: 1,
          message: '搜索关键词不能为空',
        });
      }

      const logDir = logger.logDir;
      const results = [];

      if (!fs.existsSync(logDir)) {
        return res.json({
          code: 0,
          message: '搜索完成',
          data: { results: [], total: 0 },
        });
      }

      const files = fs.readdirSync(logDir);

      for (const file of files) {
        if (level && !file.startsWith(level)) continue;

        const filePath = path.join(logDir, file);
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n').filter((line) => line.trim());

        for (const line of lines) {
          try {
            const logEntry = JSON.parse(line);

            // 日期过滤
            if (startDate && new Date(logEntry.timestamp) < new Date(startDate))
              continue;
            if (endDate && new Date(logEntry.timestamp) > new Date(endDate))
              continue;

            // 文本搜索
            const searchText = JSON.stringify(logEntry).toLowerCase();
            if (searchText.includes(query.toLowerCase())) {
              results.push(logEntry);

              if (results.length >= limit) break;
            }
          } catch (parseError) {
            // 忽略解析错误的行
          }
        }

        if (results.length >= limit) break;
      }

      res.json({
        code: 0,
        message: '搜索完成',
        data: {
          results: results.slice(0, limit),
          total: results.length,
          query,
        },
      });
    } catch (error) {
      logger.error('搜索日志失败', {
        query: req.body.query,
        error: error.message,
      });
      res.status(500).json({
        code: 1,
        message: '搜索日志失败',
        error: error.message,
      });
    }
  });

  // 获取系统监控信息
  app.get(`${prefix}/monitor`, (req, res) => {
    try {
      const memUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();

      const monitorData = {
        uptime: process.uptime(),
        memory: {
          rss: Math.round(memUsage.rss / 1024 / 1024),
          heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
          heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
          external: Math.round(memUsage.external / 1024 / 1024),
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system,
        },
        pid: process.pid,
        version: process.version,
        platform: process.platform,
      };

      res.json({
        code: 0,
        message: '获取成功',
        data: monitorData,
      });
    } catch (error) {
      logger.error('获取系统监控信息失败', { error: error.message });
      res.status(500).json({
        code: 1,
        message: '获取系统监控信息失败',
        error: error.message,
      });
    }
  });
}

module.exports = useLoggerApi;
