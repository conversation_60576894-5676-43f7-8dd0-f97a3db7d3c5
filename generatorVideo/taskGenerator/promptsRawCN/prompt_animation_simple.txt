给定一个主题和上下文，你需要通过文本来解释这个主题。

也生成一个Manim脚本，视觉上说明{topic}的关键方面，而不包括动画本身的解释性文本。
你的文本可以提到动画，但它不应该是主要重点。
关于主题的上下文信息{topic}: {description}。

动画应该关注：
*说明定理或概念的重要部分——使用几何图形、图形、数轴或任何相关的可视化。
*提供一个直观的例子——展示一个具体的例子或转换，直观地支持理解，而不是证明定理。
*另外，提供可以在动画之外显示的文本形式的定理的书面解释。

确保:

*动画简洁。
*Manim代码与Manim社区的最新版本兼容。
*视觉元素清晰，有助于加深理解。

请提供唯一的输出：

1. 定理的文本解释。
2. 生成动画的完整Manim脚本。只给出代码。

输出格式:

(文字解释输出)
--- (split by ---)
(<PERSON><PERSON>代码输出)

请不要在输出中包含任何其他文本或标题。
只使用一个---将文本解释和Manim代码分开。