const { Embeddings } = require("@langchain/core/embeddings");
const axios = require("axios");

/**
 * 自定义嵌入类，用于连接阿里云DashScope提供的嵌入API
 */
class CustomEmbeddings extends Embeddings {
  constructor(config) {
    super();
    this.embeddingModel = config.embeddingModel || "text-embedding-v3";
    this.apiUrl =
      config.apiUrl ||
      "https://dashscope.aliyuncs.com/compatible-mode/v1/embeddings";
    this.batchSize = config.batchSize || 128;
    this.timeout = config.timeout || 60000;
    this.apiKey = config.apiKey || "sk-b70be8c61fdb497895f038fb3662c9bb";
    this.dimension = config.dimension || "1024";
    this.encodingFormat = config.encodingFormat || "float";

    // 设置认证头
    this.headers = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${this.apiKey}`,
    };
  }

  /**
   * 获取文本的嵌入向量
   * @param {string} text - 要嵌入的文本
   * @returns {Promise<number[]>} 嵌入向量
   */
  async embedQuery(text) {
    try {
      const response = await axios.post(
        this.apiUrl,
        {
          model: this.embeddingModel,
          input: text,
          dimension: this.dimension,
          encoding_format: this.encodingFormat,
        },
        {
          headers: this.headers,
          timeout: this.timeout,
        }
      );

      // 根据DashScope API响应格式提取嵌入向量
      return response.data.data[0].embedding;
    } catch (error) {
      console.error("嵌入查询时出错:", error.response?.data || error.message);
      throw new Error(
        `嵌入查询失败: ${error.response?.data?.error?.message || error.message}`
      );
    }
  }

  /**
   * 批量获取文本的嵌入向量
   * @param {string[]} documents - 要嵌入的文本数组
   * @returns {Promise<number[][]>} 嵌入向量数组
   */
  async embedDocuments(documents) {
    const embeddings = [];

    // 分批处理文档以避免请求过大
    for (let i = 0; i < documents.length; i += this.batchSize) {
      const batch = documents.slice(i, i + this.batchSize);

      try {
        // 尝试先批量处理，如果API支持的话

        // 如果批量处理失败，回退到逐个处理
        for (const doc of batch) {
          try {
            const response = await axios.post(
              this.apiUrl,
              {
                model: this.embeddingModel,
                input: doc,
                dimension: this.dimension,
                encoding_format: this.encodingFormat,
              },
              {
                headers: this.headers,
                timeout: this.timeout,
              }
            );

            // 提取嵌入向量
            embeddings.push(response.data.data[0].embedding);

            // 添加短暂延迟以避免API速率限制
            await new Promise((resolve) => setTimeout(resolve, 200));
          } catch (singleError) {
            console.error(
              `处理单个文档时出错:`,
              singleError.response?.data || singleError.message
            );
            // 添加一个空向量或默认向量，以保持索引一致性
            embeddings.push(new Array(parseInt(this.dimension)).fill(0));
          }
        }

        // 每批次处理完后添加额外延迟
        if (i + this.batchSize < documents.length) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }

        // 显示进度
        console.log(
          `已处理 ${Math.min(i + this.batchSize, documents.length)}/${
            documents.length
          } 个文档`
        );
      } catch (error) {
        console.error(
          `嵌入文档批次 ${i} 到 ${i + batch.length} 时出错:`,
          error.response?.data || error.message
        );
        throw new Error(
          `嵌入文档失败: ${
            error.response?.data?.error?.message || error.message
          }`
        );
      }
    }

    return embeddings;
  }
}

// 修改导出方式
module.exports = { CustomEmbeddings };
