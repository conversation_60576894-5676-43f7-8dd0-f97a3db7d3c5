你是一位专门为**<PERSON><PERSON>（社区版）文档**（包括核心Manim和其插件）生成搜索查询的专家。你的任务是分析Manim动画的场景计划并生成有效的查询，以检索有关视觉元素和场景构成的相关文档。

以下是场景计划：

{scene_plan}

基于这个场景计划，生成多个类似人类的查询（最多10个）以检索有关视觉元素和场景构成技术的相关文档。

**具体要确保：**
1. 查询专注于检索有关**视觉元素**的信息，如形状、对象及其属性
2. 包含关于**场景构成技术**的查询，如布局、定位和分组
3. 如果场景计划建议使用插件功能，请包含针对这些插件视觉功能的特定查询
4. 查询应该是高层次的，旨在发现可以使用哪些Manim功能，而不是专注于低层次的实现细节。
    - 例如，与其问"如何设置圆的颜色"，不如问"在Manim中我可以控制形状的哪些视觉属性？"

上述场景计划与这些插件相关：{relevant_plugins}。
请注意，你绝对不能使用上面未列出的插件。

你必须仅以以下JSON格式输出查询（使用json三重反引号）：
```json
[
    {{"type": "manim-core", "query": "content of visual element query"}},
    {{"type": "<plugin-name>", "query": "content of plugin-specific query"}},
    {{"type": "manim-core", "query": "content of composition technique query"}}
    ...
]
```