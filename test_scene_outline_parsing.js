const { parseSceneOutline } = require('./generatorVideo/utils/parseText.js');

// 测试场景大纲解析功能
function testSceneOutlineParsing() {
  console.log('=== 测试场景大纲解析功能 ===\n');

  // 测试用例1：标准格式
  const standardFormat = `
<SCENE_1>
**场景标题**：勾股定理的发现
**核心内容**：介绍勾股定理的基本概念，展示直角三角形的三边关系
**教学目标**：让观众理解勾股定理的基本表述和几何意义
**视觉元素**：直角三角形、边长标注、公式文本、坐标系
**动画流程**：先显示坐标系，然后绘制直角三角形，标注边长，最后显示公式
</SCENE_1>

<SCENE_2>
**场景标题**：勾股定理的证明
**核心内容**：通过几何证明方法来证明勾股定理
**教学目标**：让观众理解勾股定理的数学严谨性
**视觉元素**：大正方形、小正方形、面积标注
**动画流程**：构建大正方形，展示面积分解过程
</SCENE_2>

<SCENE_3>
**场景标题**：勾股定理的应用
**核心内容**：展示勾股定理在实际生活中的应用
**教学目标**：让观众掌握如何在实际问题中应用勾股定理
**视觉元素**：实际场景图、测量工具、计算过程
**动画流程**：展示实际场景的数学建模过程
</SCENE_3>
  `;

  // 测试用例2：备用格式
  const alternativeFormat = `
**场景一：问题理解**
**核心内容**：展示蜡烛燃烧问题，分析已知条件
**教学目标**：理解题目中的数量关系
**视觉元素**：两支蜡烛、时间轴、长度标注
**动画流程**：显示题目，绘制蜡烛，标注数据

**场景二：建立方程**
**核心内容**：根据题目条件建立数学方程
**教学目标**：培养数学建模能力
**视觉元素**：方程式、变量定义、推导过程
**动画流程**：逐步建立方程

**场景三：求解过程**
**核心内容**：解方程得出答案
**教学目标**：掌握解题方法
**视觉元素**：计算步骤、最终答案
**动画流程**：逐步计算求解

**整体要求**：
- 确保三个场景形成完整的教学体系
  `;

  // 测试用例3：空输入
  const emptyInput = '';

  // 测试用例4：无效格式
  const invalidFormat = '这是一段没有任何标签的普通文本内容';

  const testCases = [
    { name: '标准<SCENE_X>格式', input: standardFormat },
    { name: '备用**场景X：**格式', input: alternativeFormat },
    { name: '空输入', input: emptyInput },
    { name: '无效格式', input: invalidFormat },
  ];

  testCases.forEach((testCase, index) => {
    console.log(`\n--- 测试用例 ${index + 1}: ${testCase.name} ---`);
    
    try {
      const result = parseSceneOutline(testCase.input);
      console.log(`解析结果类型: ${Array.isArray(result) ? 'Array' : typeof result}`);
      console.log(`场景数量: ${result ? result.length : 0}`);
      
      if (result && result.length > 0) {
        result.forEach((scene, i) => {
          console.log(`场景 ${i + 1} 内容长度: ${scene.length} 字符`);
          console.log(`场景 ${i + 1} 预览: ${scene.substring(0, 50)}...`);
        });
        console.log('✅ 解析成功');
      } else {
        console.log('⚠️  未解析到场景内容');
      }
    } catch (error) {
      console.log('❌ 解析出错:', error.message);
    }
  });

  console.log('\n=== 测试完成 ===');
}

// 运行测试
if (require.main === module) {
  testSceneOutlineParsing();
}

module.exports = {
  testSceneOutlineParsing,
};
