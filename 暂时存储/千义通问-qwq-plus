<SCENE_OUTLINE>
    <SCENE_1>
    场景标题: 直角三角形与勾股定理
    场景目的: 引入直角三角形的结构和勾股定理公式，建立直观的几何关联。
    场景描述: 动画从空白画布开始，绘制一个直角三角形（直角在左下角）。三条边分别用不同颜色标注：a（水平边）、b（垂直边）、c（斜边）。随后，每个边向外延伸生成对应的正方形（a²、b²、c²），正方形用半透明填充并标注面积公式。最后，a²和b²的正方形通过缩放动画合并为c²的正方形，同时公式a² + b² = c²从顶部淡入并居中显示。
    场景布局: 三角形位于画布中心。a²正方形在三角形下方，b²在右侧，c²在斜边末端。公式始终居中，随着动画推进逐渐显影。所有元素保持安全区域边界，间距适中。
    </SCENE_1>

    <SCENE_2>
    场景标题: 几何拼图证明
    场景目的: 通过图形重组展示面积恒等式，连接前一场景的直观概念到严谨证明。
    场景描述: 四个全等的直角三角形（与场景1同尺寸）以初始布局形成一个大正方形（边长a+b），内部包含一个小正方形（边长c）。动画逐步旋转并移动三角形，重组为另一种布局：四个三角形向内折叠，形成以c为边长的中心正方形，周围留出a²和b²的区域。两种布局的总面积对比动画显示，最终推导出等式。关键步骤用箭头和文字标注。
    场景布局: 左侧展示初始大正方形布局，右侧展示重组后的布局。中间用虚线分割，下方显示面积计算公式（(a+b)² = 4*(ab/2)+c² → a²+b²=c²）。三角形和正方形保持动态对齐，确保视觉连贯。
    </SCENE_2>

    <SCENE_3>
    场景标题: 代数验证与普遍性
    场景目的: 用代数方法验证定理，证明其在任意直角三角形中的普适性。
    场景描述: 从场景2的重组布局出发，代数公式逐步覆盖几何图形。变量a、b、c的值用滑块控件动态调整（例如a=3,b=4,c=5），实时计算并验证等式成立。接着展示不同比例的直角三角形（如a=5,b=12,c=13），通过缩放和旋转动画保持等式恒成立。最后，公式以三维坐标系形式展示，暗示其在空间几何中的扩展。
    场景布局: 左侧是动态直角三角形及滑块控件，右侧显示实时更新的代数计算。三维坐标系作为背景渐显，但保持次要地位。所有元素遵循场景1的安全区域和间距规范。
    </SCENE_3>
</SCENE_OUTLINE>




你是一位教育视频制作、教学设计和勾股定理领域的专家。请设计一个高质量的视频脚本，对勾股定理提供深入解释。

**视频概述：**

主题：勾股定理
描述：生成一个勾股定理证明动画

**场景细分：**

规划各个场景。对于每个场景，请提供以下内容：

* **场景标题：**简短、描述性的标题（2-5个词）。
* **场景目的：**此场景的学习目标。它如何与前面的场景相连接？
* **场景描述：**场景内容的详细描述，场景里有什么元素，元素的形象以及动画描述。
* **场景布局：**详细描述空间布局概念。重点各个元素在画布上的位置。

请按照以下格式生成视频的场景计划：

```xml
<SCENE_OUTLINE>
    <SCENE_1>
    场景标题: [场景标题]
    场景目的: [场景目的]
    场景描述: [场景描述]
    场景布局: [场景布局]
    </SCENE_1>

    <SCENE_2>
    ...
    </SCENE_2>
...
</SCENE_OUTLINE>
```

要求：

1. 场景必须逐步构建，从基础概念开始，逐渐发展到更复杂的想法，以确保观众理解的逻辑流程。每个场景应自然地从前一个场景延续，创建一个连贯的学习叙述。从简单的场景布局开始，在后续场景中逐渐增加复杂性。
2. 场景总数应在3到4个之间。
3. 学习目标应均匀分布在各个场景中。
4. 视频总时长必须在15分钟以内。
5. 必须使用提示中指定的确切输出格式、标签和标题。
6. 在整个场景计划中保持一致的格式。
7. **无外部资产：**不要导入任何外部文件（图像、音频、视频）。*仅使用Manim内置元素和程序生成。
8. 专注于对定理的深入解释。不要包含任何宣传元素（如YouTube频道推广、订阅信息或外部资源）或测验环节。
注意：这是高层次计划。详细的场景规格将在后期生成，确保遵守安全区域边距和最小间距。上面定义的空间约束将在后续规划阶段严格执行。



你是精通manim代码的动画制作专家，下面是一段脚本，请你用manim代码帮我实现脚本描述的动画。
以下是为场景2生成的详细脚本，严格遵循坐标系、安全边距及动画要求：
勾股定理场景2: 拼图证明演绎详细脚本
场景概述
通过几何拼图展示勾股定理(a²+b²=c²)的完整视觉证明。画面将呈现四个全同直角三角形的重组过程，展示其面积守恒特性，并同步推导数学公式。

基本参数
画面尺寸: 960×540像素
中心坐标: (0,0)
安全区域: 原点周围[-4.7, 4.7]×[-2.6, 2.6]范围
元素详细规格
1. 基础三角形模板
类型: 直角三角形
边长: a=2.0单位，b=1.5单位
初始顶点坐标:
A(0, 0)
B(a, 0) = (2.0, 0)
C(0, b) = (0, 1.5)
填充色: #3498DB (蓝色)，透明度0.7
描边: 白色，线宽2
2. 初始大正方形布局
中心坐标: (0, 0)
边长: a+b = 3.5单位
四个三角形顶点坐标:
三角形1: [(0,0), (2.0,0), (0,1.5)]
三角形2: [(0,0), (-2.0,0), (0,-1.5)]
三角形3: [(0,0), (0,-1.5), (-2.0,0)]
三角形4: [(0,0), (0,1.5), (2.0,0)]
中心正方形:
顶点: [(0,1.5), (2.0,0), (0,-1.5), (-2.0,0)]
填充色: #E74C3C (红色)，透明度0.7
描边: 白色，线宽2
3. 公式文本
初始公式位置: (-3.5, 2.0)
字体大小: 36pt
颜色: 白色
内容: "c² = ?"
4. 标签文本
边a标签: 位置(1.0, -0.2)，内容"a"，字体大小24pt，颜色白色
边b标签: 位置(-0.2, 0.75)，内容"b"，字体大小24pt，颜色白色
边c标签: 位置(1.0, 0.75)，内容"c"，字体大小24pt，颜色白色
详细动画时间轴
0:00-0:05 - 初始状态展示
在(0,0)创建四个全同直角三角形
将四个三角形排列成十字形，中心形成一个正方形
添加标签a, b, c标识三角形各边
在(-3.5, 2.0)添加公式"c² = ?"
0:05-0:10 - 面积计算初始状态
在(-3.5, 1.5)创建文本"大正方形面积 = (a+b)²"
在(-3.5, 1.0)创建文本"= 中心小正方形 + 4个三角形"
在(-3.5, 0.5)创建文本"= c² + 4·(½ab)"
在(-3.5, 0.0)创建文本"= c² + 2ab"
0:10-0:20 - 三角形重组动画
三角形1从[(0,0), (2.0,0), (0,1.5)]旋转并移动到[(3.0,0), (3.0,2.0), (5.0,0)]
三角形2从[(0,0), (-2.0,0), (0,-1.5)]旋转并移动到[(3.0,0), (3.0,-2.0), (5.0,0)]
三角形3从[(0,0), (0,-1.5), (-2.0,0)]旋转并移动到[(-3.0,0), (-3.0,-2.0), (-5.0,0)]
三角形4从[(0,0), (0,1.5), (2.0,0)]旋转并移动到[(-3.0,0), (-3.0,2.0), (-5.0,0)]
动画持续时间2秒，使用SMOOTH缓动函数
0:20-0:30 - 形成新正方形
在(-3.0, 0)创建一个边长为a的正方形，填充色#27AE60 (绿色)，透明度0.7
顶点: [(-3.0,0), (-3.0,2.0), (-5.0,2.0), (-5.0,0)]
在(3.0, 0)创建一个边长为b的正方形，填充色#F39C12 (橙色)，透明度0.7
顶点: [(3.0,0), (3.0,-2.0), (5.0,-2.0), (5.0,0)]
添加标签"a²"在(-4.0, 1.0)
添加标签"b²"在(4.0, -1.0)
0:30-0:40 - 公式推导完成
在(-3.5, -1.0)创建文本"新排列面积 = a² + b² + 4个三角形"
在(-3.5, -1.5)创建文本"= a² + b² + 2ab"
创建等式箭头从(0,0)指向(0,-2.0)
在(-3.5, -2.0)创建高亮文本"∴ c² + 2ab = a² + b² + 2ab"
在(-3.5, -2.5)创建结论文本"∴ c² = a² + b²"
0:40-0:45 - 最终高亮
将结论文本"c² = a² + b²"从(-3.5, -2.5)移动到中心位置(0, -2.0)
放大结论文本至1.5倍原始大小
添加黄色闪光效果，持续1秒
添加圆形边框，半径0.8单位，从中心向外扩展，持续0.5秒
0:45-0:50 - 场景结束
淡出所有元素，持续1秒
等待0.5秒
特殊动画说明
三角形旋转中使用RotateAboutPoint动画，每个三角形绕不同点旋转不同角度
文本呈现使用Write动画，每个文本持续0.8秒完成
在面积计算公式推导时，使用Indicate动画高亮相应几何部分
最终结论使用FadeInFromLarge动画配合黄色闪光效果吸引注意
以上脚本遵循所有指定空间约束，所有元素均位于安全区域内。所有位置均使用精确坐标表示，避免模糊描述。




你是精通manim代码的动画制作专家，下面是一段场景概述，你帮我为场景2生成一个更详细的脚本文案，精准到每个时间点的每个元素的大小、颜色、位置、交互、动画。可以把整个画布当作一个坐标系，把每个元素的顶点坐标都清楚的设计出来，还有动画的效果也要清楚的用精准的数字描述出来，将你生成的脚本直接给manim开发工程师，manim开发工程师能够根据脚本直接写出准确的动画代码。关于每个元素的位置和动画信息，不要用文字描述，一切都以它的顶点坐标描述位置和动作，不能用稍微、左侧、右侧等词语。
画面尺寸: 960*540 像素
坐标系: 原点(0,0)位于画面中心，x轴向右为正，y轴向上为正
单位比例: 1个单位 = 100像素

**空间约束（严格执行）：**
* **安全区域边距：**场景边缘所有侧面0.1个单位。*所有对象必须定位在这些边距内。*

**定位要求：**
1. 安全区域边距（0.1个单位）。
2. 子场景和动画步骤之间的过渡缓冲（`Wait`时间），以提高视觉清晰度和节奏感。

**图表/草图（复杂场景可选但推荐）：**
* 对于复杂场景，考虑包含一个简单的图表或草图（甚至是基于文本的）来展示预期的布局，以直观地澄清空间关系，并确保遵守间距和边距约束。

**重点：**
* 通过有效使用Manim对象和动画，同时严格遵守定义的空间约束，专注于清晰地视觉传达场景的学习目标。
* 以Manim术语提供详细的视觉描述，描述如何实现场景计划以及每个元素的位置和动画效果和元素之间的关系。
* 优先解释和可视化定理。不包括任何宣传元素或测验环节。
* 最小化文本使用 - 主要依靠视觉元素、数学符号和动画来传达概念。仅在必要时为清晰起见谨慎使用文本。
* 不要生成python代码，要的是更详细的脚本描述
主题：勾股定理
主题描述：证明勾股定理的动画
**以下是脚本内容：**
场景标题: 边长关系探究
    场景目的: 开始探究直角三角形三边的数量关系，为引出勾股定理做铺垫，承接上一场景对直角三角形的介绍。
    场景描述: 在刚才的直角三角形基础上，动画展示分别以三条边为边长向外作正方形，三个正方形的颜色分别与对应边的颜色一致。然后展示在三个正方形内用小方格填充（每个小方格边长设为一个单位长度），通过数小方格的数量来大致比较三个正方形面积的关系，引导观众观察发现两个直角边对应的正方形面积之和与斜边对应的正方形面积的关系。
    场景布局: 三个正方形分别紧密挨着直角三角形的三条边向外扩展绘制，小方格整齐排列在正方形内，整个图形依然保持在画面中心附近。