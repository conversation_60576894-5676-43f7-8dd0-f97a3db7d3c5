const { v4: uuidv4 } = require('uuid'); // 使用 CommonJS 导入方式
const VideoGenerator = require('./index.js');
const {
  parseSceneOutline,
  parseStoryboard,
  parseTLContent,
  parseAnimationContent,
} = require('./utils/parseText.js');
const videoGenerator = new VideoGenerator({
  useRag: false,
});
const useGenratorVideoApi = (app, db) => {
  const prefix = '/frontend-chat';
  const parseStoryKey = (sceneNum) => `story_${sceneNum}`;
  const parseTLKey = (sceneNum) => `tl_${sceneNum}`;
  const parseAnimationKey = (sceneNum) => `animation_${sceneNum}`;
  // 根据topic和description找到sessionId
  const findSessionId = async (topic, description) => {
    console.log('🔍 数据库查询 - 查找缓存:', { topic, description });
    const result = await db.query(
      'SELECT session_id FROM topic WHERE topic =? AND description =?',
      [topic, description]
    );
    console.log(
      '🔍 数据库查询结果:',
      result.length > 0
        ? `找到sessionId: ${result[0].session_id}`
        : '未找到缓存'
    );
    return result.length > 0 ? result[0].session_id : null;
  };
  // 查询场景大纲
  const querySceneOutline = async (sessionId, parentSceneNum = 0) => {
    const result = await db.query(
      'SELECT content, scene_num FROM topic_scene WHERE session_id =? AND parent_scene_num =?',
      [sessionId, String(parentSceneNum)]
    );
    console.log('querySceneOutline', result, sessionId, String(parentSceneNum));

    return result;
  };
  // 根据sceneNum查询单个场景大纲
  const querySceneOutlineBySceneNum = async (sessionId, sceneNum) => {
    const result = await db.query(
      'SELECT content, scene_num FROM topic_scene WHERE session_id =? AND scene_num =?',
      [sessionId, sceneNum]
    );
    return result;
  };
  const queryAllSceneOutline = async (sessionId) => {
    const allScenes = await db.query(
      'SELECT content, scene_num FROM topic_scene WHERE session_id = ? AND parent_scene_num = "0" ORDER BY scene_num',
      [sessionId]
    );
    const fullSceneOutline = allScenes
      .map((scene) => scene.content)
      .join('\n\n');
    return fullSceneOutline;
  };

  // AI交互日志记录函数
  const logAIInteraction = async (logData) => {
    try {
      await db.query(
        `
        INSERT INTO ai_interaction_log (
          session_id, step_type, scene_num, input_prompt, input_params,
          ai_raw_response, ai_response_tokens, ai_model, parsed_data,
          parsing_success, parsing_error, storage_success, storage_error,
          request_time, response_time, duration_ms, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
        [
          logData.sessionId,
          logData.stepType,
          logData.sceneNum,
          logData.inputPrompt,
          JSON.stringify(logData.inputParams),
          logData.aiRawResponse,
          logData.aiResponseTokens,
          logData.aiModel,
          JSON.stringify(logData.parsedData),
          logData.parsingSuccess,
          logData.parsingError,
          logData.storageSuccess,
          logData.storageError,
          logData.requestTime,
          logData.responseTime,
          logData.durationMs,
          logData.status,
        ]
      );
    } catch (error) {
      console.error('❌ AI交互日志记录失败:', error);
    }
  };

  // 生成场景大纲接口
  app.post(`${prefix}/generateSceneOutline`, async (req, res) => {
    const { topic, description } = req.body;
    console.log('🔍 缓存查询开始 - topic:', topic, 'description:', description);

    // 先根据topic和description找到sessionId
    const sessionId = await findSessionId(topic, description);
    console.log('🔍 缓存查询结果 - sessionId:', sessionId);

    if (sessionId) {
      // 如果存在，直接查询当前场景大纲返回
      const sceneOutLine = await querySceneOutline(sessionId); // 添加await
      console.log(
        '🎯 缓存命中 - sessionId:',
        sessionId,
        '场景数量:',
        sceneOutLine.length
      );
      if (sceneOutLine.length > 0) {
        const sceneList = sceneOutLine.map((item) => {
          return {
            content: item.content,
            sceneNum: item.scene_num,
            parentSceneNum: 0,
          };
        });
        return res.status(200).json({
          code: 0,
          message: 'success',
          data: {
            sceneList,
            sessionId,
          },
        });
      }
    }
    // 如果不存在，生成场景大纲
    console.log('❌ 缓存未命中，开始生成新的场景大纲');

    const requestTime = new Date();
    const inputParams = { topic, description };
    let aiRawResponse = null;
    let parsedData = null;
    let parsingSuccess = false;
    let parsingError = null;
    let storageSuccess = false;
    let storageError = null;
    let newSessionId = null;

    try {
      // 调用AI生成场景大纲
      const sceneOutLine = await videoGenerator.generateSceneOutline({
        topic,
        description,
      });

      aiRawResponse = sceneOutLine;
      console.log('🤖 AI原始返回长度:', aiRawResponse?.length || 0);

      // 解析场景大纲
      const sceneStrList = parseSceneOutline(sceneOutLine);
      console.log('📝 解析结果:', sceneStrList);

      if (!sceneStrList || sceneStrList.length === 0) {
        parsingError = '场景大纲解析失败，返回空数组';
        console.error('❌ 场景大纲解析失败，原始内容:', sceneOutLine);

        // 记录失败的AI交互
        await logAIInteraction({
          sessionId: 'unknown',
          stepType: 'scene_outline',
          sceneNum: null,
          inputPrompt: `生成场景大纲 - Topic: ${topic}, Description: ${description}`,
          inputParams,
          aiRawResponse,
          aiResponseTokens: aiRawResponse?.length || 0,
          aiModel: 'unknown',
          parsedData: null,
          parsingSuccess: false,
          parsingError,
          storageSuccess: false,
          storageError: null,
          requestTime,
          responseTime: new Date(),
          durationMs: Date.now() - requestTime.getTime(),
          status: 'failed',
        });

        return res.status(500).json({
          code: 1,
          message: '场景大纲生成失败，请重试',
          data: null,
        });
      }

      parsedData = sceneStrList;
      parsingSuccess = true;

      // 插入数据库
      newSessionId = uuidv4();
      try {
        await db.query(
          'INSERT INTO topic (session_id, topic, description) VALUES (?, ?, ?)',
          [newSessionId, topic, description]
        );

        for (let i = 0; i < sceneStrList.length; i++) {
          const num = i + 1;
          const content = sceneStrList[i];
          await db.query(
            'INSERT INTO topic_scene (session_id, content, scene_num, parent_scene_num) VALUES (?, ?, ?, ?)',
            [newSessionId, content, String(num), '0']
          );
        }

        storageSuccess = true;
        console.log('✅ 场景大纲存储成功 - sessionId:', newSessionId);
      } catch (error) {
        storageError = error.message;
        storageSuccess = false;
        console.error('❌ 场景大纲存储失败:', error);
      }
    } catch (error) {
      console.error('❌ 场景大纲生成过程出错:', error);
      parsingError = error.message;
    } finally {
      // 记录完整的AI交互日志
      await logAIInteraction({
        sessionId: newSessionId || 'unknown',
        stepType: 'scene_outline',
        sceneNum: null,
        inputPrompt: `生成场景大纲 - Topic: ${topic}, Description: ${description}`,
        inputParams,
        aiRawResponse,
        aiResponseTokens: aiRawResponse?.length || 0,
        aiModel: 'unknown',
        parsedData,
        parsingSuccess,
        parsingError,
        storageSuccess,
        storageError,
        requestTime,
        responseTime: new Date(),
        durationMs: Date.now() - requestTime.getTime(),
        status: storageSuccess ? 'success' : 'failed',
      });
    }

    // 返回结果
    if (storageSuccess && newSessionId) {
      const sceneList = parsedData.map((item, index) => {
        return {
          content: item,
          sceneNum: index + 1,
          parentSceneNum: 0,
        };
      });

      return res.status(200).json({
        code: 0,
        message: 'success',
        data: {
          sceneList,
          sessionId: newSessionId,
        },
      });
    } else {
      return res.status(500).json({
        code: 1,
        message: '场景大纲生成失败，请重试',
        data: null,
      });
    }
  });

  // 调试接口：查看AI交互日志
  app.get(`${prefix}/debug/ai-logs/:sessionId`, async (req, res) => {
    try {
      const { sessionId } = req.params;

      // 获取AI交互日志
      const aiLogs = await db.query(
        'SELECT * FROM ai_interaction_log WHERE session_id = ? ORDER BY request_time',
        [sessionId]
      );

      // 获取会话基本信息
      const sessionInfo = await db.query(
        'SELECT * FROM topic WHERE session_id = ?',
        [sessionId]
      );

      // 获取场景数据
      const sceneData = await db.query(
        'SELECT * FROM topic_scene WHERE session_id = ? ORDER BY scene_num',
        [sessionId]
      );

      return res.status(200).json({
        code: 0,
        message: 'success',
        data: {
          sessionInfo: sessionInfo[0] || null,
          aiLogs,
          sceneData,
          summary: {
            totalSteps: aiLogs.length,
            successfulSteps: aiLogs.filter((log) => log.status === 'success')
              .length,
            failedSteps: aiLogs.filter((log) => log.status === 'failed').length,
            sceneCount: sceneData.length,
          },
        },
      });
    } catch (error) {
      console.error('获取AI交互日志失败:', error);
      return res.status(500).json({
        code: 1,
        message: '获取调试信息失败',
        data: null,
      });
    }
  });

  // 生成故事面板
  app.post(`${prefix}/generateStoryboard`, async (req, res) => {
    const { sessionId, sceneNum, topic, description } = req.body;
    if (!sessionId || !sceneNum || !topic || !description) {
      return res.status(400).json({
        code: 1,
        message: 'params error',
        data: null,
      });
    }
    console.log(
      '🔍 故事板缓存查询 - sessionId:',
      sessionId,
      'sceneNum:',
      sceneNum
    );
    const storyboardCache = await querySceneOutlineBySceneNum(
      sessionId,
      parseStoryKey(sceneNum)
    );
    console.log(
      '🔍 故事板缓存结果:',
      storyboardCache.length > 0 ? '找到缓存' : '未找到缓存'
    );
    if (storyboardCache.length > 0) {
      console.log('🎯 故事板缓存命中 - 直接返回缓存结果');
      const storyboard = parseStoryboard(storyboardCache[0].content);
      return res.status(200).json({
        code: 0,
        message: 'success',
        data: {
          storyboard,
        },
      });
    }
    // 根据sessionId和sceneNum查询场景大纲
    const sceneOutline = await querySceneOutlineBySceneNum(sessionId, sceneNum);
    if (sceneOutline.length === 0) {
      return res.status(404).json({
        code: 1,
        message: 'scene not found',
        data: null,
      });
    }
    const sceneContent = sceneOutline[0].content;

    // 查询所有场景大纲以构建完整的场景大纲
    const fullSceneOutline = await queryAllSceneOutline(sessionId);
    const storyboardStr = await videoGenerator.generateStoryboard({
      topic,
      description,
      sceneNum,
      sceneContent,
      sceneOutline: fullSceneOutline,
    });
    await db.query(
      'INSERT INTO topic_scene (session_id, content, scene_num, parent_scene_num) VALUES (?, ?, ?, ?)',
      [sessionId, storyboardStr, parseStoryKey(sceneNum), String(sceneNum)]
    );
    const storyboard = parseStoryboard(storyboardStr);
    console.log('storyboard', storyboard);
    return res.status(200).json({
      code: 0,
      message: 'success',
      data: {
        storyboard,
      },
    });
  });
  // // 生成技术实现
  // app.post(`${prefix}/generateTechnicalImplementation`, async (req, res) => {
  //   const { sessionId, sceneNum, topic, description } = req.body;
  //   if (!sessionId || !sceneNum || !topic || !description) {
  //     return res.status(400).json({
  //       code: 1,
  //       message: 'params error',
  //       data: null,
  //     });
  //   }
  //   const tlCache = await querySceneOutlineBySceneNum(
  //     sessionId,
  //     parseTLKey(sceneNum)
  //   );
  //   if (tlCache.length > 0) {
  //     const technicalImplementation = parseTLContent(tlCache[0].content);
  //     return res.status(200).json({
  //       code: 0,
  //       message: 'success',
  //       data: {
  //         technicalImplementation,
  //       },
  //     });
  //   }
  //   // 根据sessionId和sceneNum查询场景大纲
  //   const sceneOutline = await querySceneOutlineBySceneNum(sessionId, sceneNum);
  //   const storyboard = await querySceneOutlineBySceneNum(
  //     sessionId,
  //     parseStoryKey(sceneNum)
  //   );

  //   if (sceneOutline.length === 0 || storyboard.length === 0) {
  //     return res.status(404).json({
  //       code: 1,
  //       message: 'scene not found',
  //       data: null,
  //     });
  //   }
  //   const sceneContent = sceneOutline[0].content;
  //   const storyContent = storyboard[0].content;
  //   const tlStr = await videoGenerator.generateTechnicalImplementationSingle({
  //     topic,
  //     description,
  //     sceneNum,
  //     sceneContent,
  //     storyContent,
  //   });
  //   await db.query(
  //     'INSERT INTO topic_scene (session_id, content, scene_num, parent_scene_num) VALUES (?, ?, ?, ?)',
  //     [sessionId, tlStr, parseTLKey(sceneNum), parseStoryKey(sceneNum)]
  //   );
  //   const technicalImplementation = parseTLContent(tlStr);
  //   console.log('technicalImplementation', technicalImplementation);
  //   return res.status(200).json({
  //     code: 0,
  //     message: 'success',
  //     data: {
  //       technicalImplementation,
  //     },
  //   });
  // });
  // 生成动画和旁白
  app.post(`${prefix}/generateAnimationNarration`, async (req, res) => {
    const { sessionId, sceneNum, topic, description } = req.body;
    if (!sessionId || !sceneNum || !topic || !description) {
      return res.status(400).json({
        code: 1,
        message: 'params error',
        data: null,
      });
    }
    const animationCache = await querySceneOutlineBySceneNum(
      sessionId,
      parseAnimationKey(sceneNum)
    );
    if (animationCache.length > 0) {
      const animationNarration = parseAnimationContent(
        animationCache[0].content
      );
      return res.status(200).json({
        code: 0,
        message: 'success',
        data: {
          animationNarration,
        },
      });
    }
    // 根据sessionId和sceneNum查询场景大纲
    const sceneOutline = await querySceneOutlineBySceneNum(sessionId, sceneNum);
    const storyboard = await querySceneOutlineBySceneNum(
      sessionId,
      parseStoryKey(sceneNum)
    );

    const fullSceneOutline = await queryAllSceneOutline();
    // const technicalImplementation = await querySceneOutlineBySceneNum(
    //   sessionId,
    //   parseTLKey(sceneNum)
    // );

    if (
      sceneOutline.length === 0 ||
      storyboard.length === 0
      // || technicalImplementation.length === 0
    ) {
      return res.status(404).json({
        code: 1,
        message: 'scene not found',
        data: null,
      });
    }
    const sceneContent = sceneOutline[0].content;
    const storyContent = storyboard[0].content;
    // const technicalContent = technicalImplementation[0].content;
    const animationStr = await videoGenerator.generateAnimationNarrationSingle({
      topic,
      description,
      sceneNum,
      sceneContent,
      storyContent,
      sceneOutline: fullSceneOutline,
      // technicalContent,
    });
    await db.query(
      'INSERT INTO topic_scene (session_id, content, scene_num, parent_scene_num) VALUES (?, ?, ?, ?)',
      [
        sessionId,
        animationStr,
        parseAnimationKey(sceneNum),
        parseStoryKey(sceneNum),
      ]
    );
    const animationNarration = parseAnimationContent(animationStr);
    console.log('animationNarration', animationNarration);
    return res.status(200).json({
      code: 0,
      message: 'success',
      data: {
        animationNarration,
      },
    });
  });

  // 生成视频

  app.post(`${prefix}/generateVideo`, async (req, res) => {
    const { sessionId, sceneNum, topic, description } = req.body;
    if (!sessionId || !sceneNum || !topic || !description) {
      return res.status(400).json({
        code: 1,
        message: 'params error',
        data: null,
      });
    }

    // 根据sessionId和sceneNum查询场景大纲
    const sceneOutline = await querySceneOutlineBySceneNum(sessionId, sceneNum);
    const storyboard = await querySceneOutlineBySceneNum(
      sessionId,
      parseStoryKey(sceneNum)
    );
    // const technicalImplementation = await querySceneOutlineBySceneNum(
    //   sessionId,
    //   parseTLKey(sceneNum)
    // );
    const animationNarration = await querySceneOutlineBySceneNum(
      sessionId,
      parseAnimationKey(sceneNum)
    );
    const fullSceneOutline = await queryAllSceneOutline();

    if (
      sceneOutline.length === 0 ||
      storyboard.length === 0 ||
      // technicalImplementation.length === 0 ||
      animationNarration.length === 0
    ) {
      return res.status(404).json({
        code: 1,
        message: 'scene not found',
        data: null,
      });
    }
    const sceneContent = sceneOutline[0].content;
    const storyContent = storyboard[0].content;
    // const technicalContent = technicalImplementation[0].content;
    const animationContent = animationNarration[0].content;
    const sceneImplementation = storyContent + '\n\n' + animationContent;
    try {
      const result = await videoGenerator.processScene({
        sceneNum,
        sceneContent: sceneContent,
        sceneOutline: fullSceneOutline,
        sceneImplementation,
        topic,
        description,
        maxRetries: 3,
        sessionId,
      });

      if (!result.success) {
        // 检查是否是API限流错误
        if (result.error && result.error.includes('429')) {
          return res.status(429).json({
            code: 429,
            message: 'API调用频率超限，请稍后重试',
            data: null,
          });
        }

        return res.status(500).json({
          code: 1,
          message: `视频生成失败: ${result.error}`,
          data: null,
        });
      }

      return res.status(200).json({
        code: 0,
        message: 'success',
        data: {
          // animationNarration,
        },
      });
    } catch (error) {
      console.error('视频生成过程中发生未捕获的错误:', error);

      // 检查是否是API限流错误
      if (error.message && error.message.includes('429')) {
        return res.status(429).json({
          code: 429,
          message: 'API调用频率超限，请稍后重试',
          data: null,
        });
      }

      return res.status(500).json({
        code: 1,
        message: `视频生成过程中发生错误: ${error.message}`,
        data: null,
      });
    }
  });
  // 视频合成
  app.post(`${prefix}/generateVideoMerge`, async (req, res) => {
    const { sessionId } = req.body;
    console.log('sessionId', sessionId);
    if (!sessionId) {
      return res.status(400).json({
        code: 1,
        message: 'params error',
        data: null,
      });
    }
    const sceneOutline = await querySceneOutline(sessionId);
    const sceneCount = sceneOutline.length;
    const videoMerge = await videoGenerator.generateVideoMerge({
      sessionId,
      sceneCount,
    });
    if (!videoMerge) {
      return res.status(400).json({
        code: 1,
        message: 'video merge error',
        data: null,
      });
    }
    const uploadInfo = await videoGenerator.uploadAllToS3(sessionId);
    return res.status(200).json({
      code: 0,
      message: 'success',
      data: uploadInfo,
    });
  });
};
module.exports = useGenratorVideoApi;
