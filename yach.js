const crypto = require("crypto");
const axios = require("axios");

const yachConfig = {
  url: "https://yach-oapi.zhiyinlou.com/robot/send",
  token: "K1NvWmZDZHgxeU8wUmtrSGpUeFRsYjVXMVhuMTJOWXRGdmxqcW9NbDlDbGVxcFZrNmNOeVNaNjN5YnVzSWZZVA",
  secret: "SEC88676ab3233024a00b19a96df40f2907",
};

/**
 * 生成签名
 * @param {*} timestamp
 * @returns
 */
function generateSign(timestamp) {
  console.log("timestamp", yachConfig.secret);
  let str = `${timestamp}\n${yachConfig.secret}`;
  let hash = crypto
    .createHmac("SHA256", yachConfig.secret)
    .update(str, "utf8")
    .digest("base64");

  hash = encodeURIComponent(hash);
  return hash;
}

/**
 * 发送群消息
 * @param {*} data
 * @returns
 */
const sendData = async (data) => {
  try {
    let timestamp = +new Date();
    let sign = generateSign(timestamp);
    console.log(yachConfig.url);
    console.log(yachConfig.token);
    let url = `${yachConfig.url}?access_token=${yachConfig.token}&timestamp=${timestamp}&sign=${sign}`;
    let result = await axios.post(url, data);
    console.log(11112, result.data);
    return result.data;
  } catch (error) {
    console.error("发送群消息失败", error);
  }
}

module.exports = {
  sendData
}