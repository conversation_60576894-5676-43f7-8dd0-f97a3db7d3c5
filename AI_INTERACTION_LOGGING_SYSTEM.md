# AI交互日志系统设计与实现

## 🎯 问题背景

用户发现缓存机制虽然能找到sessionId，但对应的场景数据为空，怀疑是：
1. **AI返回数据为空或格式不正确**
2. **数据提取/解析逻辑有问题**
3. **缺乏AI交互的完整记录，无法调试和优化**

## 💡 解决方案

设计并实现了完整的AI交互日志系统，记录每一步的：
- 发送给AI的提示词
- AI的原始返回值
- 数据解析结果
- 存储成功/失败状态
- 详细的错误信息

## 🗄️ 数据库设计

### **新增表：`ai_interaction_log`**

```sql
CREATE TABLE ai_interaction_log (
  id INT AUTO_INCREMENT PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  step_type VARCHAR(50) NOT NULL,        -- 步骤类型：scene_outline, storyboard, animation
  scene_num VARCHAR(10) DEFAULT NULL,    -- 场景编号
  
  -- 输入数据
  input_prompt TEXT NOT NULL,            -- 发送给AI的完整提示词
  input_params JSON DEFAULT NULL,        -- 输入参数
  
  -- AI返回数据
  ai_raw_response LONGTEXT DEFAULT NULL, -- AI的原始完整返回
  ai_response_tokens INT DEFAULT NULL,   -- 返回的token数量
  ai_model VARCHAR(100) DEFAULT NULL,    -- 使用的AI模型
  
  -- 解析结果
  parsed_data JSON DEFAULT NULL,         -- 解析后的结构化数据
  parsing_success BOOLEAN DEFAULT FALSE, -- 解析是否成功
  parsing_error TEXT DEFAULT NULL,       -- 解析错误信息
  
  -- 存储结果
  storage_success BOOLEAN DEFAULT FALSE, -- 数据库存储是否成功
  storage_error TEXT DEFAULT NULL,       -- 存储错误信息
  
  -- 时间和状态
  request_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  response_time TIMESTAMP DEFAULT NULL,
  duration_ms INT DEFAULT NULL,
  status ENUM('pending', 'success', 'failed', 'partial') DEFAULT 'pending'
);
```

### **辅助视图和存储过程**

- **`v_session_ai_steps`**: 会话AI步骤概览视图
- **`GetSessionDebugInfo`**: 调试信息查询存储过程

## 🔧 核心功能实现

### **1. AI交互日志记录函数**

```javascript
const logAIInteraction = async (logData) => {
  try {
    await db.query(`
      INSERT INTO ai_interaction_log (
        session_id, step_type, scene_num, input_prompt, input_params,
        ai_raw_response, ai_response_tokens, ai_model, parsed_data,
        parsing_success, parsing_error, storage_success, storage_error,
        request_time, response_time, duration_ms, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [/* 参数数组 */]);
  } catch (error) {
    console.error('❌ AI交互日志记录失败:', error);
  }
};
```

### **2. 增强的场景大纲生成逻辑**

```javascript
app.post('/generateSceneOutline', async (req, res) => {
  const requestTime = new Date();
  let aiRawResponse = null;
  let parsedData = null;
  let parsingSuccess = false;
  let storageSuccess = false;
  
  try {
    // 1. 调用AI生成
    const sceneOutLine = await videoGenerator.generateSceneOutline({topic, description});
    aiRawResponse = sceneOutLine;
    
    // 2. 解析数据
    const sceneStrList = parseSceneOutline(sceneOutLine);
    if (!sceneStrList || sceneStrList.length === 0) {
      parsingError = '场景大纲解析失败，返回空数组';
      // 记录失败日志...
      return res.status(500).json({...});
    }
    parsedData = sceneStrList;
    parsingSuccess = true;
    
    // 3. 存储数据
    const newSessionId = uuidv4();
    await db.query('INSERT INTO topic...');
    for (let scene of sceneStrList) {
      await db.query('INSERT INTO topic_scene...');
    }
    storageSuccess = true;
    
  } catch (error) {
    parsingError = error.message;
  } finally {
    // 4. 记录完整的AI交互日志
    await logAIInteraction({
      sessionId: newSessionId || 'unknown',
      stepType: 'scene_outline',
      inputPrompt: `生成场景大纲 - Topic: ${topic}`,
      aiRawResponse,
      parsedData,
      parsingSuccess,
      storageSuccess,
      // ... 其他字段
    });
  }
});
```

### **3. 调试查询接口**

```javascript
app.get('/debug/ai-logs/:sessionId', async (req, res) => {
  const { sessionId } = req.params;
  
  // 获取AI交互日志
  const aiLogs = await db.query('SELECT * FROM ai_interaction_log WHERE session_id = ?', [sessionId]);
  
  // 获取会话和场景数据
  const sessionInfo = await db.query('SELECT * FROM topic WHERE session_id = ?', [sessionId]);
  const sceneData = await db.query('SELECT * FROM topic_scene WHERE session_id = ?', [sessionId]);
  
  return res.json({
    sessionInfo,
    aiLogs,
    sceneData,
    summary: {
      totalSteps: aiLogs.length,
      successfulSteps: aiLogs.filter(log => log.status === 'success').length,
      failedSteps: aiLogs.filter(log => log.status === 'failed').length,
      sceneCount: sceneData.length
    }
  });
});
```

## 🚀 使用方法

### **1. 创建数据库表**
```bash
mysql -u root -p think_chat < db/create_ai_interaction_log_table.sql
```

### **2. 重启服务**
```bash
node index.js
```

### **3. 测试场景大纲生成**
发起场景大纲生成请求，系统会自动记录完整的AI交互过程。

### **4. 查看调试信息**
```bash
curl http://localhost:3000/frontend-chat/debug/ai-logs/{sessionId}
```

## 📊 调试信息包含

### **AI交互日志**
- ✅ 完整的提示词
- ✅ AI的原始返回值
- ✅ 解析后的结构化数据
- ✅ 解析成功/失败状态
- ✅ 存储成功/失败状态
- ✅ 详细的错误信息
- ✅ 处理耗时统计

### **会话概览**
- 总步骤数
- 成功步骤数
- 失败步骤数
- 场景数据数量

## 🔍 问题排查流程

1. **获取sessionId** - 从前端或日志中获取
2. **查看AI交互日志** - 访问调试接口
3. **分析AI原始返回** - 检查AI是否返回了有效数据
4. **检查解析逻辑** - 验证数据提取是否正确
5. **确认存储状态** - 检查数据库操作是否成功

## 🎯 优化建议

基于AI交互日志，可以：
1. **优化提示词** - 分析AI返回质量，改进prompt
2. **改进解析逻辑** - 根据实际返回格式调整解析规则
3. **增强错误处理** - 针对常见错误添加重试机制
4. **性能优化** - 分析处理耗时，优化慢查询

## 🔧 后续扩展

- 为故事板生成、动画生成等其他步骤添加类似的日志记录
- 添加AI交互统计和分析功能
- 实现基于历史数据的提示词自动优化
- 添加AI交互的可视化界面

**现在每一步AI交互都有完整的记录，可以精确定位问题并持续优化！** 🎉
