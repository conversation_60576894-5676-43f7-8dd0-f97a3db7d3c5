const { parseSceneOutline } = require('./generatorVideo/utils/parseText.js');

// 测试修复后的解析功能
function testFixedParsing() {
  console.log('=== 测试修复后的场景大纲解析 ===\n');

  // 模拟AI返回的格式（带SCENE_OUTLINE外层标签）
  const aiResponse = `
<SCENE_OUTLINE>
    <SCENE_1>
    场景标题: 勾股定理的发现
    场景目的: 介绍勾股定理的基本概念，展示直角三角形的三边关系
    场景描述: 场景内容的详细描述，场景里有什么元素，元素的形象以及动画描述
    场景布局: 详细描述空间布局概念。重点各个元素在画布上的位置
    </SCENE_1>

    <SCENE_2>
    场景标题: 勾股定理的证明
    场景目的: 通过几何证明方法来证明勾股定理
    场景描述: 通过几何证明方法来证明勾股定理的详细过程
    场景布局: 大正方形、小正方形、面积标注的空间布局
    </SCENE_2>

    <SCENE_3>
    场景标题: 勾股定理的应用
    场景目的: 展示勾股定理在实际生活中的应用
    场景描述: 展示勾股定理在实际问题中的应用案例
    场景布局: 实际场景图、测量工具、计算过程的布局
    </SCENE_3>
</SCENE_OUTLINE>
  `;

  // 测试解析
  console.log('测试AI返回格式（带SCENE_OUTLINE外层标签）:');
  try {
    const result = parseSceneOutline(aiResponse);
    console.log(`解析结果类型: ${Array.isArray(result) ? 'Array' : typeof result}`);
    console.log(`场景数量: ${result ? result.length : 0}`);
    
    if (result && result.length > 0) {
      result.forEach((scene, i) => {
        console.log(`\n场景 ${i + 1}:`);
        console.log(`内容长度: ${scene.length} 字符`);
        console.log(`内容预览: ${scene.substring(0, 150)}...`);
      });
      console.log('\n✅ 解析成功！');
    } else {
      console.log('⚠️  解析失败，未找到场景内容');
    }
  } catch (error) {
    console.log('❌ 解析出错:', error.message);
  }

  console.log('\n=== 测试完成 ===');
}

// 运行测试
if (require.main === module) {
  testFixedParsing();
}

module.exports = {
  testFixedParsing,
};
