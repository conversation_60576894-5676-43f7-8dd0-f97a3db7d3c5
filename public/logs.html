<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Think-Chat 日志监控系统</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        background-color: #1e1e1e;
        color: #d4d4d4;
        line-height: 1.6;
      }

      .container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        background: #2d2d30;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        border-left: 4px solid #007acc;
      }

      .header h1 {
        color: #ffffff;
        margin-bottom: 10px;
      }

      .stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
      }

      .stat-card {
        background: #2d2d30;
        padding: 15px;
        border-radius: 6px;
        border-left: 3px solid #4caf50;
      }

      .stat-card.warning {
        border-left-color: #ff9800;
      }

      .stat-card.error {
        border-left-color: #f44336;
      }

      .controls {
        background: #2d2d30;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
      }

      .control-group {
        display: flex;
        gap: 15px;
        align-items: center;
        margin-bottom: 15px;
        flex-wrap: wrap;
      }

      .control-group:last-child {
        margin-bottom: 0;
      }

      label {
        color: #cccccc;
        font-weight: bold;
        min-width: 80px;
      }

      select,
      input,
      button {
        background: #3c3c3c;
        border: 1px solid #555;
        color: #d4d4d4;
        padding: 8px 12px;
        border-radius: 4px;
        font-family: inherit;
      }

      button {
        background: #007acc;
        border: none;
        color: white;
        cursor: pointer;
        transition: background-color 0.3s;
      }

      button:hover {
        background: #005a9e;
      }

      button.danger {
        background: #f44336;
      }

      button.danger:hover {
        background: #d32f2f;
      }

      .log-container {
        background: #1e1e1e;
        border: 1px solid #3c3c3c;
        border-radius: 8px;
        height: 600px;
        overflow-y: auto;
        padding: 15px;
        font-size: 13px;
      }

      .log-entry {
        margin-bottom: 10px;
        padding: 8px;
        border-left: 3px solid #007acc;
        background: #252526;
        border-radius: 4px;
      }

      .log-entry.error {
        border-left-color: #f44336;
        background: #2d1b1b;
      }

      .log-entry.warn {
        border-left-color: #ff9800;
        background: #2d2419;
      }

      .log-entry.debug {
        border-left-color: #9c27b0;
        background: #261b2d;
      }

      .log-entry.trace {
        border-left-color: #607d8b;
        background: #1e2326;
      }

      .log-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;
        font-weight: bold;
      }

      .log-timestamp {
        color: #888;
        font-size: 11px;
      }

      .log-level {
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 10px;
        font-weight: bold;
      }

      .log-level.ERROR {
        background: #f44336;
        color: white;
      }

      .log-level.WARN {
        background: #ff9800;
        color: white;
      }

      .log-level.INFO {
        background: #2196f3;
        color: white;
      }

      .log-level.DEBUG {
        background: #9c27b0;
        color: white;
      }

      .log-level.TRACE {
        background: #607d8b;
        color: white;
      }

      .log-message {
        color: #d4d4d4;
        margin-bottom: 5px;
      }

      .log-meta {
        background: #1e1e1e;
        padding: 8px;
        border-radius: 4px;
        font-size: 11px;
        color: #888;
        max-height: 200px;
        overflow-y: auto;
      }

      .search-container {
        margin-bottom: 20px;
      }

      .search-input {
        width: 100%;
        max-width: 400px;
        margin-right: 10px;
      }

      .status-indicator {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 8px;
      }

      .status-indicator.connected {
        background: #4caf50;
      }

      .status-indicator.disconnected {
        background: #f44336;
      }

      .tabs {
        display: flex;
        background: #2d2d30;
        border-radius: 8px 8px 0 0;
        overflow: hidden;
      }

      .tab {
        padding: 12px 20px;
        background: #2d2d30;
        border: none;
        color: #cccccc;
        cursor: pointer;
        transition: background-color 0.3s;
      }

      .tab.active {
        background: #007acc;
        color: white;
      }

      .tab-content {
        display: none;
        background: #1e1e1e;
        border: 1px solid #3c3c3c;
        border-radius: 0 0 8px 8px;
        padding: 20px;
      }

      .tab-content.active {
        display: block;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Think-Chat 日志监控系统</h1>
        <p>实时监控系统日志和性能指标</p>
        <div style="margin-top: 10px">
          <span class="status-indicator" id="connectionStatus"></span>
          <span id="connectionText">连接状态</span>
        </div>
      </div>

      <div class="stats" id="statsContainer">
        <!-- 统计信息将在这里动态加载 -->
      </div>

      <div class="tabs">
        <button class="tab active" onclick="switchTab('realtime')">
          实时日志
        </button>
        <button class="tab" onclick="switchTab('search')">搜索日志</button>
        <button class="tab" onclick="switchTab('files')">文件管理</button>
        <button class="tab" onclick="switchTab('monitor')">系统监控</button>
      </div>

      <!-- 实时日志选项卡 -->
      <div id="realtime" class="tab-content active">
        <div class="controls">
          <div class="control-group">
            <label>日志级别:</label>
            <select id="logLevel">
              <option value="all">全部</option>
              <option value="error">错误</option>
              <option value="warn">警告</option>
              <option value="info" selected>信息</option>
              <option value="debug">调试</option>
              <option value="trace">跟踪</option>
            </select>
            <button onclick="connectLogStream()">连接日志流</button>
            <button onclick="disconnectLogStream()">断开连接</button>
            <button onclick="clearLogs()">清空显示</button>
          </div>
        </div>
        <div class="log-container" id="logContainer">
          <!-- 实时日志将在这里显示 -->
        </div>
      </div>

      <!-- 搜索日志选项卡 -->
      <div id="search" class="tab-content">
        <div class="controls">
          <div class="control-group">
            <label>搜索:</label>
            <input
              type="text"
              id="searchQuery"
              class="search-input"
              placeholder="输入搜索关键词..."
            />
            <button onclick="searchLogs()">搜索</button>
          </div>
          <div class="control-group">
            <label>级别:</label>
            <select id="searchLevel">
              <option value="">全部级别</option>
              <option value="error">错误</option>
              <option value="warn">警告</option>
              <option value="info">信息</option>
              <option value="debug">调试</option>
            </select>
            <label>开始日期:</label>
            <input type="date" id="startDate" />
            <label>结束日期:</label>
            <input type="date" id="endDate" />
            <label>限制:</label>
            <input
              type="number"
              id="searchLimit"
              value="100"
              min="1"
              max="1000"
              style="width: 80px"
            />
          </div>
        </div>
        <div class="log-container" id="searchResults">
          <!-- 搜索结果将在这里显示 -->
        </div>
      </div>

      <!-- 文件管理选项卡 -->
      <div id="files" class="tab-content">
        <div class="controls">
          <div class="control-group">
            <button onclick="loadLogFiles()">刷新文件列表</button>
            <button class="danger" onclick="cleanupLogs()">清理旧日志</button>
          </div>
        </div>
        <div id="filesList">
          <!-- 文件列表将在这里显示 -->
        </div>
      </div>

      <!-- 系统监控选项卡 -->
      <div id="monitor" class="tab-content">
        <div id="monitorData">
          <!-- 监控数据将在这里显示 -->
        </div>
      </div>
    </div>

    <script>
      let eventSource = null;
      let isConnected = false;

      // 初始化
      document.addEventListener('DOMContentLoaded', function () {
        loadStats();
        loadMonitorData();
        connectLogStream();

        // 每30秒刷新统计信息
        setInterval(loadStats, 30000);
        setInterval(loadMonitorData, 10000);
      });

      // 切换选项卡
      function switchTab(tabName) {
        // 隐藏所有选项卡内容
        document.querySelectorAll('.tab-content').forEach((content) => {
          content.classList.remove('active');
        });

        // 移除所有选项卡的活动状态
        document.querySelectorAll('.tab').forEach((tab) => {
          tab.classList.remove('active');
        });

        // 显示选中的选项卡
        document.getElementById(tabName).classList.add('active');
        event.target.classList.add('active');
      }

      // 加载统计信息
      async function loadStats() {
        try {
          const response = await fetch('/api/logs/stats');
          const data = await response.json();

          if (data.code === 0) {
            displayStats(data.data);
          }
        } catch (error) {
          console.error('Failed to load stats:', error);
        }
      }

      // 显示统计信息
      function displayStats(stats) {
        const container = document.getElementById('statsContainer');
        container.innerHTML = `
                <div class="stat-card">
                    <h3>总文件数</h3>
                    <p style="font-size: 24px; font-weight: bold;">${
                      stats.fileCount
                    }</p>
                </div>
                <div class="stat-card warning">
                    <h3>总大小</h3>
                    <p style="font-size: 24px; font-weight: bold;">${formatBytes(
                      stats.totalSize
                    )}</p>
                </div>
                <div class="stat-card">
                    <h3>最新文件</h3>
                    <p>${
                      stats.files.length > 0 ? stats.files[0].name : '无'
                    }</p>
                </div>
            `;
      }

      // 连接日志流
      function connectLogStream() {
        if (eventSource) {
          eventSource.close();
        }

        const level = document.getElementById('logLevel').value;
        const url =
          level === 'all'
            ? '/api/logs/stream'
            : `/api/logs/stream?level=${level}`;

        eventSource = new EventSource(url);

        eventSource.onopen = function () {
          isConnected = true;
          updateConnectionStatus();
        };

        eventSource.onmessage = function (event) {
          const logEntry = JSON.parse(event.data);
          if (logEntry.type === 'connected') {
            return;
          }
          addLogEntry(logEntry);
        };

        eventSource.onerror = function () {
          isConnected = false;
          updateConnectionStatus();
        };
      }

      // 断开日志流
      function disconnectLogStream() {
        if (eventSource) {
          eventSource.close();
          eventSource = null;
        }
        isConnected = false;
        updateConnectionStatus();
      }

      // 更新连接状态
      function updateConnectionStatus() {
        const indicator = document.getElementById('connectionStatus');
        const text = document.getElementById('connectionText');

        if (isConnected) {
          indicator.className = 'status-indicator connected';
          text.textContent = '已连接';
        } else {
          indicator.className = 'status-indicator disconnected';
          text.textContent = '未连接';
        }
      }

      // 添加日志条目
      function addLogEntry(logEntry) {
        const container = document.getElementById('logContainer');
        const entry = document.createElement('div');
        entry.className = `log-entry ${logEntry.level.toLowerCase()}`;

        const metaHtml =
          Object.keys(logEntry.meta).length > 0
            ? `<div class="log-meta">${JSON.stringify(
                logEntry.meta,
                null,
                2
              )}</div>`
            : '';

        entry.innerHTML = `
                <div class="log-header">
                    <span class="log-level ${logEntry.level}">${
          logEntry.level
        }</span>
                    <span class="log-timestamp">${new Date(
                      logEntry.timestamp
                    ).toLocaleString()}</span>
                </div>
                <div class="log-message">${logEntry.message}</div>
                ${metaHtml}
            `;

        container.insertBefore(entry, container.firstChild);

        // 限制显示的日志数量
        const maxEntries = 100;
        while (container.children.length > maxEntries) {
          container.removeChild(container.lastChild);
        }
      }

      // 清空日志显示
      function clearLogs() {
        document.getElementById('logContainer').innerHTML = '';
      }

      // 搜索日志
      async function searchLogs() {
        const query = document.getElementById('searchQuery').value;
        const level = document.getElementById('searchLevel').value;
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        const limit = document.getElementById('searchLimit').value;

        if (!query.trim()) {
          alert('请输入搜索关键词');
          return;
        }

        try {
          const response = await fetch('/api/logs/search', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              query,
              level,
              startDate,
              endDate,
              limit: parseInt(limit),
            }),
          });

          const data = await response.json();

          if (data.code === 0) {
            displaySearchResults(data.data.results);
          } else {
            alert('搜索失败: ' + data.message);
          }
        } catch (error) {
          console.error('Search failed:', error);
          alert('搜索失败');
        }
      }

      // 显示搜索结果
      function displaySearchResults(results) {
        const container = document.getElementById('searchResults');
        container.innerHTML = '';

        results.forEach((logEntry) => {
          const entry = document.createElement('div');
          entry.className = `log-entry ${logEntry.level.toLowerCase()}`;

          const metaHtml =
            Object.keys(logEntry.meta || {}).length > 0
              ? `<div class="log-meta">${JSON.stringify(
                  logEntry.meta,
                  null,
                  2
                )}</div>`
              : '';

          entry.innerHTML = `
                    <div class="log-header">
                        <span class="log-level ${logEntry.level}">${
            logEntry.level
          }</span>
                        <span class="log-timestamp">${new Date(
                          logEntry.timestamp
                        ).toLocaleString()}</span>
                    </div>
                    <div class="log-message">${logEntry.message}</div>
                    ${metaHtml}
                `;

          container.appendChild(entry);
        });
      }

      // 加载日志文件列表
      async function loadLogFiles() {
        try {
          const response = await fetch('/api/logs/files');
          const data = await response.json();

          if (data.code === 0) {
            displayFilesList(data.data.files);
          }
        } catch (error) {
          console.error('Failed to load files:', error);
        }
      }

      // 显示文件列表
      function displayFilesList(files) {
        const container = document.getElementById('filesList');

        if (files.length === 0) {
          container.innerHTML = '<p>没有找到日志文件</p>';
          return;
        }

        let html = '<table style="width: 100%; border-collapse: collapse;">';
        html +=
          '<tr style="background: #2d2d30;"><th style="padding: 10px; text-align: left;">文件名</th><th style="padding: 10px; text-align: left;">大小</th><th style="padding: 10px; text-align: left;">修改时间</th></tr>';

        files.forEach((file) => {
          html += `
                    <tr style="border-bottom: 1px solid #3c3c3c;">
                        <td style="padding: 8px;">${file.name}</td>
                        <td style="padding: 8px;">${formatBytes(file.size)}</td>
                        <td style="padding: 8px;">${new Date(
                          file.modified
                        ).toLocaleString()}</td>
                    </tr>
                `;
        });

        html += '</table>';
        container.innerHTML = html;
      }

      // 清理旧日志
      async function cleanupLogs() {
        if (!confirm('确定要清理30天前的旧日志文件吗？')) {
          return;
        }

        try {
          const response = await fetch('/api/logs/cleanup', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ daysToKeep: 30 }),
          });

          const data = await response.json();

          if (data.code === 0) {
            alert('日志清理任务已启动');
            loadLogFiles();
          } else {
            alert('清理失败: ' + data.message);
          }
        } catch (error) {
          console.error('Cleanup failed:', error);
          alert('清理失败');
        }
      }

      // 加载监控数据
      async function loadMonitorData() {
        try {
          const response = await fetch('/api/logs/monitor');
          const data = await response.json();

          if (data.code === 0) {
            displayMonitorData(data.data);
          }
        } catch (error) {
          console.error('Failed to load monitor data:', error);
        }
      }

      // 显示监控数据
      function displayMonitorData(data) {
        const container = document.getElementById('monitorData');
        container.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div class="stat-card">
                        <h3>系统信息</h3>
                        <p><strong>进程ID:</strong> ${data.pid}</p>
                        <p><strong>运行时间:</strong> ${formatUptime(
                          data.uptime
                        )}</p>
                        <p><strong>Node版本:</strong> ${data.version}</p>
                        <p><strong>平台:</strong> ${data.platform}</p>
                    </div>
                    <div class="stat-card warning">
                        <h3>内存使用</h3>
                        <p><strong>RSS:</strong> ${data.memory.rss}MB</p>
                        <p><strong>堆已用:</strong> ${
                          data.memory.heapUsed
                        }MB</p>
                        <p><strong>堆总计:</strong> ${
                          data.memory.heapTotal
                        }MB</p>
                        <p><strong>外部:</strong> ${data.memory.external}MB</p>
                    </div>
                    <div class="stat-card">
                        <h3>CPU使用</h3>
                        <p><strong>用户时间:</strong> ${Math.round(
                          data.cpu.user / 1000
                        )}ms</p>
                        <p><strong>系统时间:</strong> ${Math.round(
                          data.cpu.system / 1000
                        )}ms</p>
                    </div>
                </div>
            `;
      }

      // 工具函数
      function formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      }

      function formatUptime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        return `${hours}h ${minutes}m ${secs}s`;
      }
    </script>
  </body>
</html>
