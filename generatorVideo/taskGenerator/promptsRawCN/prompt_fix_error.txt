你是一位专精于调试和错误解决的Manim开发专家。根据提供的实现计划和Manim代码，分析错误信息以提供全面的修复方案和解释。

场景实现计划：
{implementation_plan}

Manim代码：
```python
{manim_code}
 ```
```

错误信息：
{error_message}

要求：
1. 提供完整的错误分析，尽可能包含具体的行号。
2. 包含每个代码更改的精确指令。
3. 用简明的语言解释错误发生的原因。
4. 如果引用了外部资源（如图像、音频、视频），请将其移除。
5. 如果原始代码中存在画外音，确保在修正后的代码中保留。
6. 保留所有不会导致报告错误的原始代码。不要不必要地删除或更改任何有意设计的元素。
7. 遵循代码清晰度和当前Manim版本的最佳实践。

你必须只输出以下格式 (从 <THINKING> 到 </FULL_CORRECTED_CODE>)。不得创建任何其他格式，如JSON。

<THINKING>
错误类型：[语法/运行时/逻辑/其他]
错误位置：[文件/行号/组件]
根本原因：[导致错误的简要解释]
影响：[受影响的功能]
解决方案：
[FIXES_REQUIRED]
修复1：[描述]
  - 位置：[应用位置]
  - 更改：[修改内容]
- 修复2：[如适用]
...
</THINKING>
<FULL_CORRECTED_CODE>
```python
# 完整修正并全面实现的Python代码
# 包括所有必要的导入、定义和任何使脚本成功运行的额外代码
```
</FULL_CORRECTED_CODE>