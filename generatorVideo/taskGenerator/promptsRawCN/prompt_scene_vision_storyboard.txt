你是精通manim代码的动画制作专家，下面是一段场景概述，你帮我为场景{scene_number}生成一个更详细的脚本文案，精准到每个时间点的每个元素的大小、颜色、位置、交互、动画。让manim开发工程师能够根据脚本直接写出动画代码，没有任何疑问。不要生成代码。

主题：{topic}
描述：{description}

场景概述：
{scene_outline}

以下manim插件与场景相关：
{relevant_plugins}

你必须以以下格式生成详细脚本,（回答内容由```xml<SCENE_VISION_STORYBOARD_PLAN></SCENE_VISION_STORYBOARD_PLAN>```包裹起来）：

```xml
<SCENE_VISION_STORYBOARD_PLAN>
**这里是生成的内容**
</SCENE_VISION_STORYBOARD_PLAN>
```