// 解析场景大纲字符串为对象数组
function parseSceneOutline(sceneString) {
  // 确保有内容可解析
  if (!sceneString) return null;

  const scenes = [];

  // 使用正则表达式匹配每个场景块
  const sceneRegex = /<SCENE_\d+>([\s\S]*?)<\/SCENE_\d+>/g;
  let sceneMatch;

  while ((sceneMatch = sceneRegex.exec(sceneString)) !== null) {
    const sceneContent = sceneMatch[1].trim();
    scenes.push(sceneContent);
  }

  return scenes.length > 0 ? scenes : null;
}
function parseSceneOutlineSingle(sceneContent) {
  // 首先去除每行开头的空白字符
  const normalizedContent = sceneContent.replace(/^\s+/gm, "");

  // 提取场景标题
  const titleMatch = /场景标题:\s*(.*?)(?:\r?\n|\r|$)/.exec(normalizedContent);
  const title = titleMatch ? titleMatch[1].trim() : "未命名场景";

  // 提取场景目的（可选）
  const purposeMatch = /场景目的:\s*(.*?)(?:\r?\n|\r|$)/.exec(
    normalizedContent
  );
  const purpose = purposeMatch ? purposeMatch[1].trim() : "";

  // 提取场景描述
  const descriptionMatch = /场景描述:\s*([\s\S]*?)(?=场景布局:|$)/.exec(
    normalizedContent
  );
  const description = descriptionMatch ? descriptionMatch[1].trim() : "无描述";

  // 提取场景布局（可选）
  const layoutMatch = /场景布局:\s*([\s\S]*?)(?:\r?\n\r?\n|\r\r|$)/.exec(
    normalizedContent
  );
  const layout = layoutMatch ? layoutMatch[1].trim() : "";

  return {
    title,
    description,
    purpose,
    layout,
  };
}

function parseStoryboard(storyboardString) {
  if (!storyboardString) return null;

  // // 分离 SCENE_VISION 和 STORYBOARD 部分
  // const sceneVisionMatch = storyboardString.match(
  //   /\[SCENE_VISION\]([\s\S]*?)\[STORYBOARD\]/
  // );
  // const storyboardContentMatch = storyboardString.match(
  //   /\[STORYBOARD\]([\s\S]*)/
  // );

  // if (!sceneVisionMatch || !storyboardContentMatch) return null;

  // const sceneVision = sceneVisionMatch[1].trim();
  const storyboardContent = storyboardString;

  // 返回简化的结果
  return {
    // sceneVision,
    content: storyboardContent,
  };
}
function parseTLContent(content) {
  return content;
}
function parseAnimationContent(content) {
  return content;
}
module.exports = {
  parseSceneOutline,
  parseSceneOutlineSingle,
  parseStoryboard,
  parseTLContent,
  parseAnimationContent,
};
