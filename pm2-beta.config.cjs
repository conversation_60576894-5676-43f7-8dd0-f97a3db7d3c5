module.exports = {
  apps: [
    {
      name: 'think-chat',
      script: './index.js', // 启动脚本的路径
      instances: 'max', // 实例数量，根据 CPU 核心数动态调整
      exec_mode: 'cluster', // 使用集群模式
      watch: false, // 是否启用文件监视
      max_memory_restart: '500M', // 内存占用超过 500M 时重启
      env: {
        NODE_ENV: 'beta',
      },
      log_date_format: 'YYYY-MM-DD HH:mm Z', // 日志日期格式
      error_file: './logs/pm2-error.log', // 错误日志文件路径
      out_file: './logs/pm2-out.log', // 输出日志文件路径
      merge_logs: true, // 合并日志
      min_uptime: '60s', // 最小正常运行时间（例如 60s）
      max_restarts: 10, // 最大重启次数
    }
  ]
};
