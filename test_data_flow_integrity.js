const { parseSceneOutline } = require('./generatorVideo/utils/parseText.js');

// 测试数据流完整性
function testDataFlowIntegrity() {
  console.log('=== 数据流完整性测试 ===\n');

  // 1. 模拟大模型输出格式
  const mockLLMOutput = `
<SCENE_1>
**场景标题**：问题理解
**核心内容**：展示蜡烛燃烧问题
**教学目标**：理解题目条件
**视觉元素**：两支蜡烛、时间轴
**动画流程**：显示题目，绘制蜡烛
</SCENE_1>

<SCENE_2>
**场景标题**：建立方程
**核心内容**：根据条件建立方程
**教学目标**：培养建模能力
**视觉元素**：方程式、变量
**动画流程**：逐步建立方程
</SCENE_2>

<SCENE_3>
**场景标题**：求解过程
**核心内容**：解方程得答案
**教学目标**：掌握解题方法
**视觉元素**：计算步骤
**动画流程**：逐步计算
</SCENE_3>
  `;

  console.log('1. 大模型输出 → 解析提取');
  console.log(`   输入长度: ${mockLLMOutput.length} 字符`);

  const sceneStrList = parseSceneOutline(mockLLMOutput);
  console.log(`   解析结果: ${sceneStrList.length} 个场景`);

  if (sceneStrList.length !== 3) {
    console.log('   ❌ 解析场景数量不正确');
    return;
  }

  // 2. 模拟数据库存储格式
  console.log('\n2. 解析结果 → 数据库存储');
  const dbRecords = sceneStrList.map((content, index) => ({
    session_id: 'test-session-123',
    content: content,
    scene_num: String(index + 1),
    parent_scene_num: '0',
  }));

  console.log('   数据库记录格式:');
  dbRecords.forEach((record, i) => {
    console.log(
      `   场景${i + 1}: scene_num=${record.scene_num}, content长度=${
        record.content.length
      }`
    );
  });

  // 3. 模拟下一流程读取
  console.log('\n3. 数据库读取 → 下一流程使用');

  // 模拟查询单个场景（故事板生成时）
  const sceneNum = 1;
  const selectedScene = dbRecords.find((r) => r.scene_num === String(sceneNum));
  console.log(`   查询场景${sceneNum}: ${selectedScene ? '找到' : '未找到'}`);

  let hasTitle = false,
    hasContent = false,
    hasGoal = false;
  if (selectedScene) {
    console.log(
      `   场景内容预览: ${selectedScene.content.substring(0, 50)}...`
    );

    // 检查内容是否包含必要字段
    hasTitle = selectedScene.content.includes('**场景标题**');
    hasContent = selectedScene.content.includes('**核心内容**');
    hasGoal = selectedScene.content.includes('**教学目标**');

    console.log(
      `   内容完整性: 标题${hasTitle ? '✅' : '❌'} 内容${
        hasContent ? '✅' : '❌'
      } 目标${hasGoal ? '✅' : '❌'}`
    );
  }

  // 模拟查询所有场景（构建完整大纲）
  const fullSceneOutline = dbRecords
    .map((record) => record.content)
    .join('\n\n');

  console.log(`\n   完整场景大纲长度: ${fullSceneOutline.length} 字符`);
  console.log(
    `   包含所有场景: ${
      fullSceneOutline.includes('问题理解') &&
      fullSceneOutline.includes('建立方程') &&
      fullSceneOutline.includes('求解过程')
        ? '✅'
        : '❌'
    }`
  );

  // 4. 验证数据流完整性
  console.log('\n4. 数据流完整性验证');

  const checks = [
    { name: '大模型格式正确', pass: mockLLMOutput.includes('<SCENE_1>') },
    { name: '解析提取成功', pass: sceneStrList.length === 3 },
    {
      name: '数据库格式正确',
      pass: dbRecords.every((r) => r.content && r.scene_num),
    },
    {
      name: '单场景查询可用',
      pass: selectedScene && selectedScene.content.length > 0,
    },
    { name: '完整大纲构建可用', pass: fullSceneOutline.length > 0 },
    {
      name: '内容字段完整',
      pass: selectedScene && hasTitle && hasContent && hasGoal,
    },
  ];

  checks.forEach((check) => {
    console.log(`   ${check.pass ? '✅' : '❌'} ${check.name}`);
  });

  const passedChecks = checks.filter((c) => c.pass).length;
  console.log(
    `\n   总体评估: ${passedChecks}/${checks.length} ${
      passedChecks === checks.length ? '✅ 数据流完整' : '❌ 存在问题'
    }`
  );

  console.log('\n=== 测试完成 ===');
}

// 运行测试
if (require.main === module) {
  testDataFlowIntegrity();
}

module.exports = {
  testDataFlowIntegrity,
};
