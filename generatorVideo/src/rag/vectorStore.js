const fs = require("fs");
const path = require("path");
const { TextLoader } = require("langchain/document_loaders/fs/text");
const { RecursiveCharacterTextSplitter } = require("@langchain/textsplitters");

// 移除 Tiktoken 导入
// const { Tiktoken } = require('@dqbd/tiktoken')
// const { LangfuseClient } = require('langfuse')
// const { tqdm } = require('tqdm')
// 替换 Chroma 导入为 FAISS
const { FaissStore } = require("@langchain/community/vectorstores/faiss");
// 替换 OpenAIEmbeddings 导入
// const { OpenAIEmbeddings } = require('@langchain/openai')
const { CustomEmbeddings } = require("../utils/customEmbeddings");

class RAGVectorStore {
  /**
   * 创建一个用于RAG（检索增强生成）的向量存储管理类
   *
   * @param {Object} options - 配置选项
   * @param {string} options.faissDbPath - FAISS存储目录路径
   * @param {string} options.manimDocsPath - Manim文档文件路径
   * @param {string} options.embeddingApiUrl - 嵌入API的URL
   * @param {string} options.appId - 公司API的应用ID
   * @param {string} options.appKey - 公司API的应用密钥
   * @param {string} options.traceId - 用于日志记录的跟踪标识符
   * @param {string} options.sessionId - 会话标识符
   * @param {boolean} options.useLangfuse - 是否使用Langfuse日志记录
   */
  constructor({
    faissDbPath = "faiss_db",
    manimDocsPath = "rag/manim_docs",
    embeddingModel = "text-embedding-v3",
    appId = process.env.TAL_MLOPS_APP_ID,
    appKey = process.env.TAL_MLOPS_APP_KEY,
    traceId = null,
    sessionId = null,
    useLangfuse = true,
  } = {}) {
    this.embeddingModel = embeddingModel;
    this.faissDbPath = faissDbPath;
    this.manimDocsPath = manimDocsPath;
    this.appKey = appKey;
    this.traceId = traceId;
    this.sessionId = sessionId;
    this.useLangfuse = useLangfuse;
    // 移除 Tiktoken 初始化
    // this.enc = new Tiktoken('gpt-4')
    this.pluginStores = {};

    // 初始化时加载或创建向量存储
    this.vectorStore = this.loadOrCreateVectorStore();
  }

  /**
   * 获取嵌入函数
   * @returns {Object} 嵌入函数对象
   */
  getEmbeddingFunction() {
    // 使用自定义嵌入服务替代OpenAI
    return new CustomEmbeddings({
      appKey: this.appKey,
      embeddingModel: this.embeddingModel,
    });
  }

  /**
   * 加载现有或创建新的FAISS向量存储
   * @returns {Promise<Object>} 核心Manim向量存储实例
   */
  async loadOrCreateVectorStore() {
    console.log("Entering loadOrCreateVectorStore with traceId:", this.traceId);
    const corePath = path.join(this.faissDbPath, "manim_core");

    // 确保目录存在
    if (!fs.existsSync(this.faissDbPath)) {
      fs.mkdirSync(this.faissDbPath, { recursive: true });
    }

    if (!fs.existsSync(corePath)) {
      fs.mkdirSync(corePath, { recursive: true });
    }

    // 加载或创建核心向量存储
    if (fs.existsSync(path.join(corePath, "faiss.index"))) {
      console.log("Loading existing core FAISS...");
      this.coreVectorStore = await FaissStore.load(
        corePath,
        this.getEmbeddingFunction()
      );
    } else {
      console.log("Creating new core FAISS...");
      this.coreVectorStore = await this.createCoreStore();
    }

    // 处理插件文档
    const plugin_docs_path = path.join(this.manimDocsPath, "plugin_docs");
    console.log(`Plugin docs path: ${plugin_docs_path}`);

    if (fs.existsSync(plugin_docs_path)) {
      const plugin_dirs = fs.readdirSync(plugin_docs_path);

      for (const plugin_name of plugin_dirs) {
        const plugin_store_path = path.join(
          this.faissDbPath,
          `manim_plugin_${plugin_name}`
        );

        if (!fs.existsSync(plugin_store_path)) {
          fs.mkdirSync(plugin_store_path, { recursive: true });
        }

        if (fs.existsSync(path.join(plugin_store_path, "faiss.index"))) {
          console.log(`Loading existing plugin store: ${plugin_name}`);
          this.pluginStores[plugin_name] = await FaissStore.load(
            plugin_store_path,
            this.getEmbeddingFunction()
          );
        } else {
          console.log(`Creating new plugin store: ${plugin_name}`);
          const plugin_path = path.join(plugin_docs_path, plugin_name);

          if (fs.statSync(plugin_path).isDirectory()) {
            // 创建空的FAISS存储
            const plugin_docs = await this.processDocumentationFolder(
              plugin_path
            );

            if (plugin_docs && plugin_docs.length > 0) {
              const plugin_store = await FaissStore.fromDocuments(
                plugin_docs,
                this.getEmbeddingFunction()
              );

              // 保存到指定路径
              await plugin_store.save(plugin_store_path);

              this.pluginStores[plugin_name] = plugin_store;
            }
          }
        }
      }
    }

    return this.coreVectorStore; // 为了向后兼容返回核心存储
  }

  /**
   * 创建主要的FAISS向量存储用于Manim核心文档
   * @returns {Promise<Object>} 初始化并填充的核心向量存储
   */
  async createCoreStore() {
    // 处理manim核心文档
    const core_docs = await this.processDocumentationFolder(
      path.join(this.manimDocsPath, "manim_core")
    );

    let coreVectorStore = null;

    if (core_docs && core_docs.length > 0) {
      coreVectorStore = await FaissStore.fromDocuments(
        core_docs,
        this.getEmbeddingFunction()
      );

      // 保存到指定路径
      await coreVectorStore.save(path.join(this.faissDbPath, "manim_core"));
    } else {
      // 创建空的存储
      coreVectorStore = await FaissStore.fromDocuments(
        [],
        this.getEmbeddingFunction()
      );
      await coreVectorStore.save(path.join(this.faissDbPath, "manim_core"));
    }

    return coreVectorStore;
  }

  /**
   * 将文件夹中的文档文件处理为LangChain文档
   * @param {string} folder_path - 包含文档文件的文件夹路径
   * @returns {Promise<Array>} 处理后的LangChain文档列表
   */
  async processDocumentationFolder(folder_path) {
    const all_docs = [];

    // 递归获取所有文件
    const getFiles = (dir) => {
      const files = fs.readdirSync(dir);
      let fileList = [];

      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
          fileList = fileList.concat(getFiles(filePath));
        } else if (file.endsWith(".md") || file.endsWith(".py")) {
          fileList.push(filePath);
        }
      }

      return fileList;
    };

    const files = getFiles(folder_path);

    // 加载所有文件
    for (const file_path of files) {
      try {
        const loader = new TextLoader(file_path);
        const documents = await loader.load();

        for (const doc of documents) {
          doc.metadata.source = file_path;
        }

        all_docs.push(...documents);
      } catch (e) {
        console.error(`Error loading file ${file_path}: ${e}`);
      }
    }

    if (all_docs.length === 0) {
      console.log(`No markdown or python files found in ${folder_path}`);
      return [];
    }

    // 使用适当的分割器分割文档
    const split_docs = [];
    const markdown_splitter =
      RecursiveCharacterTextSplitter.fromLanguage("markdown");
    const python_splitter =
      RecursiveCharacterTextSplitter.fromLanguage("python");

    for (const doc of all_docs) {
      if (doc.metadata.source.endsWith(".md")) {
        const temp_docs = await markdown_splitter.splitDocuments([doc]);
        for (const temp_doc of temp_docs) {
          temp_doc.pageContent = `Source: ${doc.metadata.source}\n\n${temp_doc.pageContent}`;
        }
        split_docs.push(...temp_docs);
      } else if (doc.metadata.source.endsWith(".py")) {
        const temp_docs = await python_splitter.splitDocuments([doc]);
        for (const temp_doc of temp_docs) {
          temp_doc.pageContent = `Source: ${doc.metadata.source}\n\n${temp_doc.pageContent}`;
        }
        split_docs.push(...temp_docs);
      }
    }

    return split_docs;
  }

  /**
   * 批量添加文档到向量存储，并进行速率限制
   * @param {Object} vector_store - 要添加文档的向量存储
   * @param {Array} documents - 要添加的文档列表
   * @param {string} store_name - 用于日志记录的存储名称
   */
  async addDocumentsToStore(vector_store, documents, store_name) {
    console.log(`Adding documents to ${store_name} store`);

    // 简化的文档长度统计，使用字符长度替代token计数
    const char_lengths = documents.map((doc) => doc.pageContent.length);
    const min = Math.min(...char_lengths);
    const max = Math.max(...char_lengths);
    const mean = char_lengths.reduce((a, b) => a + b, 0) / char_lengths.length;
    const median = char_lengths.sort((a, b) => a - b)[
      Math.floor(char_lengths.length / 2)
    ];

    console.log(
      `Document length statistics for ${store_name}: ` +
        `Min: ${min}, Max: ${max}, ` +
        `Mean: ${mean.toFixed(1)}, ` +
        `Median: ${median}`
    );

    const batch_size = 10;
    let request_count = 0;

    for (let i = 0; i < documents.length; i += batch_size) {
      const batch_docs = documents.slice(i, i + batch_size);

      // FAISS 不需要 IDs，直接添加文档
      await vector_store.addDocuments(batch_docs);

      request_count++;
      if (request_count % 100 === 0) {
        await new Promise((resolve) => setTimeout(resolve, 60000)); // 每100个请求暂停60秒
      }

      // 显示进度
      console.log(
        `Processing ${store_name} batches: ${Math.min(
          i + batch_size,
          documents.length
        )}/${documents.length}`
      );
    }

    // 保存更新后的向量存储
    if (store_name === "manim_core") {
      await vector_store.save(path.join(this.faissDbPath, "manim_core"));
    } else {
      await vector_store.save(
        path.join(this.faissDbPath, `manim_plugin_${store_name}`)
      );
    }
  }

  /**
   * 根据提供的查询查找相关文档
   * @param {Array} queries - 包含'type'和'query'键的查询字典列表
   * @param {number} k - 每个查询返回的结果数量
   * @param {string} traceId - 用于日志记录的跟踪标识符
   * @param {string} topic - 用于日志记录的主题名称
   * @param {number} scene_number - 用于日志记录的场景编号
   * @returns {string} 包含相关文档片段的格式化字符串
   */
  async findRelevantDocs({
    queries,
    k = 5,
    topic = null,
    scene_number = null,
  }) {
    const manim_core_formatted_results = [];
    const manim_plugin_formatted_results = [];

    // 如果启用，创建Langfuse跨度
    let span = null;
    if (this.useLangfuse) {
      // const langfuse = new LangfuseClient()
      // span = langfuse.span({
      //   traceId: traceId, // 使用传递的trace_id
      //   name: `RAG search for ${topic} - scene ${scene_number}`,
      //   metadata: {
      //     topic: topic,
      //     scene_number: scene_number,
      //     sessionId: this.sessionId
      //   }
      // })
    }

    // 按类型分离查询
    const manim_core_queries = queries.filter(
      (query) => query.type === "manim-core"
    );
    const manim_plugin_queries = queries.filter(
      (query) =>
        query.type !== "manim-core" &&
        Object.keys(this.pluginStores).includes(query.type)
    );

    if (
      queries.filter((q) => q.type !== "manim-core").length !==
      manim_plugin_queries.length
    ) {
      console.log(
        "Warning: Some plugin queries were skipped because their types weren't found in available plugin stores"
      );
    }

    // 在核心manim文档中搜索
    for (const query of manim_core_queries) {
      const query_text = query.query;

      // FAISS 使用 similaritySearch 而不是 similaritySearchWithScore
      const manim_core_results =
        await this.coreVectorStore.similaritySearchWithScore(query_text, k);

      for (const result of manim_core_results) {
        manim_core_formatted_results.push({
          query: query_text,
          source: result[0].metadata.source,
          content: result[0].pageContent,
          score: result[1],
        });
      }
    }
    console.log(111111, manim_core_formatted_results);
    // 在相关插件文档中搜索
    for (const query of manim_plugin_queries) {
      const plugin_name = query.type;
      const query_text = query.query;

      if (this.pluginStores[plugin_name]) {
        if (span) {
          this.pluginStores[plugin_name]._embedding.parent_observation_id =
            span.id;
        }

        const plugin_results = await this.pluginStores[
          plugin_name
        ].similaritySearchWithScore(query_text, k, { score_threshold: 0.5 });

        for (const result of plugin_results) {
          manim_plugin_formatted_results.push({
            query: query_text,
            source: result[0].metadata.source,
            content: result[0].pageContent,
            score: result[1],
          });
        }
      }
    }
    console.log(111111222, manim_plugin_formatted_results);

    console.log(
      `Number of results before removing duplicates: ${
        manim_core_formatted_results.length +
        manim_plugin_formatted_results.length
      }`
    );

    // 根据内容删除重复项
    const manim_core_unique_results = [];
    const manim_plugin_unique_results = [];
    const seen = new Set();

    for (const item of manim_core_formatted_results) {
      const key = item.content;
      if (!seen.has(key)) {
        manim_core_unique_results.push(item);
        seen.add(key);
      }
    }

    for (const item of manim_plugin_formatted_results) {
      const key = item.content;
      if (!seen.has(key)) {
        manim_plugin_unique_results.push(item);
        seen.add(key);
      }
    }

    console.log(
      `Number of results after removing duplicates: ${
        manim_core_unique_results.length + manim_plugin_unique_results.length
      }`
    );
    // 简化的文档长度统计，使用文档数量替代token计数
    const total_docs =
      manim_core_unique_results.length + manim_plugin_unique_results.length;
    console.log(`Total documents for the RAG search: ${total_docs}`);

    // 使用去重结果更新Langfuse
    if (this.useLangfuse && span) {
      const filtered_results_markdown = JSON.stringify(
        [...manim_core_unique_results, ...manim_plugin_unique_results],
        null,
        2
      );
      span.update({
        output: filtered_results_markdown,
        metadata: {
          total_docs: total_docs,
          initial_results_count:
            manim_core_formatted_results.length +
            manim_plugin_formatted_results.length,
          filtered_results_count:
            manim_core_unique_results.length +
            manim_plugin_unique_results.length,
        },
      });
    }

    // 格式化结果
    const manim_core_results =
      "Please refer to the following Manim core documentation that may be helpful for the code generation:\n\n" +
      manim_core_unique_results
        .map(
          (res) =>
            `Content:\n\`\`\`\`text\n${res.content}\n\`\`\`\`\nScore: ${res.score}`
        )
        .join("\n\n");

    const manim_plugin_results =
      "Please refer to the following Manim plugin documentation that may be helpful for the code generation:\n\n" +
      manim_plugin_unique_results
        .map(
          (res) =>
            `Content:\n\`\`\`\`text\n${res.content}\n\`\`\`\`\nScore: ${res.score}`
        )
        .join("\n\n");

    return manim_core_results + "\n\n" + manim_plugin_results;
  }
}

// 修改导出方式
module.exports = { RAGVectorStore };
